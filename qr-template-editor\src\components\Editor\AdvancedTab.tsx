import React from 'react';
import {
  Card,
  CardContent,
  TextField,
  Typography,
  Box,
  Alert,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import { Settings, Code, Download, QrCode, ChevronDown } from 'lucide-react';
import type { BusinessData } from '../../types/index';

interface AdvancedTabProps {
  businessData: BusinessData;
  onUpdate: (field: keyof BusinessData, value: any) => void;
}

const AdvancedTab: React.FC<AdvancedTabProps> = ({ businessData, onUpdate }) => {
  const [showRawData, setShowRawData] = React.useState(false);
  const [qrCodeSettings, setQrCodeSettings] = React.useState({
    size: 256,
    errorCorrection: 'M',
    includeMargin: true,
    darkColor: '#000000',
    lightColor: '#FFFFFF'
  });

  const handleExportData = () => {
    const dataStr = JSON.stringify(businessData, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${businessData.businessName.toLowerCase().replace(/\s+/g, '-')}-data.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedData = JSON.parse(e.target?.result as string);
          // Validate and update data
          Object.keys(importedData).forEach(key => {
            if (key in businessData) {
              onUpdate(key as keyof BusinessData, importedData[key]);
            }
          });
          alert('Data imported successfully!');
        } catch (error) {
          alert('Error importing data. Please check the file format.');
        }
      };
      reader.readAsText(file);
    }
  };

  const resetAllData = () => {
    if (confirm('Are you sure you want to reset all data? This action cannot be undone.')) {
      // Reset to default values
      const defaultData = {
        pageTitle: '',
        businessName: '',
        logoUrl: '',
        faviconUrl: '',
        phone: '',
        email: '',
        address: '',
        hours: '',
        websiteUrl: '',
        bookingUrl: '',
        facebookUrl: '',
        instagramUrl: '',
        googleMapsUrl: '',
        template: 'modern' as const,
        primaryColor: '#3B82F6',
        secondaryColor: '#1E40AF',
        customCSS: '',
        galleryImages: [],
        services: [],
        offers: []
      };
      
      Object.keys(defaultData).forEach(key => {
        onUpdate(key as keyof BusinessData, defaultData[key as keyof typeof defaultData]);
      });
    }
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              bgcolor: 'grey.700',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            <Settings size={20} color="white" />
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Advanced Settings
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Advanced configuration and data management
            </Typography>
          </Box>
        </Box>

        <Alert severity="warning" sx={{ mb: 3 }}>
          These are advanced settings. Please be careful when making changes.
        </Alert>

        {/* Custom CSS Editor */}
        <Accordion sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ChevronDown />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Code size={16} />
              <Typography variant="subtitle1">Custom CSS</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <TextField
              label="Custom CSS"
              value={businessData.customCSS || ''}
              onChange={(e) => onUpdate('customCSS', e.target.value)}
              fullWidth
              multiline
              rows={10}
              placeholder="/* Add your custom CSS here */&#10;.business-name {&#10;  font-family: 'Arial', sans-serif;&#10;  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);&#10;}"
              sx={{
                '& .MuiInputBase-input': {
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                },
              }}
              helperText="Add custom CSS to override default styles. Changes will be applied to the generated HTML."
            />
          </AccordionDetails>
        </Accordion>

        {/* QR Code Settings */}
        <Accordion sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ChevronDown />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <QrCode size={16} />
              <Typography variant="subtitle1">QR Code Settings</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="QR Code Size"
                type="number"
                value={qrCodeSettings.size}
                onChange={(e) => setQrCodeSettings(prev => ({ ...prev, size: parseInt(e.target.value) }))}
                inputProps={{ min: 128, max: 512, step: 32 }}
                helperText="Size in pixels (128-512)"
              />
              
              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="Dark Color"
                  type="color"
                  value={qrCodeSettings.darkColor}
                  onChange={(e) => setQrCodeSettings(prev => ({ ...prev, darkColor: e.target.value }))}
                  sx={{ flex: 1 }}
                />
                <TextField
                  label="Light Color"
                  type="color"
                  value={qrCodeSettings.lightColor}
                  onChange={(e) => setQrCodeSettings(prev => ({ ...prev, lightColor: e.target.value }))}
                  sx={{ flex: 1 }}
                />
              </Box>

              <FormControlLabel
                control={
                  <Switch
                    checked={qrCodeSettings.includeMargin}
                    onChange={(e) => setQrCodeSettings(prev => ({ ...prev, includeMargin: e.target.checked }))}
                  />
                }
                label="Include margin around QR code"
              />
            </Box>
          </AccordionDetails>
        </Accordion>

        {/* Data Management */}
        <Accordion sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ChevronDown />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Download size={16} />
              <Typography variant="subtitle1">Data Management</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="outlined"
                  startIcon={<Download size={16} />}
                  onClick={handleExportData}
                >
                  Export Data
                </Button>
                
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<Download size={16} />}
                >
                  Import Data
                  <input
                    type="file"
                    accept=".json"
                    hidden
                    onChange={handleImportData}
                  />
                </Button>
                
                <Button
                  variant="outlined"
                  color="error"
                  onClick={resetAllData}
                >
                  Reset All Data
                </Button>
              </Box>

              <FormControlLabel
                control={
                  <Switch
                    checked={showRawData}
                    onChange={(e) => setShowRawData(e.target.checked)}
                  />
                }
                label="Show raw data (JSON)"
              />

              {showRawData && (
                <TextField
                  label="Raw Data (JSON)"
                  value={JSON.stringify(businessData, null, 2)}
                  fullWidth
                  multiline
                  rows={15}
                  InputProps={{
                    readOnly: true,
                  }}
                  sx={{
                    '& .MuiInputBase-input': {
                      fontFamily: 'monospace',
                      fontSize: '0.75rem',
                    },
                  }}
                  helperText="Read-only view of all business data in JSON format"
                />
              )}
            </Box>
          </AccordionDetails>
        </Accordion>

        <Divider sx={{ my: 3 }} />

        {/* Meta Information */}
        <Box>
          <Typography variant="h6" gutterBottom>
            Meta Information
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Page Title"
              value={businessData.pageTitle}
              onChange={(e) => onUpdate('pageTitle', e.target.value)}
              fullWidth
              helperText="This will appear in the browser tab and search results"
            />
            
            <Alert severity="info">
              <Typography variant="body2">
                <strong>SEO Tips:</strong>
                <br />
                • Keep page title under 60 characters
                <br />
                • Include your business name and main service
                <br />
                • Use descriptive, keyword-rich content
              </Typography>
            </Alert>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default AdvancedTab;
