import { TemplateEngine, convertBusinessDataToTemplateData } from './templateEngine';
import type { BusinessData } from '../types/index';

export const debugTemplate = async (businessData: BusinessData) => {
  console.log('=== DEBUG TEMPLATE ===');

  try {
    // Test with simple template first
    const testTemplateEngine = new TemplateEngine();

    // Load test template
    const testResponse = await fetch('/test-template.html');
    testTemplateEngine.template = await testResponse.text();

    console.log('Test template loaded');
    console.log('Test template length:', testTemplateEngine.template.length);

    const templateData = convertBusinessDataToTemplateData(businessData);
    console.log('Template data:', {
      HAS_SERVICES: templateData.HAS_SERVICES,
      SERVICES: templateData.SERVICES,
      HAS_OFFERS: templateData.HAS_OFFERS,
      OFFERS: templateData.OFFERS
    });

    const html = testTemplateEngine.render(templateData);
    console.log('Rendered HTML length:', html.length);
    console.log('Rendered HTML preview:', html.substring(0, 500));

    // Check for template syntax remaining
    const hasUnprocessedSyntax = /\{\{[^}]+\}\}/.test(html);
    console.log('Has unprocessed template syntax:', hasUnprocessedSyntax);

    if (hasUnprocessedSyntax) {
      const matches = html.match(/\{\{[^}]+\}\}/g);
      console.log('Unprocessed syntax:', matches);
    }

    // Check for specific sections
    const hasServicesSection = html.includes('Our Services');
    const hasOffersSection = html.includes('Special Offers');
    const hasServiceItems = html.includes('Manicure');
    const hasOfferItems = html.includes('New Customer Special');

    console.log('Section checks:', {
      hasServicesSection,
      hasOffersSection,
      hasServiceItems,
      hasOfferItems
    });

    return html;
  } catch (error) {
    console.error('Debug template error:', error);
    throw error;
  }
};
