import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Alert,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import { Download, Share2, QrCode, Copy } from 'lucide-react';

interface QRCodeTabProps {
  businessData: any;
  previewHtml: string;
  templateName: string;
}

const QRCodeTab: React.FC<QRCodeTabProps> = ({
  businessData,
  previewHtml,
  templateName
}) => {
  const [qrUrl, setQrUrl] = useState('');
  const [qrSize, setQrSize] = useState(200);
  const [qrColor, setQrColor] = useState('#000000');
  const [qrBgColor, setQrBgColor] = useState('#ffffff');
  const [errorLevel, setErrorLevel] = useState('M');
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState('');

  // Generate a temporary URL for the business page
  useEffect(() => {
    // In a real implementation, you would upload the HTML to a server
    // For demo purposes, we'll create a blob URL
    const blob = new Blob([previewHtml], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    setQrUrl(url);

    return () => {
      URL.revokeObjectURL(url);
    };
  }, [previewHtml]);

  // Generate QR Code using a QR code library (placeholder implementation)
  useEffect(() => {
    generateQRCode();
  }, [qrUrl, qrSize, qrColor, qrBgColor, errorLevel]);

  const generateQRCode = async () => {
    try {
      // This is a placeholder - in a real implementation, you would use a QR code library
      // like qrcode.js or similar
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${qrSize}x${qrSize}&data=${encodeURIComponent(qrUrl)}&color=${qrColor.replace('#', '')}&bgcolor=${qrBgColor.replace('#', '')}&ecc=${errorLevel}`;
      setQrCodeDataUrl(qrCodeUrl);
    } catch (error) {
      console.error('Failed to generate QR code:', error);
    }
  };

  const downloadQRCode = async () => {
    try {
      const response = await fetch(qrCodeDataUrl);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `${businessData.businessName || templateName}-qr-code.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download QR code:', error);
    }
  };

  const copyQRUrl = () => {
    navigator.clipboard.writeText(qrUrl);
    // You could add a toast notification here
  };

  const shareQRCode = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${businessData.businessName} - QR Code`,
          text: `Check out ${businessData.businessName}`,
          url: qrUrl
        });
      } catch (error) {
        console.error('Failed to share:', error);
      }
    } else {
      // Fallback to copying URL
      copyQRUrl();
    }
  };

  const exportBusinessPage = () => {
    const blob = new Blob([previewHtml], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `${businessData.businessName || templateName}-page.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        QR Code Generation
      </Typography>

      <Grid container spacing={3}>
        {/* QR Code Preview */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom>
              QR Code Preview
            </Typography>

            {qrCodeDataUrl ? (
              <Box>
                <img
                  src={qrCodeDataUrl}
                  alt="QR Code"
                  style={{
                    maxWidth: '100%',
                    height: 'auto',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    marginBottom: '16px'
                  }}
                />
                <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    startIcon={<Download size={16} />}
                    onClick={downloadQRCode}
                  >
                    Download
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Share2 size={16} />}
                    onClick={shareQRCode}
                  >
                    Share
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Copy size={16} />}
                    onClick={copyQRUrl}
                  >
                    Copy URL
                  </Button>
                </Box>
              </Box>
            ) : (
              <Box sx={{ py: 4 }}>
                <QrCode size={48} style={{ color: '#ccc', marginBottom: '16px' }} />
                <Typography color="text.secondary">
                  Generating QR code...
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* QR Code Settings */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              QR Code Settings
            </Typography>

            <TextField
              fullWidth
              label="QR Code URL"
              value={qrUrl}
              onChange={(e) => setQrUrl(e.target.value)}
              margin="normal"
              helperText="The URL that the QR code will link to"
            />

            <Box sx={{ mt: 2 }}>
              <Typography gutterBottom>Size: {qrSize}px</Typography>
              <Slider
                value={qrSize}
                onChange={(_, value) => setQrSize(value as number)}
                min={100}
                max={500}
                step={10}
                marks={[
                  { value: 100, label: '100px' },
                  { value: 200, label: '200px' },
                  { value: 300, label: '300px' },
                  { value: 500, label: '500px' }
                ]}
              />
            </Box>

            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid size={{ xs: 6 }}>
                <TextField
                  fullWidth
                  label="Foreground Color"
                  value={qrColor}
                  onChange={(e) => setQrColor(e.target.value)}
                  type="color"
                />
              </Grid>
              <Grid size={{ xs: 6 }}>
                <TextField
                  fullWidth
                  label="Background Color"
                  value={qrBgColor}
                  onChange={(e) => setQrBgColor(e.target.value)}
                  type="color"
                />
              </Grid>
            </Grid>

            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>Error Correction Level</InputLabel>
              <Select
                value={errorLevel}
                label="Error Correction Level"
                onChange={(e) => setErrorLevel(e.target.value)}
              >
                <MenuItem value="L">Low (7%)</MenuItem>
                <MenuItem value="M">Medium (15%)</MenuItem>
                <MenuItem value="Q">Quartile (25%)</MenuItem>
                <MenuItem value="H">High (30%)</MenuItem>
              </Select>
            </FormControl>
          </Paper>
        </Grid>

        {/* Export Options */}
        <Grid size={{ xs: 12 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Export Options
            </Typography>

            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <QrCode size={32} style={{ marginBottom: '8px' }} />
                    <Typography variant="h6" gutterBottom>
                      QR Code Only
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Download just the QR code image
                    </Typography>
                    <Button
                      variant="contained"
                      fullWidth
                      onClick={downloadQRCode}
                    >
                      Download PNG
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Download size={32} style={{ marginBottom: '8px' }} />
                    <Typography variant="h6" gutterBottom>
                      Business Page
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Download the complete HTML page
                    </Typography>
                    <Button
                      variant="contained"
                      fullWidth
                      onClick={exportBusinessPage}
                    >
                      Download HTML
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Share2 size={32} style={{ marginBottom: '8px' }} />
                    <Typography variant="h6" gutterBottom>
                      Share Link
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Share the business page URL
                    </Typography>
                    <Button
                      variant="contained"
                      fullWidth
                      onClick={shareQRCode}
                    >
                      Share URL
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Usage Instructions */}
        <Grid size={{ xs: 12 }}>
          <Alert severity="info">
            <Typography variant="body2">
              <strong>How to use your QR code:</strong>
              <br />1. Download the QR code image and print it on business cards, flyers, or posters
              <br />2. Customers can scan the QR code with their phone camera to instantly view your business page
              <br />3. The business page works on all devices and doesn't require any app installation
              <br />4. You can update your business information anytime and the QR code will show the latest content
            </Typography>
          </Alert>
        </Grid>

        {/* Business Summary */}
        <Grid size={{ xs: 12 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Business Summary
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Typography variant="subtitle2" color="text.secondary">Business Name</Typography>
                <Typography variant="body1" sx={{ mb: 1 }}>{businessData.businessName}</Typography>
                
                <Typography variant="subtitle2" color="text.secondary">Contact</Typography>
                <Typography variant="body2">{businessData.phone}</Typography>
                <Typography variant="body2">{businessData.email}</Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>{businessData.address}</Typography>
              </Grid>
              
              <Grid size={{ xs: 12, sm: 6 }}>
                <Typography variant="subtitle2" color="text.secondary">Content</Typography>
                <Typography variant="body2">Services: {businessData.services?.length || 0}</Typography>
                <Typography variant="body2">Offers: {businessData.offers?.length || 0}</Typography>
                <Typography variant="body2">Gallery: {businessData.galleryImages?.length || 0} images</Typography>
                
                <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>Social Media</Typography>
                <Typography variant="body2">
                  {[businessData.facebookUrl, businessData.instagramUrl, businessData.yelpUrl]
                    .filter(Boolean).length} social links
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default QRCodeTab;
