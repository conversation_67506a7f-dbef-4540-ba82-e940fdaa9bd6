import React, { useState } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, AppBar, Toolbar, Typography, Tabs, Tab } from '@mui/material';
import { Layout, FileText, Users } from 'lucide-react';

// Import components
import TemplateLibrary from './components/TemplateLibrary';
import BusinessManager from './components/BusinessManager';
import TemplateUpload from './components/TemplateUpload';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function App() {
  const [currentTab, setCurrentTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ width: '100vw', height: '100vh', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <AppBar position="static">
          <Toolbar>
            <Layout size={24} style={{ marginRight: '12px' }} />
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              QR Template Manager
            </Typography>
          </Toolbar>
        </AppBar>

        {/* Navigation Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={handleTabChange} aria-label="template manager tabs">
            <Tab
              icon={<Layout size={20} />}
              label="Template Library"
              id="tab-0"
              aria-controls="tabpanel-0"
            />
            <Tab
              icon={<Users size={20} />}
              label="Business Manager"
              id="tab-1"
              aria-controls="tabpanel-1"
            />
            <Tab
              icon={<FileText size={20} />}
              label="Upload Template"
              id="tab-2"
              aria-controls="tabpanel-2"
            />
          </Tabs>
        </Box>

        {/* Tab Content */}
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <TabPanel value={currentTab} index={0}>
            <TemplateLibrary />
          </TabPanel>
          <TabPanel value={currentTab} index={1}>
            <BusinessManager />
          </TabPanel>
          <TabPanel value={currentTab} index={2}>
            <TemplateUpload />
          </TabPanel>
        </Box>
      </Box>
    </ThemeProvider>
  );
}

export default App;
