import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, Grid } from '@mui/material';
import { useBusinessData } from './hooks/useBusinessData';
import { useEditorState } from './hooks/useEditorState';
import Header from './components/Layout/Header';
import EditorPanel from './components/Editor/EditorPanel';
import MobilePreview from './components/Preview/MobilePreview';
import { TemplateEngine, convertBusinessDataToTemplateData } from './utils/templateEngine';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
});

function App() {
  const { businessData, updateField, batchUpdate, applyTemplate, saveToServer } = useBusinessData();
  const {
    editorState,
    setPreviewDevice,
    togglePreview,
    setCurrentBasicTab
  } = useEditorState();

  // Debug: Track significant businessData changes only
  React.useEffect(() => {
    const hasLogo = !!(businessData.logoUrl && businessData.logoUrl.trim());
    const hasFavicon = !!(businessData.faviconUrl && businessData.faviconUrl.trim());
    console.log('� App: Data changed - Logo:', hasLogo ? '✅' : '❌', 'Favicon:', hasFavicon ? '✅' : '❌');
  }, [businessData.logoUrl, businessData.faviconUrl, businessData.businessName]);

  const handleSave = async () => {
    console.log('Saving business data:', businessData);
    const success = await saveToServer();
    if (success) {
      alert('✅ Data saved successfully to JSON Server!');
    } else {
      alert('❌ Failed to save data. Check console for details.');
    }
  };

  const handleExport = async () => {
    try {
      const templateEngine = new TemplateEngine();
      await templateEngine.loadTemplate();

      const templateData = convertBusinessDataToTemplateData(businessData);
      const html = templateEngine.render(templateData);

      const blob = new Blob([html], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${businessData.businessName.toLowerCase().replace(/\s+/g, '-')}-qr-page.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ width: '100vw', height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <Header
          editorState={editorState}
          onSave={handleSave}
          onExport={handleExport}
          onPreviewDeviceChange={setPreviewDevice}
          onTogglePreview={togglePreview}
        />

        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          <Grid container sx={{ height: '100%', width: '100%' }}>
            {/* Editor Panel */}
            <Grid
              size={{ xs: 12, md: editorState.isPreviewOpen ? 6 : 12 }}
              sx={{ height: '100%' }}
            >
              <EditorPanel
                businessData={businessData}
                currentTab={editorState.currentBasicTab}
                onTabChange={setCurrentBasicTab}
                onUpdate={updateField}
                onBatchUpdate={batchUpdate}
                onApplyTemplate={applyTemplate}
              />
            </Grid>

            {/* Preview Panel */}
            {editorState.isPreviewOpen && (
              <Grid
                size={{ xs: 12, md: 6 }}
                sx={{
                  height: '100%',
                  borderLeft: '1px solid #e0e0e0',
                  bgcolor: '#f5f5f5',
                  overflow: 'auto'
                }}
              >
                <MobilePreview
                  businessData={businessData}
                  device={editorState.previewDevice}
                />
              </Grid>
            )}
          </Grid>
        </Box>
      </Box>
    </ThemeProvider>
  );
}

export default App;
