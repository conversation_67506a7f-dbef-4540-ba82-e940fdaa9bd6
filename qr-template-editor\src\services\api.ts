import type { BusinessData, TemplateRecord, TemplateCategory, AppSettings } from '../types/index';

const API_BASE_URL = 'http://localhost:3001';

export class ApiService {
  private static instance: ApiService;
  
  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }



  // Patch specific field
  async updateField(field: keyof BusinessData, value: any): Promise<BusinessData> {
    try {
      const response = await fetch(`${API_BASE_URL}/businessData`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ [field]: value }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      // Handle JSON Server v1+ response format
      return result.item || result;
    } catch (error) {
      console.error(`Failed to update field ${field}:`, error);
      throw error;
    }
  }

  // Auto-save with debouncing
  private saveTimeout: number | null = null;
  
  autoSave(data: BusinessData, delay: number = 1000): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.saveTimeout) {
        clearTimeout(this.saveTimeout);
      }
      
      this.saveTimeout = setTimeout(async () => {
        try {
          await this.updateBusinessData(data);
          console.log('Auto-saved successfully');
          resolve();
        } catch (error) {
          console.error('Auto-save failed:', error);
          reject(error);
        }
      }, delay);
    });
  }

  // Check if server is available
  async checkServerHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/businessData`, {
        method: 'HEAD',
      });
      return response.ok;
    } catch (error) {
      console.error('Server health check failed:', error);
      return false;
    }
  }

  // Backup data to localStorage
  backupToLocalStorage(data: BusinessData): void {
    try {
      localStorage.setItem('qr-template-backup', JSON.stringify({
        data,
        timestamp: new Date().toISOString(),
      }));
    } catch (error) {
      console.error('Failed to backup to localStorage:', error);
    }
  }

  // Restore data from localStorage
  restoreFromLocalStorage(): BusinessData | null {
    try {
      const backup = localStorage.getItem('qr-template-backup');
      if (backup) {
        const parsed = JSON.parse(backup);
        return parsed.data;
      }
      return null;
    } catch (error) {
      console.error('Failed to restore from localStorage:', error);
      return null;
    }
  }

  // Clear localStorage backup
  clearLocalStorageBackup(): void {
    try {
      localStorage.removeItem('qr-template-backup');
    } catch (error) {
      console.error('Failed to clear localStorage backup:', error);
    }
  }

  // ===== TEMPLATE MANAGEMENT METHODS =====

  // Get all templates
  async getAllTemplates(): Promise<TemplateRecord[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.items || data;
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      throw error;
    }
  }

  // Get template by ID
  async getTemplate(id: number): Promise<TemplateRecord> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates/${id}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.item || data;
    } catch (error) {
      console.error(`Failed to fetch template ${id}:`, error);
      throw error;
    }
  }

  // Create new template
  async createTemplate(templateData: Omit<TemplateRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<TemplateRecord> {
    try {
      const newTemplate = {
        ...templateData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const response = await fetch(`${API_BASE_URL}/templates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newTemplate),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.item || result;
    } catch (error) {
      console.error('Failed to create template:', error);
      throw error;
    }
  }

  // Update template
  async updateTemplate(id: number, templateData: Partial<TemplateRecord>): Promise<TemplateRecord> {
    try {
      const updateData = {
        ...templateData,
        updatedAt: new Date().toISOString(),
      };

      const response = await fetch(`${API_BASE_URL}/templates/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.item || result;
    } catch (error) {
      console.error(`Failed to update template ${id}:`, error);
      throw error;
    }
  }

  // Delete template
  async deleteTemplate(id: number): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error(`Failed to delete template ${id}:`, error);
      throw error;
    }
  }

  // Duplicate template
  async duplicateTemplate(id: number, newName?: string): Promise<TemplateRecord> {
    try {
      const originalTemplate = await this.getTemplate(id);
      const duplicatedTemplate = {
        ...originalTemplate,
        name: newName || `${originalTemplate.name} (Copy)`,
        isActive: false, // New templates start as inactive
      };

      // Remove id, createdAt, updatedAt to create new record
      const { id: _, createdAt: __, updatedAt: ___, ...templateData } = duplicatedTemplate;

      return await this.createTemplate(templateData);
    } catch (error) {
      console.error(`Failed to duplicate template ${id}:`, error);
      throw error;
    }
  }

  // Get templates by category
  async getTemplatesByCategory(category: string): Promise<TemplateRecord[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates?category=${category}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.items || data;
    } catch (error) {
      console.error(`Failed to fetch templates by category ${category}:`, error);
      throw error;
    }
  }

  // Get active templates
  async getActiveTemplates(): Promise<TemplateRecord[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates?isActive=true`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.items || data;
    } catch (error) {
      console.error('Failed to fetch active templates:', error);
      throw error;
    }
  }

  // Get categories
  async getCategories(): Promise<TemplateCategory[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/categories`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.items || data;
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      throw error;
    }
  }

  // Get settings
  async getSettings(): Promise<AppSettings> {
    try {
      const response = await fetch(`${API_BASE_URL}/settings`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.item || data;
    } catch (error) {
      console.error('Failed to fetch settings:', error);
      throw error;
    }
  }

  // Update settings
  async updateSettings(settings: Partial<AppSettings>): Promise<AppSettings> {
    try {
      const response = await fetch(`${API_BASE_URL}/settings`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.item || result;
    } catch (error) {
      console.error('Failed to update settings:', error);
      throw error;
    }
  }

  // ===== LEGACY SUPPORT =====

  // Get business data (backward compatibility)
  async getBusinessData(): Promise<BusinessData> {
    try {
      // Try to get the first active template
      const templates = await this.getActiveTemplates();
      if (templates.length > 0) {
        const template = templates[0];
        // Convert TemplateRecord to BusinessData
        const { id, name, description, category, isActive, createdAt, updatedAt, ...businessData } = template;
        return businessData as BusinessData;
      }

      // Fallback to old endpoint
      const response = await fetch(`${API_BASE_URL}/businessData`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data.item || data;
    } catch (error) {
      console.error('Failed to fetch business data:', error);
      throw error;
    }
  }

  // Update business data (backward compatibility)
  async updateBusinessData(data: BusinessData): Promise<BusinessData> {
    try {
      // Try to update the first template
      const templates = await this.getAllTemplates();
      if (templates.length > 0) {
        const templateId = templates[0].id;
        const updatedTemplate = await this.updateTemplate(templateId, data);
        const { id, name, description, category, isActive, createdAt, updatedAt, ...businessData } = updatedTemplate;
        return businessData as BusinessData;
      }

      // Fallback to old endpoint
      const response = await fetch(`${API_BASE_URL}/businessData`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.item || result;
    } catch (error) {
      console.error('Failed to update business data:', error);
      throw error;
    }
  }
}

export const apiService = ApiService.getInstance();
