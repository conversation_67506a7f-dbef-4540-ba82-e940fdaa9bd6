import type { BusinessData } from '../types/index';

const API_BASE_URL = 'http://localhost:3001';

export class ApiService {
  private static instance: ApiService;
  
  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  // Get business data
  async getBusinessData(): Promise<BusinessData> {
    try {
      const response = await fetch(`${API_BASE_URL}/businessData`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch business data:', error);
      throw error;
    }
  }

  // Update business data
  async updateBusinessData(data: BusinessData): Promise<BusinessData> {
    try {
      const response = await fetch(`${API_BASE_URL}/businessData`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to update business data:', error);
      throw error;
    }
  }

  // Patch specific field
  async updateField(field: keyof BusinessData, value: any): Promise<BusinessData> {
    try {
      const response = await fetch(`${API_BASE_URL}/businessData`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ [field]: value }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Failed to update field ${field}:`, error);
      throw error;
    }
  }

  // Auto-save with debouncing
  private saveTimeout: NodeJS.Timeout | null = null;
  
  autoSave(data: BusinessData, delay: number = 1000): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.saveTimeout) {
        clearTimeout(this.saveTimeout);
      }
      
      this.saveTimeout = setTimeout(async () => {
        try {
          await this.updateBusinessData(data);
          console.log('Auto-saved successfully');
          resolve();
        } catch (error) {
          console.error('Auto-save failed:', error);
          reject(error);
        }
      }, delay);
    });
  }

  // Check if server is available
  async checkServerHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/businessData`, {
        method: 'HEAD',
      });
      return response.ok;
    } catch (error) {
      console.error('Server health check failed:', error);
      return false;
    }
  }

  // Backup data to localStorage
  backupToLocalStorage(data: BusinessData): void {
    try {
      localStorage.setItem('qr-template-backup', JSON.stringify({
        data,
        timestamp: new Date().toISOString(),
      }));
    } catch (error) {
      console.error('Failed to backup to localStorage:', error);
    }
  }

  // Restore data from localStorage
  restoreFromLocalStorage(): BusinessData | null {
    try {
      const backup = localStorage.getItem('qr-template-backup');
      if (backup) {
        const parsed = JSON.parse(backup);
        return parsed.data;
      }
      return null;
    } catch (error) {
      console.error('Failed to restore from localStorage:', error);
      return null;
    }
  }

  // Clear localStorage backup
  clearLocalStorageBackup(): void {
    try {
      localStorage.removeItem('qr-template-backup');
    } catch (error) {
      console.error('Failed to clear localStorage backup:', error);
    }
  }
}

export const apiService = ApiService.getInstance();
