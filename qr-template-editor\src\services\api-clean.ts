import type { BusinessData, TemplateRecord, TemplateCategory, AppSettings } from '../types/index';

const API_BASE_URL = 'http://localhost:3001';

export class ApiService {
  private static instance: ApiService;
  private saveTimeout: number | null = null;
  
  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  // ===== CORE BUSINESS DATA METHODS =====

  // Get business data (from first active template)
  async getBusinessData(): Promise<BusinessData> {
    try {
      console.log('🔄 API: Getting business data...');
      
      // Get first active template
      const response = await fetch(`${API_BASE_URL}/templates?isActive=true`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const templates = await response.json();
      console.log('🔄 API: Found templates:', templates.length);
      
      if (templates.length > 0) {
        const template = templates[0];
        console.log('🔄 API: Using template:', template.name);
        
        // Convert TemplateRecord to BusinessData
        const { id, name, description, category, isActive, createdAt, updatedAt, ...businessData } = template;
        
        console.log('🔄 API: Converted data:', {
          yelpUrl: businessData.yelpUrl,
          instagramUrl: businessData.instagramUrl,
          customSocialLinks: businessData.customSocialLinks?.length || 0
        });
        
        return businessData as BusinessData;
      }
      
      throw new Error('No active templates found');
    } catch (error) {
      console.error('❌ API: Failed to fetch business data:', error);
      throw error;
    }
  }

  // Update business data (update first template)
  async updateBusinessData(data: BusinessData): Promise<BusinessData> {
    try {
      console.log('🔄 API: Updating business data...');
      
      // Get all templates to find first one
      const templatesResponse = await fetch(`${API_BASE_URL}/templates`);
      if (!templatesResponse.ok) {
        throw new Error(`HTTP error! status: ${templatesResponse.status}`);
      }
      
      const templates = await templatesResponse.json();
      if (templates.length === 0) {
        throw new Error('No templates found');
      }
      
      const templateId = templates[0].id;
      const existingTemplate = templates[0];
      console.log('🔄 API: Updating template ID:', templateId);

      // Merge business data with existing template data
      const updateData = {
        ...existingTemplate,
        ...data,
        updatedAt: new Date().toISOString(),
      };

      const response = await fetch(`${API_BASE_URL}/templates/${templateId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ API: Updated successfully');
      
      // Convert back to BusinessData
      const { id, name, description, category, isActive, createdAt, updatedAt, ...businessData } = result;
      return businessData as BusinessData;
    } catch (error) {
      console.error('❌ API: Failed to update business data:', error);
      throw error;
    }
  }

  // Auto-save with debouncing
  autoSave(data: BusinessData, delay: number = 1000): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.saveTimeout) {
        clearTimeout(this.saveTimeout);
      }
      
      this.saveTimeout = setTimeout(async () => {
        try {
          await this.updateBusinessData(data);
          console.log('✅ Auto-saved successfully');
          resolve();
        } catch (error) {
          console.error('❌ Auto-save failed:', error);
          reject(error);
        }
      }, delay);
    });
  }

  // Check if server is available
  async checkServerHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates`, {
        method: 'HEAD',
      });
      return response.ok;
    } catch (error) {
      console.error('❌ Server health check failed:', error);
      return false;
    }
  }

  // ===== BACKUP METHODS =====

  // Backup data to localStorage
  backupToLocalStorage(data: BusinessData): void {
    try {
      localStorage.setItem('qr-template-backup', JSON.stringify({
        data,
        timestamp: new Date().toISOString(),
      }));
      console.log('📦 Backed up to localStorage');
    } catch (error) {
      console.error('❌ Failed to backup to localStorage:', error);
    }
  }

  // Restore data from localStorage
  restoreFromLocalStorage(): BusinessData | null {
    try {
      const backup = localStorage.getItem('qr-template-backup');
      if (backup) {
        const parsed = JSON.parse(backup);
        console.log('📦 Restored from localStorage');
        return parsed.data;
      }
      return null;
    } catch (error) {
      console.error('❌ Failed to restore from localStorage:', error);
      return null;
    }
  }

  // Clear localStorage backup
  clearLocalStorageBackup(): void {
    try {
      localStorage.removeItem('qr-template-backup');
      console.log('🗑️ Cleared localStorage backup');
    } catch (error) {
      console.error('❌ Failed to clear localStorage backup:', error);
    }
  }

  // ===== TEMPLATE MANAGEMENT METHODS (Future) =====

  // Get all templates
  async getAllTemplates(): Promise<TemplateRecord[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('❌ Failed to fetch templates:', error);
      throw error;
    }
  }

  // Get template by ID
  async getTemplate(id: number): Promise<TemplateRecord> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates/${id}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`❌ Failed to fetch template ${id}:`, error);
      throw error;
    }
  }

  // Get categories (mock data since endpoint doesn't exist)
  async getCategories(): Promise<TemplateCategory[]> {
    try {
      // Return mock categories since /categories endpoint doesn't exist
      return [
        { id: 1, name: 'Beauty & Wellness', slug: 'beauty', description: 'Nail salons, spas, beauty services', icon: 'palette' },
        { id: 2, name: 'Food & Beverage', slug: 'food', description: 'Restaurants, cafes, food services', icon: 'utensils' },
        { id: 3, name: 'Retail', slug: 'retail', description: 'Shops, stores, retail businesses', icon: 'shopping-bag' },
        { id: 4, name: 'Professional Services', slug: 'services', description: 'Consulting, professional services', icon: 'briefcase' }
      ];
    } catch (error) {
      console.error('❌ Failed to fetch categories:', error);
      throw error;
    }
  }

  // Get settings (mock data since endpoint doesn't exist)
  async getSettings(): Promise<AppSettings> {
    try {
      // Return mock settings since /settings endpoint doesn't exist
      return {
        defaultTemplate: 'minimal' as const,
        maxTemplatesPerUser: 10,
        autoSaveInterval: 1000
      };
    } catch (error) {
      console.error('❌ Failed to fetch settings:', error);
      throw error;
    }
  }
}

export const apiService = ApiService.getInstance();
