import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  LinearProgress,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import { Upload, FileText, Image, Code, CheckCircle, AlertCircle } from 'lucide-react';
import type { TemplateCategory } from '../types/template';
import { templateValidator } from '../utils/templateValidator';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  features: string[];
}

const TemplateUpload: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '' as TemplateCategory | '',
    tags: '',
    isPublic: false
  });
  
  const [files, setFiles] = useState({
    html: null as File | null,
    css: null as File | null,
    js: null as File | null,
    preview: null as File | null
  });
  
  const [uploading, setUploading] = useState(false);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (type: keyof typeof files, file: File | null) => {
    setFiles(prev => ({ ...prev, [type]: file }));
    
    // Validate HTML file when uploaded
    if (type === 'html' && file) {
      validateHtmlFile(file);
    }
  };

  const validateHtmlFile = async (file: File) => {
    try {
      const content = await file.text();

      // Use templateValidator for comprehensive validation
      const validationResult = templateValidator.validateTemplate(content);

      setValidation({
        isValid: validationResult.isValid,
        errors: validationResult.errors,
        warnings: validationResult.warnings,
        features: validationResult.features
      });
    } catch (err) {
      setValidation({
        isValid: false,
        errors: ['Failed to read HTML file'],
        warnings: [],
        features: []
      });
    }
  };

  const handleSubmit = async () => {
    if (!files.html || !formData.name || !formData.category) {
      setError('Please fill in all required fields and upload an HTML file');
      return;
    }

    setUploading(true);
    setError(null);

    try {
      // Read file contents
      const htmlContent = await files.html.text();
      let cssContent = '';
      let jsContent = '';
      let previewImage = '';

      if (files.css) {
        cssContent = await files.css.text();
      }

      if (files.js) {
        jsContent = await files.js.text();
      }

      if (files.preview) {
        previewImage = await fileToBase64(files.preview);
      }

      // Create template object
      const template = {
        id: Date.now().toString(),
        name: formData.name,
        description: formData.description,
        category: formData.category,
        version: '1.0.0',
        author: 'User',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        htmlContent,
        cssContent,
        jsContent,
        metadata: {
          features: validation?.features || [],
          customizable: {
            colors: htmlContent.includes('PRIMARY_COLOR') || htmlContent.includes('SECONDARY_COLOR'),
            fonts: htmlContent.includes('font'),
            layout: false,
            header: htmlContent.includes('HEADER_'),
            footer: false,
            sidebar: false
          },
          requiredVariables: ['BUSINESS_NAME', 'PHONE', 'EMAIL', 'ADDRESS'],
          optionalVariables: ['SLOGAN', 'DESCRIPTION', 'LOGO_URL', 'FAVICON_URL'],
          supportedDevices: ['mobile', 'tablet', 'desktop'],
          dependencies: [],
          tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
        },
        previewImage,
        status: 'draft' as const,
        isPublic: formData.isPublic,
        downloadCount: 0,
        validationResult: validation ? {
          ...validation,
          score: validation.isValid ? 90 : 60,
          lastValidated: new Date().toISOString()
        } : undefined
      };

      // Upload to server
      const response = await fetch('http://localhost:3001/templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(template)
      });

      if (!response.ok) throw new Error('Failed to upload template');

      setSuccess(true);
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        category: '',
        tags: '',
        isPublic: false
      });
      setFiles({
        html: null,
        css: null,
        js: null,
        preview: null
      });
      setValidation(null);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload template');
    } finally {
      setUploading(false);
    }
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const FileUploadBox: React.FC<{
    title: string;
    accept: string;
    file: File | null;
    onChange: (file: File | null) => void;
    required?: boolean;
    icon: React.ReactNode;
  }> = ({ title, accept, file, onChange, required, icon }) => (
    <Paper
      sx={{
        p: 2,
        border: '2px dashed',
        borderColor: file ? 'success.main' : 'grey.300',
        textAlign: 'center',
        cursor: 'pointer',
        '&:hover': { borderColor: 'primary.main' }
      }}
      onClick={() => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = accept;
        input.onchange = (e) => {
          const target = e.target as HTMLInputElement;
          onChange(target.files?.[0] || null);
        };
        input.click();
      }}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
        {icon}
        <Typography variant="subtitle2">
          {title} {required && '*'}
        </Typography>
        {file ? (
          <Typography variant="body2" color="success.main">
            ✓ {file.name}
          </Typography>
        ) : (
          <Typography variant="body2" color="text.secondary">
            Click to upload
          </Typography>
        )}
      </Box>
    </Paper>
  );

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Upload Template
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Template uploaded successfully!
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Form */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Template Information
              </Typography>

              <TextField
                fullWidth
                label="Template Name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                margin="normal"
                required
              />

              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                margin="normal"
                multiline
                rows={3}
              />

              <FormControl fullWidth margin="normal" required>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  label="Category"
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  <MenuItem value="business">Business</MenuItem>
                  <MenuItem value="restaurant">Restaurant</MenuItem>
                  <MenuItem value="beauty">Beauty</MenuItem>
                  <MenuItem value="healthcare">Healthcare</MenuItem>
                  <MenuItem value="retail">Retail</MenuItem>
                  <MenuItem value="service">Service</MenuItem>
                  <MenuItem value="event">Event</MenuItem>
                  <MenuItem value="portfolio">Portfolio</MenuItem>
                  <MenuItem value="custom">Custom</MenuItem>
                </Select>
              </FormControl>

              <TextField
                fullWidth
                label="Tags (comma separated)"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                margin="normal"
                placeholder="modern, responsive, business"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* File Uploads */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Template Files
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FileUploadBox
                    title="HTML File"
                    accept=".html,.htm"
                    file={files.html}
                    onChange={(file) => handleFileChange('html', file)}
                    required
                    icon={<FileText size={24} />}
                  />
                </Grid>

                <Grid item xs={6}>
                  <FileUploadBox
                    title="CSS File"
                    accept=".css"
                    file={files.css}
                    onChange={(file) => handleFileChange('css', file)}
                    icon={<Code size={24} />}
                  />
                </Grid>

                <Grid item xs={6}>
                  <FileUploadBox
                    title="JS File"
                    accept=".js"
                    file={files.js}
                    onChange={(file) => handleFileChange('js', file)}
                    icon={<Code size={24} />}
                  />
                </Grid>

                <Grid item xs={12}>
                  <FileUploadBox
                    title="Preview Image"
                    accept="image/*"
                    file={files.preview}
                    onChange={(file) => handleFileChange('preview', file)}
                    icon={<Image size={24} />}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Validation Results */}
        {validation && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Validation Results
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  {validation.isValid ? (
                    <CheckCircle color="green" size={20} />
                  ) : (
                    <AlertCircle color="orange" size={20} />
                  )}
                  <Typography variant="subtitle1">
                    {validation.isValid ? 'Template is valid' : 'Template has issues'}
                  </Typography>
                </Box>

                {validation.errors.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="error">
                      Errors:
                    </Typography>
                    {validation.errors.map((error, index) => (
                      <Typography key={index} variant="body2" color="error">
                        • {error}
                      </Typography>
                    ))}
                  </Box>
                )}

                {validation.warnings.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="warning.main">
                      Warnings:
                    </Typography>
                    {validation.warnings.map((warning, index) => (
                      <Typography key={index} variant="body2" color="warning.main">
                        • {warning}
                      </Typography>
                    ))}
                  </Box>
                )}

                {validation.features.length > 0 && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Detected Features:
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {validation.features.map((feature) => (
                        <Chip key={feature} label={feature} size="small" />
                      ))}
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {/* Upload Button */}
      <Box sx={{ mt: 3, textAlign: 'center' }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<Upload size={20} />}
          onClick={handleSubmit}
          disabled={uploading || !files.html || !formData.name || !formData.category}
        >
          {uploading ? 'Uploading...' : 'Upload Template'}
        </Button>
        
        {uploading && (
          <LinearProgress sx={{ mt: 2 }} />
        )}
      </Box>
    </Box>
  );
};

export default TemplateUpload;
