{"version": 3, "file": "sequenceEqual.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/sequenceEqual.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AA2DpD,MAAM,UAAU,aAAa,CAC3B,SAA6B,EAC7B,aAAsC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC;IAEvD,OAAO,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;QAEpC,MAAM,MAAM,GAAG,WAAW,EAAK,CAAC;QAEhC,MAAM,MAAM,GAAG,WAAW,EAAK,CAAC;QAGhC,MAAM,IAAI,GAAG,CAAC,OAAgB,EAAE,EAAE;YAChC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,CAAC;QAOF,MAAM,gBAAgB,GAAG,CAAC,SAA2B,EAAE,UAA4B,EAAE,EAAE;YACrF,MAAM,uBAAuB,GAAG,wBAAwB,CACtD,UAAU,EACV,CAAC,CAAI,EAAE,EAAE;gBACP,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAC;gBACxC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;oBAOvB,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACnD;qBAAM;oBAIL,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;iBAChD;YACH,CAAC,EACD,GAAG,EAAE;gBAEH,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAC1B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;gBAKxC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;gBAEtC,uBAAuB,aAAvB,uBAAuB,uBAAvB,uBAAuB,CAAE,WAAW,EAAE,CAAC;YACzC,CAAC,CACF,CAAC;YAEF,OAAO,uBAAuB,CAAC;QACjC,CAAC,CAAC;QAGF,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;QACnD,SAAS,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;AACL,CAAC;AAgBD,SAAS,WAAW;IAClB,OAAO;QACL,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,KAAK;KAChB,CAAC;AACJ,CAAC"}