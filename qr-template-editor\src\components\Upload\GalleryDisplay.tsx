import React, { useState } from 'react';
import {
  Box,
  Grid,
  Paper,
  IconButton,
  Typography,
  Dialog,
  DialogContent,
  DialogActions,
  <PERSON>ton,
  Chip,
  Tooltip
} from '@mui/material';
import { X, Eye, Download, Move } from 'lucide-react';

interface GalleryDisplayProps {
  images: string[];
  onRemove: (index: number) => void;
  onReorder?: (fromIndex: number, toIndex: number) => void;
  maxImages?: number;
  title?: string;
}

const GalleryDisplay: React.FC<GalleryDisplayProps> = ({
  images,
  onRemove,
  onReorder,
  maxImages = 10,
  title = "Gallery Images"
}) => {
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  const handlePreview = (image: string) => {
    setPreviewImage(image);
  };

  const handleClosePreview = () => {
    setPreviewImage(null);
  };

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    
    if (draggedIndex !== null && draggedIndex !== dropIndex && onReorder) {
      onReorder(draggedIndex, dropIndex);
    }
    
    setDraggedIndex(null);
  };

  const handleDownload = (image: string, index: number) => {
    const link = document.createElement('a');
    link.href = image;
    link.download = `gallery-image-${index + 1}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (images.length === 0) {
    return (
      <Paper
        sx={{
          p: 4,
          textAlign: 'center',
          bgcolor: 'grey.50',
          border: '2px dashed',
          borderColor: 'grey.300'
        }}
      >
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No Images Yet
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Upload some images to create your gallery
        </Typography>
      </Paper>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" fontWeight="bold">
          {title}
        </Typography>
        <Chip
          label={`${images.length}/${maxImages} images`}
          size="small"
          color={images.length >= maxImages ? 'warning' : 'primary'}
          variant="outlined"
        />
      </Box>

      <Grid container spacing={2}>
        {images.map((image, index) => (
          <Grid item xs={6} sm={4} md={3} key={index}>
            <Paper
              elevation={2}
              sx={{
                position: 'relative',
                borderRadius: 2,
                overflow: 'hidden',
                cursor: onReorder ? 'move' : 'default',
                transition: 'all 0.2s ease',
                '&:hover': {
                  elevation: 4,
                  '& .image-overlay': {
                    opacity: 1,
                  },
                },
                opacity: draggedIndex === index ? 0.5 : 1,
              }}
              draggable={!!onReorder}
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, index)}
            >
              {/* Image */}
              <Box
                component="img"
                src={image}
                alt={`Gallery ${index + 1}`}
                sx={{
                  width: '100%',
                  height: 150,
                  objectFit: 'cover',
                  display: 'block',
                }}
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />

              {/* Overlay with actions */}
              <Box
                className="image-overlay"
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  bgcolor: 'rgba(0,0,0,0.7)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 1,
                  opacity: 0,
                  transition: 'opacity 0.2s ease',
                }}
              >
                <Tooltip title="Preview">
                  <IconButton
                    size="small"
                    onClick={() => handlePreview(image)}
                    sx={{ color: 'white', bgcolor: 'rgba(255,255,255,0.2)' }}
                  >
                    <Eye size={16} />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Download">
                  <IconButton
                    size="small"
                    onClick={() => handleDownload(image, index)}
                    sx={{ color: 'white', bgcolor: 'rgba(255,255,255,0.2)' }}
                  >
                    <Download size={16} />
                  </IconButton>
                </Tooltip>

                {onReorder && (
                  <Tooltip title="Drag to reorder">
                    <IconButton
                      size="small"
                      sx={{ color: 'white', bgcolor: 'rgba(255,255,255,0.2)', cursor: 'move' }}
                    >
                      <Move size={16} />
                    </IconButton>
                  </Tooltip>
                )}

                <Tooltip title="Remove">
                  <IconButton
                    size="small"
                    onClick={() => onRemove(index)}
                    sx={{ color: 'white', bgcolor: 'rgba(255,0,0,0.3)' }}
                  >
                    <X size={16} />
                  </IconButton>
                </Tooltip>
              </Box>

              {/* Image number badge */}
              <Chip
                label={index + 1}
                size="small"
                sx={{
                  position: 'absolute',
                  top: 8,
                  left: 8,
                  bgcolor: 'rgba(0,0,0,0.7)',
                  color: 'white',
                  fontSize: '0.75rem',
                  height: 24,
                  '& .MuiChip-label': {
                    px: 1,
                  },
                }}
              />
            </Paper>
          </Grid>
        ))}
      </Grid>

      {/* Preview Dialog */}
      <Dialog
        open={!!previewImage}
        onClose={handleClosePreview}
        maxWidth="md"
        fullWidth
      >
        <DialogContent sx={{ p: 0 }}>
          {previewImage && (
            <Box
              component="img"
              src={previewImage}
              alt="Preview"
              sx={{
                width: '100%',
                height: 'auto',
                maxHeight: '80vh',
                objectFit: 'contain',
              }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePreview}>Close</Button>
          {previewImage && (
            <Button
              variant="contained"
              startIcon={<Download size={16} />}
              onClick={() => {
                const link = document.createElement('a');
                link.href = previewImage;
                link.download = 'gallery-image.jpg';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              Download
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GalleryDisplay;
