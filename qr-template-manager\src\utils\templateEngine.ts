/**
 * Template Engine - Renders templates with business data
 */

import type { Template, BusinessInstance, TemplateRenderData } from '../types/template';

export class TemplateEngine {
  
  /**
   * Render template with business data
   */
  renderTemplate(template: Template, business: BusinessInstance): string {
    const renderData = this.prepareRenderData(business);
    let html = template.htmlContent;

    // Replace simple variables
    html = this.replaceVariables(html, renderData);
    
    // Handle conditional sections
    html = this.handleConditionals(html, renderData);
    
    // Handle array loops
    html = this.handleArrayLoops(html, renderData);
    
    // Clean up any remaining template syntax
    html = this.cleanupTemplate(html);

    return html;
  }

  /**
   * Prepare business data for template rendering
   */
  private prepareRenderData(business: BusinessInstance): TemplateRenderData {
    const data = business.businessData;
    
    return {
      // Basic business info
      BUSINESS_NAME: data.businessName,
      SLOGAN: data.slogan || '',
      DESCRIPTION: data.description || '',
      
      // Contact info
      PHONE: data.phone,
      EMAIL: data.email,
      ADDRESS: data.address,
      HOURS: data.hours || '',
      WEBSITE: data.website || '',
      
      // Media
      LOGO_URL: data.logoUrl || '',
      FAVICON_URL: data.faviconUrl || '',
      
      // Social media
      FACEBOOK_URL: data.facebookUrl || '',
      INSTAGRAM_URL: data.instagramUrl || '',
      YELP_URL: data.yelpUrl || '',
      
      // Styling
      PRIMARY_COLOR: data.primaryColor || '#1976d2',
      SECONDARY_COLOR: data.secondaryColor || '#dc004e',
      HEADER_BACKGROUND_TYPE: data.headerBackgroundType || 'color',
      HEADER_BACKGROUND_VALUE: data.headerBackgroundValue || data.primaryColor || '#1976d2',
      HEADER_TEXT_COLOR: data.headerTextColor || '#ffffff',
      
      // Computed boolean flags
      HAS_LOGO: !!(data.logoUrl && data.logoUrl.trim()),
      HAS_SERVICES: data.services.length > 0,
      HAS_OFFERS: data.offers.length > 0,
      HAS_GALLERY: data.galleryImages.length > 0,
      HAS_SOCIAL_LINKS: !!(data.facebookUrl || data.instagramUrl || data.yelpUrl || data.customSocialLinks.length > 0),
      
      // Arrays for loops
      SERVICES: data.services.filter(s => s.isActive),
      OFFERS: data.offers.filter(o => o.isActive),
      GALLERY_IMAGES: data.galleryImages,
      CUSTOM_SOCIAL_LINKS: data.customSocialLinks
    };
  }

  /**
   * Replace simple template variables {{VARIABLE}}
   */
  private replaceVariables(html: string, data: TemplateRenderData): string {
    return html.replace(/\{\{([A-Z_]+)\}\}/g, (match, variable) => {
      const value = data[variable];
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Handle conditional sections {{#CONDITION}}...{{/CONDITION}}
   */
  private handleConditionals(html: string, data: TemplateRenderData): string {
    // Handle conditional sections
    return html.replace(/\{\{#([A-Z_]+)\}\}([\s\S]*?)\{\{\/\1\}\}/g, (match, condition, content) => {
      const value = data[condition];
      
      // Show content if condition is truthy
      if (value) {
        return content;
      }
      
      return '';
    });
  }

  /**
   * Handle array loops {{#ARRAY}}...{{/ARRAY}}
   */
  private handleArrayLoops(html: string, data: TemplateRenderData): string {
    const arrayVariables = ['SERVICES', 'OFFERS', 'GALLERY_IMAGES', 'CUSTOM_SOCIAL_LINKS'];
    
    arrayVariables.forEach(arrayVar => {
      const regex = new RegExp(`\\{\\{#${arrayVar}\\}\\}([\\s\\S]*?)\\{\\{\\/${arrayVar}\\}\\}`, 'g');
      
      html = html.replace(regex, (match, template) => {
        const array = data[arrayVar] as any[];
        
        if (!Array.isArray(array) || array.length === 0) {
          return '';
        }
        
        return array.map(item => {
          let itemHtml = template;
          
          // Replace item properties
          Object.keys(item).forEach(key => {
            const value = item[key];
            const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
            itemHtml = itemHtml.replace(regex, value !== undefined ? String(value) : '');
          });
          
          // Handle conditional properties within items
          itemHtml = itemHtml.replace(/\{\{#([a-zA-Z_]+)\}\}([\s\S]*?)\{\{\/\1\}\}/g, (match, condition, content) => {
            const value = item[condition];
            return value ? content : '';
          });
          
          return itemHtml;
        }).join('');
      });
    });
    
    return html;
  }

  /**
   * Clean up any remaining template syntax
   */
  private cleanupTemplate(html: string): string {
    // Remove any remaining conditional blocks
    html = html.replace(/\{\{#[^}]+\}\}/g, '');
    html = html.replace(/\{\{\/[^}]+\}\}/g, '');
    
    // Remove any remaining variables
    html = html.replace(/\{\{[^}]+\}\}/g, '');
    
    return html;
  }

  /**
   * Generate preview with sample data
   */
  generatePreview(template: Template): string {
    const sampleBusiness: BusinessInstance = {
      id: 'preview',
      businessName: 'Sample Business',
      templateId: template.id,
      templateVersion: template.version,
      businessData: {
        businessName: 'Sample Business',
        slogan: 'Your Success is Our Mission',
        description: 'We provide excellent services to help your business grow and succeed in today\'s competitive market.',
        phone: '(*************',
        email: '<EMAIL>',
        address: '123 Main Street, City, State 12345',
        hours: 'Mon-Fri: 9AM-6PM, Sat: 10AM-4PM',
        website: 'https://samplebusiness.com',
        logoUrl: 'https://via.placeholder.com/150x100/1976d2/ffffff?text=LOGO',
        faviconUrl: 'https://via.placeholder.com/32x32/1976d2/ffffff?text=F',
        galleryImages: [
          {
            id: '1',
            url: 'https://via.placeholder.com/300x200/1976d2/ffffff?text=Gallery+1',
            alt: 'Gallery Image 1',
            caption: 'Sample gallery image',
            order: 1
          },
          {
            id: '2',
            url: 'https://via.placeholder.com/300x200/dc004e/ffffff?text=Gallery+2',
            alt: 'Gallery Image 2',
            caption: 'Another gallery image',
            order: 2
          }
        ],
        services: [
          {
            id: '1',
            name: 'Premium Service',
            description: 'Our flagship service with premium features',
            price: '$99',
            duration: '60 min',
            category: 'premium',
            isActive: true,
            order: 1
          },
          {
            id: '2',
            name: 'Standard Service',
            description: 'Quality service at an affordable price',
            price: '$59',
            duration: '45 min',
            category: 'standard',
            isActive: true,
            order: 2
          }
        ],
        offers: [
          {
            id: '1',
            title: 'New Customer Special',
            description: 'Get 20% off your first service',
            originalPrice: '$100',
            discountPrice: '$80',
            discountPercent: 20,
            validUntil: '2024-12-31',
            isActive: true,
            order: 1
          }
        ],
        facebookUrl: 'https://facebook.com/samplebusiness',
        instagramUrl: 'https://instagram.com/samplebusiness',
        yelpUrl: 'https://yelp.com/biz/samplebusiness',
        customSocialLinks: [
          {
            id: '1',
            platform: 'Twitter',
            url: 'https://twitter.com/samplebusiness',
            icon: 'twitter',
            order: 1
          }
        ],
        primaryColor: '#1976d2',
        secondaryColor: '#dc004e',
        headerBackgroundType: 'color',
        headerBackgroundValue: '#1976d2',
        headerTextColor: '#ffffff'
      },
      customizations: {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      viewCount: 0,
      isActive: true,
      isPublic: false,
      exportHistory: []
    };

    return this.renderTemplate(template, sampleBusiness);
  }

  /**
   * Validate template syntax
   */
  validateTemplateSyntax(html: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check for unmatched conditional blocks
    const conditionalRegex = /\{\{#([A-Z_]+)\}\}/g;
    const endConditionalRegex = /\{\{\/([A-Z_]+)\}\}/g;
    
    const openTags: string[] = [];
    let match;
    
    // Find all opening tags
    while ((match = conditionalRegex.exec(html)) !== null) {
      openTags.push(match[1]);
    }
    
    // Find all closing tags
    const closeTags: string[] = [];
    while ((match = endConditionalRegex.exec(html)) !== null) {
      closeTags.push(match[1]);
    }
    
    // Check for unmatched tags
    openTags.forEach(tag => {
      if (!closeTags.includes(tag)) {
        errors.push(`Unmatched opening tag: {{#${tag}}}`);
      }
    });
    
    closeTags.forEach(tag => {
      if (!openTags.includes(tag)) {
        errors.push(`Unmatched closing tag: {{/${tag}}}`);
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const templateEngine = new TemplateEngine();
