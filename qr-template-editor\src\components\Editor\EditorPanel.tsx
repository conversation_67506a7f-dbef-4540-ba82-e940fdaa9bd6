import React from 'react';
import { Box, Paper } from '@mui/material';
import type { BusinessData, BasicTab, TemplateType } from '../../types/index';
import EditorTabs from './EditorTabs';
import BasicInfoTab from './BasicInfoTab';
import ContactTab from './ContactTab';
import DesignTab from './DesignTab';
import MediaTab from './MediaTab';
import BusinessTab from './BusinessTab';
import SocialTab from './SocialTab';
import AdvancedTab from './AdvancedTab';

interface EditorPanelProps {
  businessData: BusinessData;
  currentTab: BasicTab;
  onTabChange: (tab: BasicTab) => void;
  onUpdate: (field: keyof BusinessData, value: any) => void;
  onApplyTemplate: (template: TemplateType) => void;
}

const EditorPanel: React.FC<EditorPanelProps> = ({
  businessData,
  currentTab,
  onTabChange,
  onUpdate,
  onApplyTemplate,
}) => {
  const renderTabContent = () => {
    switch (currentTab) {
      case 'basic-info':
        return <BasicInfoTab businessData={businessData} onUpdate={onUpdate} />;
      case 'media':
        return <MediaTab businessData={businessData} onUpdate={onUpdate} />;
      case 'contact':
        return <ContactTab businessData={businessData} onUpdate={onUpdate} />;
      case 'business':
        return <BusinessTab businessData={businessData} onUpdate={onUpdate} />;
      case 'social':
        return <SocialTab businessData={businessData} onUpdate={onUpdate} />;
      case 'design':
        return <DesignTab businessData={businessData} onUpdate={onUpdate} onApplyTemplate={onApplyTemplate} />;
      case 'advanced':
        return <AdvancedTab businessData={businessData} onUpdate={onUpdate} />;
      default:
        return <BasicInfoTab businessData={businessData} onUpdate={onUpdate} />;
    }
  };

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 3, pb: 0 }}>
        <EditorTabs currentTab={currentTab} onTabChange={onTabChange} />
      </Box>
      
      <Box sx={{ flex: 1, overflow: 'auto', p: 3, pt: 0 }}>
        {renderTabContent()}
      </Box>
    </Paper>
  );
};

export default EditorPanel;
