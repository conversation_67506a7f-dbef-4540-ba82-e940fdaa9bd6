import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ield,
  Typography,
  Box,
  Alert,
  Button,
  IconButton,
  Divider
} from '@mui/material';
import { Briefcase, Plus, X, DollarSign, Clock } from 'lucide-react';
import type { BusinessData, Service, Offer } from '../../types/index';

interface BusinessTabProps {
  businessData: BusinessData;
  onUpdate: (field: keyof BusinessData, value: any) => void;
}

const BusinessTab: React.FC<BusinessTabProps> = ({ businessData, onUpdate }) => {
  const [newService, setNewService] = React.useState<Partial<Service>>({
    name: '',
    description: '',
    price: '',
    duration: ''
  });

  const [newOffer, setNewOffer] = React.useState<Partial<Offer>>({
    title: '',
    description: '',
    validUntil: '',
    discount: ''
  });

  const handleAddService = () => {
    if (newService.name && newService.description && newService.price) {
      const service: Service = {
        id: Date.now().toString(),
        name: newService.name,
        description: newService.description,
        price: newService.price,
        duration: newService.duration || undefined
      };
      
      const updatedServices = [...businessData.services, service];
      onUpdate('services', updatedServices);
      setNewService({ name: '', description: '', price: '', duration: '' });
    }
  };

  const handleRemoveService = (id: string) => {
    const updatedServices = businessData.services.filter(service => service.id !== id);
    onUpdate('services', updatedServices);
  };

  const handleAddOffer = () => {
    if (newOffer.title && newOffer.description) {
      const offer: Offer = {
        id: Date.now().toString(),
        title: newOffer.title,
        description: newOffer.description,
        validUntil: newOffer.validUntil || undefined,
        discount: newOffer.discount || undefined
      };
      
      const updatedOffers = [...businessData.offers, offer];
      onUpdate('offers', updatedOffers);
      setNewOffer({ title: '', description: '', validUntil: '', discount: '' });
    }
  };

  const handleRemoveOffer = (id: string) => {
    const updatedOffers = businessData.offers.filter(offer => offer.id !== id);
    onUpdate('offers', updatedOffers);
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              bgcolor: 'primary.main',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            <Briefcase size={20} color="white" />
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Services & Offers
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Showcase your services and special offers
            </Typography>
          </Box>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          Add your services and special offers to help customers understand what you provide.
        </Alert>

        {/* Services Section */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Services
          </Typography>
          
          {/* Add new service form */}
          <Box sx={{ p: 2, border: '1px dashed #ccc', borderRadius: 1, mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Add New Service
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Service Name"
                value={newService.name}
                onChange={(e) => setNewService(prev => ({ ...prev, name: e.target.value }))}
                size="small"
                placeholder="e.g., Manicure"
              />
              <TextField
                label="Description"
                value={newService.description}
                onChange={(e) => setNewService(prev => ({ ...prev, description: e.target.value }))}
                size="small"
                multiline
                rows={2}
                placeholder="Brief description of the service"
              />
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  label="Price"
                  value={newService.price}
                  onChange={(e) => setNewService(prev => ({ ...prev, price: e.target.value }))}
                  size="small"
                  placeholder="$25"
                  InputProps={{
                    startAdornment: <DollarSign size={16} />
                  }}
                />
                <TextField
                  label="Duration (optional)"
                  value={newService.duration}
                  onChange={(e) => setNewService(prev => ({ ...prev, duration: e.target.value }))}
                  size="small"
                  placeholder="30 min"
                  InputProps={{
                    startAdornment: <Clock size={16} />
                  }}
                />
              </Box>
              <Button
                variant="contained"
                startIcon={<Plus size={16} />}
                onClick={handleAddService}
                disabled={!newService.name || !newService.description || !newService.price}
                size="small"
              >
                Add Service
              </Button>
            </Box>
          </Box>

          {/* Current services */}
          {businessData.services.length > 0 ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {businessData.services.map((service) => (
                <Box
                  key={service.id}
                  sx={{
                    p: 2,
                    border: '1px solid #e0e0e0',
                    borderRadius: 1,
                    bgcolor: '#f9f9f9',
                  }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {service.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {service.description}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 2 }}>
                        <Typography variant="body2" fontWeight="bold" color="primary">
                          {service.price}
                        </Typography>
                        {service.duration && (
                          <Typography variant="body2" color="text.secondary">
                            {service.duration}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleRemoveService(service.id)}
                    >
                      <X size={16} />
                    </IconButton>
                  </Box>
                </Box>
              ))}
            </Box>
          ) : (
            <Alert severity="info">
              No services added yet. Add some services to showcase what you offer!
            </Alert>
          )}
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Offers Section */}
        <Box>
          <Typography variant="h6" gutterBottom>
            Special Offers
          </Typography>
          
          {/* Add new offer form */}
          <Box sx={{ p: 2, border: '1px dashed #ccc', borderRadius: 1, mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Add New Offer
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Offer Title"
                value={newOffer.title}
                onChange={(e) => setNewOffer(prev => ({ ...prev, title: e.target.value }))}
                size="small"
                placeholder="e.g., First Time Customer Special"
              />
              <TextField
                label="Description"
                value={newOffer.description}
                onChange={(e) => setNewOffer(prev => ({ ...prev, description: e.target.value }))}
                size="small"
                multiline
                rows={2}
                placeholder="Details about the special offer"
              />
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  label="Discount (optional)"
                  value={newOffer.discount}
                  onChange={(e) => setNewOffer(prev => ({ ...prev, discount: e.target.value }))}
                  size="small"
                  placeholder="20% off"
                />
                <TextField
                  label="Valid Until (optional)"
                  value={newOffer.validUntil}
                  onChange={(e) => setNewOffer(prev => ({ ...prev, validUntil: e.target.value }))}
                  size="small"
                  placeholder="Dec 31, 2024"
                />
              </Box>
              <Button
                variant="contained"
                startIcon={<Plus size={16} />}
                onClick={handleAddOffer}
                disabled={!newOffer.title || !newOffer.description}
                size="small"
              >
                Add Offer
              </Button>
            </Box>
          </Box>

          {/* Current offers */}
          {businessData.offers.length > 0 ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {businessData.offers.map((offer) => (
                <Box
                  key={offer.id}
                  sx={{
                    p: 2,
                    border: '1px solid #e0e0e0',
                    borderRadius: 1,
                    bgcolor: '#fff3cd',
                  }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {offer.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {offer.description}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 2 }}>
                        {offer.discount && (
                          <Typography variant="body2" fontWeight="bold" color="warning.main">
                            {offer.discount}
                          </Typography>
                        )}
                        {offer.validUntil && (
                          <Typography variant="body2" color="text.secondary">
                            Valid until: {offer.validUntil}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleRemoveOffer(offer.id)}
                    >
                      <X size={16} />
                    </IconButton>
                  </Box>
                </Box>
              ))}
            </Box>
          ) : (
            <Alert severity="info">
              No special offers added yet. Add some offers to attract customers!
            </Alert>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default BusinessTab;
