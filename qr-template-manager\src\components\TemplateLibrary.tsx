import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardActions,
  Typo<PERSON>,
  Button,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Grid
} from '@mui/material';
import { Download, Eye, Edit, Star, Trash2 } from 'lucide-react';
import type { Template, TemplateCategory } from '../types/template';
import TemplatePreview from './TemplatePreview';
import TemplateEditor from './TemplateEditor';
import TemplateEditorPage from './TemplateEditorPage';
import ConfirmDialog from './ConfirmDialog';
import NotificationSnackbar from './NotificationSnackbar';

const TemplateLibrary: React.FC = () => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<TemplateCategory | ''>('');
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [editTemplate, setEditTemplate] = useState<Template | null>(null);
  const [editorOpen, setEditorOpen] = useState(false);
  const [fullEditorOpen, setFullEditorOpen] = useState(false);
  const [deleteTemplate, setDeleteTemplate] = useState<Template | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success'
  });

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3001/templates');
      if (!response.ok) throw new Error('Failed to load templates');
      const data = await response.json();
      setTemplates(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handlePreview = (template: Template) => {
    setPreviewTemplate(template);
    setPreviewOpen(true);
  };

  const handleEdit = (template: Template) => {
    setEditTemplate(template);
    setFullEditorOpen(true);
  };

  const handleQuickEdit = (template: Template) => {
    setEditTemplate(template);
    setEditorOpen(true);
  };

  const handleSaveTemplate = async (updatedTemplate: Template) => {
    try {
      const response = await fetch(`http://localhost:3001/templates/${updatedTemplate.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedTemplate)
      });

      if (!response.ok) throw new Error('Failed to update template');

      setEditorOpen(false);
      setFullEditorOpen(false);
      setEditTemplate(null);
      loadTemplates();
      setNotification({
        open: true,
        message: 'Template updated successfully!',
        severity: 'success'
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update template');
    }
  };

  const handleDeleteTemplate = (template: Template) => {
    setDeleteTemplate(template);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteTemplate = async () => {
    if (!deleteTemplate) return;

    try {
      const response = await fetch(`http://localhost:3001/templates/${deleteTemplate.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete template');

      setDeleteDialogOpen(false);
      setDeleteTemplate(null);
      loadTemplates();
      setNotification({
        open: true,
        message: 'Template deleted successfully!',
        severity: 'success'
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete template');
    }
  };

  const handleDownload = (template: Template) => {
    // TODO: Implement download functionality
    const blob = new Blob([template.htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${template.name.toLowerCase().replace(/\s+/g, '-')}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button onClick={loadTemplates} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  // Show full editor page if editing
  if (fullEditorOpen && editTemplate) {
    return (
      <TemplateEditorPage
        template={editTemplate}
        onBack={() => {
          setFullEditorOpen(false);
          setEditTemplate(null);
        }}
        onSave={handleSaveTemplate}
      />
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Template Library
      </Typography>
      
      {/* Search and Filter */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <TextField
          label="Search templates"
          variant="outlined"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          sx={{ minWidth: 300 }}
        />
        
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Category</InputLabel>
          <Select
            value={selectedCategory}
            label="Category"
            onChange={(e) => setSelectedCategory(e.target.value as TemplateCategory | '')}
          >
            <MenuItem value="">All Categories</MenuItem>
            <MenuItem value="business">Business</MenuItem>
            <MenuItem value="restaurant">Restaurant</MenuItem>
            <MenuItem value="beauty">Beauty</MenuItem>
            <MenuItem value="healthcare">Healthcare</MenuItem>
            <MenuItem value="retail">Retail</MenuItem>
            <MenuItem value="service">Service</MenuItem>
            <MenuItem value="event">Event</MenuItem>
            <MenuItem value="portfolio">Portfolio</MenuItem>
            <MenuItem value="custom">Custom</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Templates Grid */}
      <Grid container spacing={3}>
        {filteredTemplates.map((template) => (
          <Grid size={{ xs: 12, sm: 6, md: 4 }} key={template.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* Preview Image */}
              {template.previewImage ? (
                <Box
                  component="img"
                  src={template.previewImage}
                  alt={template.name}
                  sx={{
                    height: 200,
                    objectFit: 'cover',
                    bgcolor: 'grey.100'
                  }}
                />
              ) : (
                <Box
                  sx={{
                    height: 200,
                    bgcolor: 'grey.100',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Typography variant="body2" color="text.secondary">
                    No Preview
                  </Typography>
                </Box>
              )}

              <CardContent sx={{ flexGrow: 1 }}>
                <Typography gutterBottom variant="h6" component="div">
                  {template.name}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {template.description}
                </Typography>

                {/* Category and Status */}
                <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip 
                    label={template.category} 
                    size="small" 
                    color="primary" 
                    variant="outlined"
                  />
                  <Chip 
                    label={template.status} 
                    size="small" 
                    color={template.status === 'published' ? 'success' : 'default'}
                  />
                </Box>

                {/* Features */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Features:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mt: 0.5 }}>
                    {template.metadata.features.slice(0, 3).map((feature) => (
                      <Chip 
                        key={feature} 
                        label={feature} 
                        size="small" 
                        variant="outlined"
                      />
                    ))}
                    {template.metadata.features.length > 3 && (
                      <Chip 
                        label={`+${template.metadata.features.length - 3} more`} 
                        size="small" 
                        variant="outlined"
                      />
                    )}
                  </Box>
                </Box>

                {/* Rating and Downloads */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  {template.rating && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Star size={16} fill="currentColor" />
                      <Typography variant="body2">
                        {template.rating}
                      </Typography>
                    </Box>
                  )}
                  <Typography variant="body2" color="text.secondary">
                    {template.downloadCount} downloads
                  </Typography>
                </Box>
              </CardContent>

              <CardActions sx={{ justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    startIcon={<Eye size={16} />}
                    onClick={() => handlePreview(template)}
                  >
                    Preview
                  </Button>
                  <Button
                    size="small"
                    startIcon={<Edit size={16} />}
                    onClick={() => handleEdit(template)}
                  >
                    Edit
                  </Button>
                  <Button
                    size="small"
                    startIcon={<Download size={16} />}
                    onClick={() => handleDownload(template)}
                  >
                    Download
                  </Button>
                </Box>
                <Button
                  size="small"
                  color="error"
                  startIcon={<Trash2 size={16} />}
                  onClick={() => handleDeleteTemplate(template)}
                >
                  Delete
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {filteredTemplates.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No templates found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try adjusting your search criteria
          </Typography>
        </Box>
      )}

      <TemplatePreview
        template={previewTemplate}
        open={previewOpen}
        onClose={() => {
          setPreviewOpen(false);
          setPreviewTemplate(null);
        }}
      />

      <TemplateEditor
        template={editTemplate}
        open={editorOpen}
        onClose={() => {
          setEditorOpen(false);
          setEditTemplate(null);
        }}
        onSave={handleSaveTemplate}
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Template"
        message={`Are you sure you want to delete "${deleteTemplate?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        severity="error"
        onConfirm={confirmDeleteTemplate}
        onCancel={() => {
          setDeleteDialogOpen(false);
          setDeleteTemplate(null);
        }}
      />

      <NotificationSnackbar
        open={notification.open}
        message={notification.message}
        severity={notification.severity}
        onClose={() => setNotification({ ...notification, open: false })}
      />
    </Box>
  );
};

export default TemplateLibrary;
