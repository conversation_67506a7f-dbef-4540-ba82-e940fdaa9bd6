{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/editor/advancedtab.tsx", "../../src/components/editor/basicinfotab.tsx", "../../src/components/editor/businesstab.tsx", "../../src/components/editor/contacttab.tsx", "../../src/components/editor/designtab.tsx", "../../src/components/editor/editorpanel.tsx", "../../src/components/editor/editortabs.tsx", "../../src/components/editor/headercustomization.tsx", "../../src/components/editor/mediatab.tsx", "../../src/components/editor/socialtab.tsx", "../../src/components/layout/header.tsx", "../../src/components/preview/mobilepreview.tsx", "../../src/components/upload/gallerydisplay.tsx", "../../src/components/upload/imageupload.tsx", "../../src/components/upload/simpleimageupload.tsx", "../../src/hooks/usebusinessdata.ts", "../../src/hooks/useeditorstate.ts", "../../src/services/api-clean.ts", "../../src/services/api.ts", "../../src/types/index.ts", "../../src/utils/base64utils.ts", "../../src/utils/debugtemplate.ts", "../../src/utils/imagememorymanager.ts", "../../src/utils/imageutils.ts", "../../src/utils/templateengine.ts", "../../src/utils/templategenerator.ts", "../../src/utils/validation.ts"], "version": "5.8.3"}