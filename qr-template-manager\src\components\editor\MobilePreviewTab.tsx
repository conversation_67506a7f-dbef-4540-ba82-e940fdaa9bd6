import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
  Button,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import { Smartphone, Tablet, Monitor, RefreshCw, ExternalLink } from 'lucide-react';

interface MobilePreviewTabProps {
  previewHtml: string;
  businessData: any;
}

const MobilePreviewTab: React.FC<MobilePreviewTabProps> = ({
  previewHtml,
  businessData
}) => {
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');
  const [refreshKey, setRefreshKey] = useState(0);

  const deviceSizes = {
    mobile: { width: 375, height: 667 },
    tablet: { width: 768, height: 1024 },
    desktop: { width: 1200, height: 800 }
  };

  const handleDeviceChange = (
    event: React.MouseEvent<HTMLElement>,
    newDevice: 'mobile' | 'tablet' | 'desktop' | null,
  ) => {
    if (newDevice !== null) {
      setDeviceType(newDevice);
    }
  };

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const openInNewWindow = () => {
    const newWindow = window.open('', '_blank', 'width=400,height=700');
    if (newWindow) {
      newWindow.document.write(previewHtml);
      newWindow.document.close();
    }
  };

  const currentSize = deviceSizes[deviceType];

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Mobile Preview
      </Typography>

      {/* Device Controls */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="subtitle1">Device:</Typography>
            <ToggleButtonGroup
              value={deviceType}
              exclusive
              onChange={handleDeviceChange}
              size="small"
            >
              <ToggleButton value="mobile">
                <Smartphone size={16} />
                <Typography variant="body2" sx={{ ml: 1 }}>Mobile</Typography>
              </ToggleButton>
              <ToggleButton value="tablet">
                <Tablet size={16} />
                <Typography variant="body2" sx={{ ml: 1 }}>Tablet</Typography>
              </ToggleButton>
              <ToggleButton value="desktop">
                <Monitor size={16} />
                <Typography variant="body2" sx={{ ml: 1 }}>Desktop</Typography>
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh Preview">
              <IconButton onClick={handleRefresh}>
                <RefreshCw size={16} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Open in New Window">
              <IconButton onClick={openInNewWindow}>
                <ExternalLink size={16} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Resolution: {currentSize.width} × {currentSize.height}px
        </Typography>
      </Paper>

      {/* Preview Container */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
        <Paper
          elevation={3}
          sx={{
            width: currentSize.width,
            height: currentSize.height,
            maxWidth: '100%',
            maxHeight: '70vh',
            overflow: 'hidden',
            borderRadius: deviceType === 'mobile' ? '20px' : '8px',
            border: deviceType === 'mobile' ? '8px solid #333' : 'none',
            position: 'relative'
          }}
        >
          {/* Device Frame for Mobile */}
          {deviceType === 'mobile' && (
            <>
              {/* Home Indicator */}
              <Box
                sx={{
                  position: 'absolute',
                  bottom: 4,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 134,
                  height: 5,
                  bgcolor: '#333',
                  borderRadius: '2.5px',
                  zIndex: 1000
                }}
              />
              {/* Notch */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 150,
                  height: 25,
                  bgcolor: '#333',
                  borderRadius: '0 0 15px 15px',
                  zIndex: 1000
                }}
              />
            </>
          )}

          {/* Preview Content */}
          <Box
            component="iframe"
            key={refreshKey}
            srcDoc={previewHtml}
            sx={{
              width: '100%',
              height: '100%',
              border: 'none',
              borderRadius: deviceType === 'mobile' ? '12px' : '0px'
            }}
          />
        </Paper>
      </Box>

      {/* Preview Info */}
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>Preview Tips:</strong>
          <br />• This preview shows how your QR page will look on different devices
          <br />• Use the device buttons to switch between mobile, tablet, and desktop views
          <br />• Click the refresh button to reload the preview after making changes
          <br />• Open in new window to test interactions and scrolling
        </Typography>
      </Alert>

      {/* Business Info Summary */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Current Business Data
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
          <Box>
            <Typography variant="subtitle2" color="text.secondary">Business Name</Typography>
            <Typography variant="body2">{businessData.businessName || 'Not set'}</Typography>
          </Box>
          <Box>
            <Typography variant="subtitle2" color="text.secondary">Phone</Typography>
            <Typography variant="body2">{businessData.phone || 'Not set'}</Typography>
          </Box>
          <Box>
            <Typography variant="subtitle2" color="text.secondary">Email</Typography>
            <Typography variant="body2">{businessData.email || 'Not set'}</Typography>
          </Box>
          <Box>
            <Typography variant="subtitle2" color="text.secondary">Services</Typography>
            <Typography variant="body2">{businessData.services?.length || 0} services</Typography>
          </Box>
          <Box>
            <Typography variant="subtitle2" color="text.secondary">Offers</Typography>
            <Typography variant="body2">{businessData.offers?.length || 0} offers</Typography>
          </Box>
          <Box>
            <Typography variant="subtitle2" color="text.secondary">Gallery</Typography>
            <Typography variant="body2">{businessData.galleryImages?.length || 0} images</Typography>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default MobilePreviewTab;
