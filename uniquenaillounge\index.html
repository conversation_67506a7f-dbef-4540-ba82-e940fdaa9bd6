<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unique Nails Lounge - Premium Nail Care</title>
    <link rel="icon" type="image/webp" href="https://uniquenaillounge.com/wp-content/uploads/2025/05/Unique-Nails-Lounge-Favicon-h40.webp#7">
    <link rel="shortcut icon" type="image/webp" href="https://uniquenaillounge.com/wp-content/uploads/2025/05/Unique-Nails-Lounge-Favicon-h40.webp#7">
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config = { theme: { extend: { colors: { primary: '#d4af37', secondary: '#222222' }, borderRadius: { 'none': '0px', 'sm': '4px', DEFAULT: '8px', 'md': '12px', 'lg': '16px', 'xl': '20px', '2xl': '24px', '3xl': '32px', 'full': '9999px', 'button': '8px' } } } }</script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        body {
            font-family: 'Playfair Display', serif;
            background-color: #f9f5eb;
        }

        .gold-gradient {
            background: linear-gradient(135deg, #d4af37 0%, #f9e076 50%, #d4af37 100%);
        }

        /* Modern Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .animate-slideInLeft {
            animation: slideInLeft 0.6s ease-out forwards;
        }

        .animate-pulse-slow {
            animation: pulse 2s ease-in-out infinite;
        }

        .shimmer {
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            background-size: 200px 100%;
            animation: shimmer 2s infinite;
        }

        .hover-scale {
            transition: all 0.3s ease;
        }

        .hover-scale:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stagger-animation > * {
            opacity: 0;
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
        .stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
        .stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
        .stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
    </style>
</head>

<body class="bg-white">
    <!-- Main Content -->
    <main>
        <!-- Banner -->
        <div class="relative w-full h-48 bg-secondary overflow-hidden">
            <!-- Thay thế bằng hình ảnh của riêng bạn hoặc gradient background -->
            <div class="w-full h-full gold-gradient shimmer"></div>
            <div class="absolute inset-0 bg-black bg-opacity-40 flex flex-col items-center justify-center">
                <img src="https://uniquenaillounge.com/wp-content/uploads/2025/05/Unique-Nails-Lounge-Logo-h300.webp"
                    alt="Unique Nails Lounge Logo" class="h-16 mb-1 animate-pulse-slow">
                <h1 class="text-white text-xl font-semibold tracking-wider animate-fadeInUp">UNIQUE NAILS LOUNGE</h1>
            </div>
        </div>
        <!-- Connect Section -->
        <div class="px-5 py-6 bg-white">
            <h2 class="text-2xl font-semibold text-secondary mb-2 animate-slideInLeft">Connect with us</h2>
            <p class="text-gray-600 mb-6 animate-fadeInUp">Follow us and get updates delivered to your favorite social media channel.
                Stay informed about our latest promotions and nail art trends.</p>
            <!-- Social Media Links -->
            <div class="space-y-4 stagger-animation">
                <!-- Facebook -->
                <a href="https://www.facebook.com/profile.php?id=100091754925855" target="_blank"
                    class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-[#1877F2] rounded-full mr-4">
                        <i class="ri-facebook-fill ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Facebook</h3>
                        <p class="text-sm text-gray-500">Visit our Facebook page</p>
                    </div>
                </a>
                <!-- Instagram -->
                <a href="#" target="_blank"
                    class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center rounded-full mr-4"
                        style="background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);">
                        <i class="ri-instagram-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Instagram</h3>
                        <p class="text-sm text-gray-500">Follow our nail art</p>
                    </div>
                </a>
                <!-- Google Review -->
                <a href="https://maps.app.goo.gl/U9buMXZH1SWxdSzc9" target="_blank"
                    class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div
                        class="w-10 h-10 flex items-center justify-center bg-white rounded-full mr-4 border border-gray-200">
                        <i class="ri-google-fill ri-lg" style="color: #4285F4;"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Google Review</h3>
                        <p class="text-sm text-gray-500">Review us on Google</p>
                    </div>
                </a>
                <!-- Yelp Review -->
                <a href="#" target="_blank"
                    class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-[#D32323] rounded-full mr-4">
                        <i class="ri-yelp-fill ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Yelp Review</h3>
                        <p class="text-sm text-gray-500">Review us on Yelp</p>
                    </div>
                </a>
            </div>
        </div>
        <!-- Action Buttons -->
        <div class="px-5 py-6 bg-gray-50">
            <h2 class="text-2xl font-semibold text-secondary mb-6 animate-slideInLeft">Our Services</h2>
            <div class="stagger-animation">
                <!-- Visit Website -->
                <a href="https://uniquenaillounge.com/" target="_blank"
                    class="flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4">
                        <i class="ri-global-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Visit our website</h3>
                        <p class="text-sm text-gray-500">uniquenaillounge.com</p>
                    </div>
                </a>
                <!-- Book Appointment -->
                <a href="https://www.lldtek.org/salon/appt/VkU1Zk1URXpNVFE9" target="_blank"
                    class="flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4">
                        <i class="ri-calendar-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Book an appointment</h3>
                        <p class="text-sm text-gray-500">Online booking available</p>
                    </div>
                </a>
                <!-- Call Now -->
                <a href="tel:+16156755494"
                    class="flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4">
                        <i class="ri-phone-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Call us now</h3>
                        <p class="text-sm text-gray-500">(*************</p>
                    </div>
                </a>
            </div>
        </div>
        <!-- Contact Info -->
        <div class="px-5 py-6 bg-gray-50">
            <h2 class="text-2xl font-semibold text-secondary mb-4 animate-slideInLeft">Visit Us</h2>
            <div class="bg-white p-4 rounded-lg shadow-sm mb-4 animate-fadeInUp hover-scale">
                <div class="flex items-start mb-3">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                        <i class="ri-map-pin-line text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Address</h3>
                        <p class="text-gray-600">2021 Gorden Crossing, Ste #150<br>Gallatin, TN 37066</p>
                    </div>
                </div>
                <div class="flex items-start mb-3">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                        <i class="ri-time-line text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Hours</h3>
                        <p class="text-gray-600">Mon - Fri: 9:00 AM – 8:00 PM<br>Saturday: 9:00 AM – 6:00 PM<br>Sunday: 11:00 AM – 5:00 PM</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                        <i class="ri-phone-line text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Phone</h3>
                        <p class="text-gray-600">(*************</p>
                    </div>
                </div>
            </div>
            <div class="h-48 rounded-lg overflow-hidden shadow-sm animate-fadeInUp">
                <!-- Google Maps Embed -->
                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3229.123456789!2d-86.4466667!3d36.3833333!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzYuMzgzMzMzMywtODYuNDQ2NjY2Nw!5e0!3m2!1sen!2sus!4v1234567890123!5m2!1sen!2sus"
                       width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"
                       referrerpolicy="no-referrer-when-downgrade"></iframe>
            </div>
            <a href="https://maps.app.goo.gl/U9buMXZH1SWxdSzc9" target="_blank"
                class="w-full mt-4 py-3 bg-primary text-white font-medium rounded-button flex items-center justify-center cursor-pointer hover-scale">
                <i class="ri-navigation-line ri-lg mr-2"></i>
                <span>Get Directions</span>
            </a>
        </div>
    </main>
</body>

</html>