import React from 'react';
import {
  Card,
  CardContent,
  TextField,
  Typography,
  Box,
  Alert,
  InputAdornment,
  IconButton
} from '@mui/material';
import { Phone, Mail, MapPin, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import type { BusinessData, ValidationResult } from '../../types/index';
import { validateRequired, validateEmail, validatePhone, formatPhoneNumber } from '../../utils/validation';

interface ContactTabProps {
  businessData: BusinessData;
  onUpdate: (field: keyof BusinessData, value: string) => void;
}

const ContactTab: React.FC<ContactTabProps> = ({ businessData, onUpdate }) => {
  const [validations, setValidations] = React.useState<{ [key: string]: ValidationResult }>({});

  const validateField = (field: keyof BusinessData, value: string) => {
    let validation: ValidationResult;
    
    switch (field) {
      case 'phone':
        validation = validatePhone(value);
        break;
      case 'email':
        validation = validateEmail(value);
        break;
      case 'address':
        validation = validateRequired(value, 'Address');
        break;
      case 'hours':
        validation = validateRequired(value, 'Business Hours');
        break;
      default:
        validation = { isValid: true, message: '', type: 'success' };
    }
    
    setValidations(prev => ({ ...prev, [field]: validation }));
    return validation;
  };

  const handleFieldChange = (field: keyof BusinessData, value: string) => {
    onUpdate(field, value);
    validateField(field, value);
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value);
    handleFieldChange('phone', formatted);
  };

  const getValidationProps = (field: keyof BusinessData) => {
    const validation = validations[field];
    if (!validation) return {};

    return {
      error: !validation.isValid,
      helperText: validation.message,
      InputProps: {
        endAdornment: validation.message && (
          <InputAdornment position="end">
            {validation.isValid ? (
              <CheckCircle size={16} color="green" />
            ) : (
              <AlertCircle size={16} color="red" />
            )}
          </InputAdornment>
        ),
      },
    };
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              bgcolor: 'success.main',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            <Phone size={20} color="white" />
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Contact Information
            </Typography>
            <Typography variant="body2" color="text.secondary">
              How customers can reach you
            </Typography>
          </Box>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          Provide accurate contact information so customers can easily reach your business.
        </Alert>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          <TextField
            label="Phone Number"
            value={businessData.phone}
            onChange={(e) => handlePhoneChange(e.target.value)}
            required
            fullWidth
            placeholder="(*************"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Phone size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('phone').InputProps,
            }}
            {...getValidationProps('phone')}
          />

          <TextField
            label="Email Address"
            value={businessData.email}
            onChange={(e) => handleFieldChange('email', e.target.value)}
            fullWidth
            placeholder="<EMAIL>"
            type="email"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Mail size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('email').InputProps,
            }}
            {...getValidationProps('email')}
          />

          <TextField
            label="Business Address"
            value={businessData.address}
            onChange={(e) => handleFieldChange('address', e.target.value)}
            required
            fullWidth
            multiline
            rows={3}
            placeholder="2021 Gorden Crossing, Ste #150&#10;Gallatin, TN 37066"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 1 }}>
                  <MapPin size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('address').InputProps,
            }}
            {...getValidationProps('address')}
          />

          <TextField
            label="Business Hours"
            value={businessData.hours}
            onChange={(e) => handleFieldChange('hours', e.target.value)}
            required
            fullWidth
            multiline
            rows={4}
            placeholder="Mon - Fri: 9:00 AM – 8:00 PM&#10;Saturday: 9:00 AM – 6:00 PM&#10;Sunday: 11:00 AM – 5:00 PM"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" sx={{ alignSelf: 'flex-start', mt: 1 }}>
                  <Clock size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('hours').InputProps,
            }}
            {...getValidationProps('hours')}
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default ContactTab;
