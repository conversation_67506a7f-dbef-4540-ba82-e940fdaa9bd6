import { useState, useCallback } from 'react';
import type { EditorView, BasicTab, EditorState } from '../types/index';

const defaultEditorState: EditorState = {
  currentView: 'basic',
  currentBasicTab: 'basic-info',
  isPreviewOpen: true,
  previewDevice: 'mobile'
};

export const useEditorState = () => {
  const [editorState, setEditorState] = useState<EditorState>(defaultEditorState);

  const setCurrentView = useCallback((view: EditorView) => {
    setEditorState(prev => ({ ...prev, currentView: view }));
  }, []);

  const setCurrentBasicTab = useCallback((tab: BasicTab) => {
    setEditorState(prev => ({ ...prev, currentBasicTab: tab }));
  }, []);

  const setPreviewDevice = useCallback((device: 'mobile' | 'tablet' | 'desktop') => {
    setEditorState(prev => ({ ...prev, previewDevice: device }));
  }, []);

  const togglePreview = useCallback(() => {
    setEditorState(prev => ({ ...prev, isPreviewOpen: !prev.isPreviewOpen }));
  }, []);

  const goToNextTab = useCallback(() => {
    const tabs: BasicTab[] = ['basic-info', 'media', 'contact', 'business', 'social', 'design', 'advanced'];
    const currentIndex = tabs.indexOf(editorState.currentBasicTab);
    if (currentIndex < tabs.length - 1) {
      setCurrentBasicTab(tabs[currentIndex + 1]);
    }
  }, [editorState.currentBasicTab, setCurrentBasicTab]);

  const goToPreviousTab = useCallback(() => {
    const tabs: BasicTab[] = ['basic-info', 'media', 'contact', 'business', 'social', 'design', 'advanced'];
    const currentIndex = tabs.indexOf(editorState.currentBasicTab);
    if (currentIndex > 0) {
      setCurrentBasicTab(tabs[currentIndex - 1]);
    }
  }, [editorState.currentBasicTab, setCurrentBasicTab]);

  return {
    editorState,
    setCurrentView,
    setCurrentBasicTab,
    setPreviewDevice,
    togglePreview,
    goToNextTab,
    goToPreviousTab
  };
};
