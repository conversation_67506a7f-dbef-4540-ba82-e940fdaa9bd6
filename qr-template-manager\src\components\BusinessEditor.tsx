import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  Tabs,
  Tab,
  IconButton,
  Chip,
  FormControlLabel,
  Switch,
  Divider
} from '@mui/material';
import { X, Save, Eye, Plus, Trash2 } from 'lucide-react';
import type { BusinessInstance, Service, Offer } from '../types/template';

interface BusinessEditorProps {
  business: BusinessInstance | null;
  open: boolean;
  onClose: () => void;
  onSave: (business: BusinessInstance) => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`editor-tabpanel-${index}`}
      aria-labelledby={`editor-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2 }}>{children}</Box>}
    </div>
  );
}

const BusinessEditor: React.FC<BusinessEditorProps> = ({
  business,
  open,
  onClose,
  onSave
}) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [editedBusiness, setEditedBusiness] = useState<BusinessInstance | null>(null);

  useEffect(() => {
    if (business) {
      setEditedBusiness({ ...business });
    }
  }, [business]);

  const handleSave = () => {
    if (editedBusiness) {
      onSave({
        ...editedBusiness,
        updatedAt: new Date().toISOString()
      });
    }
  };

  const updateBusinessData = (field: string, value: any) => {
    if (!editedBusiness) return;
    
    setEditedBusiness({
      ...editedBusiness,
      businessData: {
        ...editedBusiness.businessData,
        [field]: value
      }
    });
  };

  const addService = () => {
    if (!editedBusiness) return;
    
    const newService: Service = {
      id: Date.now().toString(),
      name: '',
      description: '',
      price: '',
      duration: '',
      category: '',
      isActive: true,
      order: editedBusiness.businessData.services.length
    };

    updateBusinessData('services', [...editedBusiness.businessData.services, newService]);
  };

  const updateService = (index: number, field: string, value: any) => {
    if (!editedBusiness) return;
    
    const services = [...editedBusiness.businessData.services];
    services[index] = { ...services[index], [field]: value };
    updateBusinessData('services', services);
  };

  const removeService = (index: number) => {
    if (!editedBusiness) return;
    
    const services = editedBusiness.businessData.services.filter((_, i) => i !== index);
    updateBusinessData('services', services);
  };

  const addOffer = () => {
    if (!editedBusiness) return;
    
    const newOffer: Offer = {
      id: Date.now().toString(),
      title: '',
      description: '',
      originalPrice: '',
      discountPrice: '',
      discountPercent: 0,
      validUntil: '',
      isActive: true,
      order: editedBusiness.businessData.offers.length
    };

    updateBusinessData('offers', [...editedBusiness.businessData.offers, newOffer]);
  };

  const updateOffer = (index: number, field: string, value: any) => {
    if (!editedBusiness) return;
    
    const offers = [...editedBusiness.businessData.offers];
    offers[index] = { ...offers[index], [field]: value };
    updateBusinessData('offers', offers);
  };

  const removeOffer = (index: number) => {
    if (!editedBusiness) return;
    
    const offers = editedBusiness.businessData.offers.filter((_, i) => i !== index);
    updateBusinessData('offers', offers);
  };

  if (!editedBusiness) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '90vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant="h6">Edit Business</Typography>
          <Typography variant="body2" color="text.secondary">
            {editedBusiness.businessName}
          </Typography>
        </Box>
        <IconButton onClick={onClose}>
          <X size={20} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
            <Tab label="Basic Info" />
            <Tab label="Services" />
            <Tab label="Offers" />
            <Tab label="Social Media" />
            <Tab label="Settings" />
          </Tabs>
        </Box>

        <TabPanel value={currentTab} index={0}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Business Name"
                value={editedBusiness.businessData.businessName}
                onChange={(e) => updateBusinessData('businessName', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Slogan"
                value={editedBusiness.businessData.slogan || ''}
                onChange={(e) => updateBusinessData('slogan', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={editedBusiness.businessData.description || ''}
                onChange={(e) => updateBusinessData('description', e.target.value)}
                margin="normal"
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={editedBusiness.businessData.phone}
                onChange={(e) => updateBusinessData('phone', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                value={editedBusiness.businessData.email}
                onChange={(e) => updateBusinessData('email', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                value={editedBusiness.businessData.address}
                onChange={(e) => updateBusinessData('address', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Hours"
                value={editedBusiness.businessData.hours || ''}
                onChange={(e) => updateBusinessData('hours', e.target.value)}
                margin="normal"
                placeholder="Mon-Fri: 9AM-6PM"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Website"
                value={editedBusiness.businessData.website || ''}
                onChange={(e) => updateBusinessData('website', e.target.value)}
                margin="normal"
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={currentTab} index={1}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Services</Typography>
            <Button startIcon={<Plus size={16} />} onClick={addService}>
              Add Service
            </Button>
          </Box>
          
          {editedBusiness.businessData.services.map((service, index) => (
            <Box key={service.id} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle2">Service {index + 1}</Typography>
                <IconButton size="small" color="error" onClick={() => removeService(index)}>
                  <Trash2 size={16} />
                </IconButton>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Service Name"
                    value={service.name}
                    onChange={(e) => updateService(index, 'name', e.target.value)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Price"
                    value={service.price || ''}
                    onChange={(e) => updateService(index, 'price', e.target.value)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    value={service.description || ''}
                    onChange={(e) => updateService(index, 'description', e.target.value)}
                    size="small"
                    multiline
                    rows={2}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Duration"
                    value={service.duration || ''}
                    onChange={(e) => updateService(index, 'duration', e.target.value)}
                    size="small"
                    placeholder="30 min"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={service.isActive}
                        onChange={(e) => updateService(index, 'isActive', e.target.checked)}
                      />
                    }
                    label="Active"
                  />
                </Grid>
              </Grid>
            </Box>
          ))}
        </TabPanel>

        <TabPanel value={currentTab} index={2}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Special Offers</Typography>
            <Button startIcon={<Plus size={16} />} onClick={addOffer}>
              Add Offer
            </Button>
          </Box>
          
          {editedBusiness.businessData.offers.map((offer, index) => (
            <Box key={offer.id} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle2">Offer {index + 1}</Typography>
                <IconButton size="small" color="error" onClick={() => removeOffer(index)}>
                  <Trash2 size={16} />
                </IconButton>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Offer Title"
                    value={offer.title}
                    onChange={(e) => updateOffer(index, 'title', e.target.value)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    value={offer.description || ''}
                    onChange={(e) => updateOffer(index, 'description', e.target.value)}
                    size="small"
                    multiline
                    rows={2}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="Original Price"
                    value={offer.originalPrice || ''}
                    onChange={(e) => updateOffer(index, 'originalPrice', e.target.value)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="Discount Price"
                    value={offer.discountPrice || ''}
                    onChange={(e) => updateOffer(index, 'discountPrice', e.target.value)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="Valid Until"
                    value={offer.validUntil || ''}
                    onChange={(e) => updateOffer(index, 'validUntil', e.target.value)}
                    size="small"
                    type="date"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
              </Grid>
            </Box>
          ))}
        </TabPanel>

        <TabPanel value={currentTab} index={3}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Facebook URL"
                value={editedBusiness.businessData.facebookUrl || ''}
                onChange={(e) => updateBusinessData('facebookUrl', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Instagram URL"
                value={editedBusiness.businessData.instagramUrl || ''}
                onChange={(e) => updateBusinessData('instagramUrl', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Yelp URL"
                value={editedBusiness.businessData.yelpUrl || ''}
                onChange={(e) => updateBusinessData('yelpUrl', e.target.value)}
                margin="normal"
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={currentTab} index={4}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Primary Color"
                value={editedBusiness.businessData.primaryColor || '#1976d2'}
                onChange={(e) => updateBusinessData('primaryColor', e.target.value)}
                margin="normal"
                type="color"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Secondary Color"
                value={editedBusiness.businessData.secondaryColor || '#dc004e'}
                onChange={(e) => updateBusinessData('secondaryColor', e.target.value)}
                margin="normal"
                type="color"
              />
            </Grid>
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <FormControlLabel
                control={
                  <Switch
                    checked={editedBusiness.isActive}
                    onChange={(e) => setEditedBusiness({
                      ...editedBusiness,
                      isActive: e.target.checked
                    })}
                  />
                }
                label="Business Active"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={editedBusiness.isPublic}
                    onChange={(e) => setEditedBusiness({
                      ...editedBusiness,
                      isPublic: e.target.checked
                    })}
                  />
                }
                label="Public Listing"
              />
            </Grid>
          </Grid>
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          variant="contained"
          startIcon={<Save size={16} />}
          onClick={handleSave}
        >
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BusinessEditor;
