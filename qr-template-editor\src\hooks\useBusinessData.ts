import { useState, useCallback, useEffect, useRef } from 'react';
import type { BusinessData, TemplateType } from '../types/index';
import { apiService } from '../services/api-clean';

const defaultBusinessData: BusinessData = {
  // Basic Info
  pageTitle: 'Unique Nails Lounge - Premium Nail Care',
  businessName: 'UNIQUE NAILS LOUNGE',
  slogan: 'Where Beauty Meets Excellence',
  logoUrl: 'https://uniquenaillounge.com/wp-content/uploads/2025/05/Unique-Nails-Lounge-Logo-h300.webp',
  logoFile: undefined,
  faviconUrl: 'https://uniquenaillounge.com/wp-content/uploads/2025/05/Unique-Nails-Lounge-Favicon-h40.webp#7',
  faviconFile: undefined,

  // Header Customization
  headerBackgroundType: 'gradient' as const,
  headerBackgroundValue: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  headerTextColor: '#ffffff',
  
  // Contact Info
  phone: '(*************',
  email: '',
  address: '2021 Gorden Crossing, Ste #150\nGallatin, TN 37066',
  hours: 'Mon - Fri: 9:00 AM – 8:00 PM\nSaturday: 9:00 AM – 6:00 PM\nSunday: 11:00 AM – 5:00 PM',
  
  // Links & Social
  websiteUrl: 'https://uniquenaillounge.com/',
  bookingUrl: 'https://www.lldtek.org/salon/appt/VkU1Zk1URXpNVFE9',
  facebookUrl: 'https://www.facebook.com/profile.php?id=100091754925855',
  instagramUrl: '',
  yelpUrl: '',
  googleMapsUrl: 'https://maps.app.goo.gl/U9buMXZH1SWxdSzc9',
  customSocialLinks: [],
  
  // Template & Styling
  template: 'modern',
  primaryColor: '#d4af37',
  secondaryColor: '#222222',
  customCSS: '',
  
  // Media
  galleryImages: [
    'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop',
    'https://images.unsplash.com/photo-1610992015732-2449b76344bc?w=400&h=300&fit=crop',
    'https://images.unsplash.com/photo-1607779097040-26e80aa78e66?w=400&h=300&fit=crop'
  ],
  galleryFiles: undefined,
  
  // Services & Offers
  services: [
    {
      id: '1',
      name: 'Manicure',
      description: 'Classic nail care with polish',
      price: '$25',
      duration: '30 min'
    },
    {
      id: '2',
      name: 'Pedicure',
      description: 'Foot care and nail polish',
      price: '$35',
      duration: '45 min'
    },
    {
      id: '3',
      name: 'Gel Nails',
      description: 'Long-lasting gel polish',
      price: '$45'
    }
  ],
  offers: [
    {
      id: '1',
      title: 'New Customer Special',
      description: '20% off your first visit',
      discount: '20% OFF',
      validUntil: 'December 31, 2024'
    }
  ]
};

export const useBusinessData = () => {
  const [businessData, setBusinessData] = useState<BusinessData>(defaultBusinessData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 🔄 Auto-save timeout reference
  const autoSaveTimeoutRef = useRef<number | undefined>(undefined);

  // Load data from API on mount
  useEffect(() => {
    console.log('🔄 useBusinessData: Starting data load...');

    const loadData = async () => {
      try {
        console.log('🔄 useBusinessData: Setting loading to true');
        setIsLoading(true);

        console.log('🔄 useBusinessData: Checking server health...');
        const serverHealth = await apiService.checkServerHealth();
        console.log('🔄 useBusinessData: Server health:', serverHealth);

        if (serverHealth) {
          console.log('🔄 useBusinessData: Loading data from API...');
          const data = await apiService.getBusinessData();
          console.log('🔄 useBusinessData: Received data:', {
            yelpUrl: data.yelpUrl,
            instagramUrl: data.instagramUrl,
            customSocialLinks: data.customSocialLinks?.length || 0
          });
          setBusinessData(data);
          console.log('✅ Data loaded from JSON Server');
        } else {
          console.warn('⚠️ JSON Server not available, using default data');
          // Try to restore from localStorage backup
          const backup = apiService.restoreFromLocalStorage();
          if (backup) {
            setBusinessData(backup);
            console.log('📦 Data restored from localStorage backup');
          }
        }
      } catch (error) {
        console.error('❌ Failed to load data:', error);
        setError('Failed to load data from server');

        // Try to restore from localStorage backup
        const backup = apiService.restoreFromLocalStorage();
        if (backup) {
          setBusinessData(backup);
          console.log('📦 Data restored from localStorage backup');
        }
      } finally {
        console.log('🔄 useBusinessData: Setting loading to false');
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // 🧹 CLEANUP - Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
        console.log('🧹 Cleaned up auto-save timeout');
      }
    };
  }, []);

  const updateBusinessData = useCallback(async (updates: Partial<BusinessData>) => {
    const newData = { ...businessData, ...updates };
    setBusinessData(newData);

    // Auto-save to server
    try {
      await apiService.autoSave(newData);
      setError(null);
    } catch (error) {
      console.error('❌ Auto-save failed:', error);
      // Backup to localStorage if server fails
      apiService.backupToLocalStorage(newData);
    }
  }, [businessData]);

  const updateField = useCallback(async (field: keyof BusinessData, value: any) => {
    const newData = { ...businessData, [field]: value };
    setBusinessData(newData);

    // Only auto-save non-File fields to server (Files are not serializable)
    const isFileField = field.toString().includes('File') || value instanceof File;

    if (!isFileField) {
      try {
        await apiService.autoSave(newData);
        setError(null);
      } catch (error) {
        console.error('❌ Auto-save failed:', error);
        // Backup to localStorage if server fails
        apiService.backupToLocalStorage(newData);
      }
    } else {
      console.log('📁 File field updated locally:', field, value?.name || 'removed');
    }
  }, [businessData]);

  const batchUpdate = useCallback((updates: Partial<BusinessData>) => {
    const newData = { ...businessData, ...updates };

    // 🚀 IMMEDIATE UI UPDATE - No waiting for API
    setBusinessData(newData);
    console.log('⚡ Immediate batch update:', Object.keys(updates));

    // Clear previous auto-save timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    // 🔄 DEBOUNCED AUTO-SAVE - Save after 1 second of no changes
    autoSaveTimeoutRef.current = setTimeout(async () => {
      try {
        // Filter out File objects for server save
        const serverData = { ...newData };
        Object.keys(serverData).forEach(key => {
          if (key.includes('File') || serverData[key as keyof BusinessData] instanceof File) {
            delete serverData[key as keyof BusinessData];
          }
        });

        await apiService.autoSave(serverData);
        console.log('💾 Batch auto-saved to server');
        setError(null);
      } catch (error) {
        console.error('❌ Batch auto-save failed:', error);
        apiService.backupToLocalStorage(newData);
      }
    }, 1000);
  }, [businessData]);

  const resetToDefault = useCallback(async () => {
    setBusinessData(defaultBusinessData);

    // Save to server
    try {
      await apiService.updateBusinessData(defaultBusinessData);
      setError(null);
    } catch (error) {
      console.error('❌ Reset failed:', error);
      apiService.backupToLocalStorage(defaultBusinessData);
    }
  }, []);

  const loadFromProfile = useCallback(async (profileData: BusinessData) => {
    setBusinessData(profileData);

    // Save to server
    try {
      await apiService.updateBusinessData(profileData);
      setError(null);
    } catch (error) {
      console.error('❌ Load profile failed:', error);
      apiService.backupToLocalStorage(profileData);
    }
  }, []);

  const saveToServer = useCallback(async () => {
    try {
      setIsLoading(true);
      await apiService.updateBusinessData(businessData);
      setError(null);
      console.log('✅ Data saved to JSON Server');
      return true;
    } catch (error) {
      console.error('❌ Save failed:', error);
      setError('Failed to save data to server');
      // Backup to localStorage
      apiService.backupToLocalStorage(businessData);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [businessData]);

  const applyTemplate = useCallback((template: TemplateType) => {
    const templateConfigs = {
      modern: {
        primaryColor: '#3B82F6',
        secondaryColor: '#1E40AF'
      },
      luxury: {
        primaryColor: '#D4AF37',
        secondaryColor: '#B8860B'
      },
      minimal: {
        primaryColor: '#6B7280',
        secondaryColor: '#374151'
      },
      cute: {
        primaryColor: '#EC4899',
        secondaryColor: '#BE185D'
      }
    };

    const config = templateConfigs[template];
    updateBusinessData({
      template,
      primaryColor: config.primaryColor,
      secondaryColor: config.secondaryColor
    });
  }, [updateBusinessData]);

  return {
    businessData,
    updateBusinessData,
    updateField,
    batchUpdate,
    resetToDefault,
    loadFromProfile,
    applyTemplate,
    saveToServer,
    isLoading,
    error
  };
};
