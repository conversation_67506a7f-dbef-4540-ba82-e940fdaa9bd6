import React from 'react';
import {
  Card,
  CardContent,
  TextField,
  Typography,
  Box,
  Button,
  Alert,
  InputAdornment,
  IconButton
} from '@mui/material';
import { Info, Wand2, CheckCircle, AlertCircle } from 'lucide-react';
import type { BusinessData, ValidationResult } from '../../types/index';
import { validateRequired, validateUrl } from '../../utils/validation';
import SimpleImageUpload from '../Upload/SimpleImageUpload';

interface BasicInfoTabProps {
  businessData: BusinessData;
  onUpdate: (field: keyof BusinessData, value: string | File | undefined) => void;
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ businessData, onUpdate }) => {
  const [validations, setValidations] = React.useState<{ [key: string]: ValidationResult }>({});

  const validateField = (field: keyof BusinessData, value: string) => {
    let validation: ValidationResult;
    
    switch (field) {
      case 'pageTitle':
        validation = validateRequired(value, 'Page Title');
        break;
      case 'businessName':
        validation = validateRequired(value, 'Business Name');
        break;
      case 'logoUrl':
        validation = validateUrl(value, 'Logo');
        break;
      case 'faviconUrl':
        validation = validateUrl(value, 'Favicon');
        break;
      default:
        validation = { isValid: true, message: '', type: 'success' };
    }
    
    setValidations(prev => ({ ...prev, [field]: validation }));
    return validation;
  };

  const handleFieldChange = (field: keyof BusinessData, value: string) => {
    onUpdate(field, value);
    validateField(field, value);
  };

  const autoDetectFavicon = () => {
    if (businessData.websiteUrl) {
      try {
        const url = new URL(businessData.websiteUrl);
        const faviconUrl = `${url.origin}/favicon.ico`;
        handleFieldChange('faviconUrl', faviconUrl);
      } catch (error) {
        alert('Please enter a valid website URL first');
      }
    } else {
      alert('Please enter a website URL first');
    }
  };

  const handleLogoUpload = (result: { dataUrl: string; file: File }) => {
    console.log('🖼️ Logo upload:', result.file.name, 'Size:', Math.round(result.dataUrl.length / 1024), 'KB');
    onUpdate('logoUrl', result.dataUrl);
    onUpdate('logoFile', result.file);
  };

  const handleLogoRemove = () => {
    console.log('🗑️ Logo removed');
    onUpdate('logoUrl', '');
    onUpdate('logoFile', undefined);
  };

  const handleFaviconUpload = (result: { dataUrl: string; file: File }) => {
    console.log('🔖 Favicon upload:', result.file.name, 'Size:', Math.round(result.dataUrl.length / 1024), 'KB');
    onUpdate('faviconUrl', result.dataUrl);
    onUpdate('faviconFile', result.file);
  };

  const handleFaviconRemove = () => {
    console.log('🗑️ Favicon removed');
    onUpdate('faviconUrl', '');
    onUpdate('faviconFile', undefined);
  };

  const getValidationProps = (field: keyof BusinessData) => {
    const validation = validations[field];
    if (!validation) return {};

    return {
      error: !validation.isValid,
      helperText: validation.message,
      InputProps: {
        endAdornment: validation.message && (
          <InputAdornment position="end">
            {validation.isValid ? (
              <CheckCircle size={16} color="green" />
            ) : (
              <AlertCircle size={16} color="red" />
            )}
          </InputAdornment>
        ),
      },
    };
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              bgcolor: 'primary.main',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            <Info size={20} color="white" />
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Basic Information
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Essential details about your business
            </Typography>
          </Box>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          Fill in the basic information that will appear on your QR code page. Required fields are marked with *.
        </Alert>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          <TextField
            label="Page Title"
            value={businessData.pageTitle}
            onChange={(e) => handleFieldChange('pageTitle', e.target.value)}
            required
            fullWidth
            placeholder="e.g., Unique Nails Lounge - Premium Nail Care"
            {...getValidationProps('pageTitle')}
          />

          <TextField
            label="Business Name"
            value={businessData.businessName}
            onChange={(e) => handleFieldChange('businessName', e.target.value)}
            required
            fullWidth
            placeholder="e.g., UNIQUE NAILS LOUNGE"
            {...getValidationProps('businessName')}
          />

          <TextField
            label="Slogan (Optional)"
            value={businessData.slogan || ''}
            onChange={(e) => handleFieldChange('slogan', e.target.value)}
            fullWidth
            placeholder="e.g., Where Beauty Meets Excellence"
            helperText="A catchy tagline that describes your business"
          />

          <SimpleImageUpload
            label="Business Logo"
            description="Upload your business logo. This will appear at the top of your QR page."
            currentImage={businessData.logoUrl}
            onImageUpload={handleLogoUpload}
            onImageRemove={handleLogoRemove}
            size={80}
          />

          <SimpleImageUpload
            label="Favicon"
            description="Upload a favicon for your page. This small icon appears in browser tabs."
            currentImage={businessData.faviconUrl}
            onImageUpload={handleFaviconUpload}
            onImageRemove={handleFaviconRemove}
            size={40}
          />

          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
            <TextField
              label="Favicon URL (Alternative)"
              value={businessData.faviconUrl}
              onChange={(e) => handleFieldChange('faviconUrl', e.target.value)}
              fullWidth
              placeholder="https://example.com/favicon.ico"
              size="small"
              {...getValidationProps('faviconUrl')}
            />
            <Button
              variant="outlined"
              startIcon={<Wand2 size={16} />}
              onClick={autoDetectFavicon}
              sx={{ minWidth: 'auto', px: 2 }}
              size="small"
            >
              Auto
            </Button>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default BasicInfoTab;
