'use client';

import * as React from 'react';
import { useTheme as useThemeSystem } from '@mui/system';
import defaultTheme from "./defaultTheme.js";
import THEME_ID from "./identifier.js";
export default function useTheme() {
  const theme = useThemeSystem(defaultTheme);
  if (process.env.NODE_ENV !== 'production') {
    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/rules-of-hooks
    React.useDebugValue(theme);
  }
  return theme[THEME_ID] || theme;
}