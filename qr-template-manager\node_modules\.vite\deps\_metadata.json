{"hash": "7b430af2", "configHash": "4891556e", "lockfileHash": "0e6a4e8d", "browserHash": "5dda4d8a", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "8e522c0e", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c632b46a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "5267c70c", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b1d6b54d", "needsInterop": true}, "@mui/material": {"src": "../../@mui/material/esm/index.js", "file": "@mui_material.js", "fileHash": "c2a572b9", "needsInterop": false}, "@mui/material/styles": {"src": "../../@mui/material/esm/styles/index.js", "file": "@mui_material_styles.js", "fileHash": "7c30a907", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "d84109aa", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "73667c02", "needsInterop": true}}, "chunks": {"chunk-QT7BC6FG": {"file": "chunk-QT7BC6FG.js"}, "chunk-R3OOIZWV": {"file": "chunk-R3OOIZWV.js"}, "chunk-NYHBQTAG": {"file": "chunk-NYHBQTAG.js"}, "chunk-P5XWQMHZ": {"file": "chunk-P5XWQMHZ.js"}}}