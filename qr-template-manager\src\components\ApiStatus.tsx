import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Box, Button, Chip } from '@mui/material';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { checkApiHealth } from '../utils/api';

const ApiStatus: React.FC = () => {
  const [isOnline, setIsOnline] = useState<boolean | null>(null);
  const [checking, setChecking] = useState(false);

  const checkStatus = async () => {
    setChecking(true);
    try {
      const status = await checkApiHealth();
      setIsOnline(status);
    } catch {
      setIsOnline(false);
    } finally {
      setChecking(false);
    }
  };

  useEffect(() => {
    checkStatus();
    
    // Check every 30 seconds
    const interval = setInterval(checkStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  if (isOnline === null) {
    return null; // Loading
  }

  if (isOnline) {
    return (
      <Box sx={{ position: 'fixed', top: 16, right: 16, zIndex: 1000 }}>
        <Chip
          icon={<Wifi size={16} />}
          label="API Connected"
          color="success"
          size="small"
        />
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'fixed', top: 16, right: 16, zIndex: 1000, maxWidth: 400 }}>
      <Alert 
        severity="error" 
        action={
          <Button
            color="inherit"
            size="small"
            onClick={checkStatus}
            disabled={checking}
            startIcon={<RefreshCw size={16} />}
          >
            {checking ? 'Checking...' : 'Retry'}
          </Button>
        }
        icon={<WifiOff size={16} />}
      >
        API Server Offline - Please start JSON server on port 3001
      </Alert>
    </Box>
  );
};

export default ApiStatus;
