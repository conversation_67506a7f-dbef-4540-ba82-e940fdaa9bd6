import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Ty<PERSON><PERSON>,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Tabs,
  Tab,
  Paper,
  Alert,
  Snackbar
} from '@mui/material';
import { ArrowLeft, Save, Eye, Download, Upload } from 'lucide-react';
import type { Template } from '../types/template';
import EditContentTab from './editor/EditContentTab';
import ThemeCustomizationTab from './editor/ThemeCustomizationTab';
import MobilePreviewTab from './editor/MobilePreviewTab';
import QRCodeTab from './editor/QRCodeTab';
import { templateEngine } from '../utils/templateEngine';

interface TemplateEditorPageProps {
  template: Template;
  onBack: () => void;
  onSave: (template: Template) => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`editor-tabpanel-${index}`}
      aria-labelledby={`editor-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const TemplateEditorPage: React.FC<TemplateEditorPageProps> = ({
  template,
  onBack,
  onSave
}) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [editedTemplate, setEditedTemplate] = useState<Template>(template);
  const [businessData, setBusinessData] = useState({
    businessName: 'Sample Business',
    slogan: 'Your Success is Our Mission',
    description: 'We provide excellent services to help your business grow.',
    phone: '(*************',
    email: '<EMAIL>',
    address: '123 Main Street, City, State 12345',
    hours: 'Mon-Fri: 9AM-6PM, Sat: 10AM-4PM',
    website: 'https://samplebusiness.com',
    logoUrl: '',
    faviconUrl: '',
    services: [
      {
        id: '1',
        name: 'Consultation',
        description: 'Professional business consultation',
        price: '$100',
        duration: '1 hour',
        category: 'consulting',
        isActive: true,
        order: 1
      },
      {
        id: '2',
        name: 'Strategy Planning',
        description: 'Comprehensive business strategy development',
        price: '$250',
        duration: '2 hours',
        category: 'planning',
        isActive: true,
        order: 2
      }
    ],
    offers: [
      {
        id: '1',
        title: 'New Client Special',
        description: 'Get 20% off your first consultation',
        originalPrice: '$100',
        discountPrice: '$80',
        validUntil: '2024-12-31',
        isActive: true,
        order: 1
      }
    ],
    galleryImages: [],
    facebookUrl: 'https://facebook.com/samplebusiness',
    instagramUrl: 'https://instagram.com/samplebusiness',
    yelpUrl: 'https://yelp.com/biz/samplebusiness',
    customSocialLinks: [],
    primaryColor: '#1976d2',
    secondaryColor: '#dc004e'
  });
  
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success'
  });

  const [previewHtml, setPreviewHtml] = useState('');

  useEffect(() => {
    generatePreview();
  }, [editedTemplate, businessData]);

  const generatePreview = () => {
    try {
      const rendered = templateEngine.renderTemplate(editedTemplate.htmlContent, businessData);
      setPreviewHtml(rendered);
    } catch (error) {
      console.error('Preview generation failed:', error);
      setPreviewHtml('<p>Preview generation failed</p>');
    }
  };

  const handleSave = async () => {
    try {
      const updatedTemplate = {
        ...editedTemplate,
        updatedAt: new Date().toISOString()
      };
      
      await onSave(updatedTemplate);
      
      setNotification({
        open: true,
        message: 'Template saved successfully!',
        severity: 'success'
      });
    } catch (error) {
      setNotification({
        open: true,
        message: 'Failed to save template',
        severity: 'error'
      });
    }
  };

  const handleExport = () => {
    const blob = new Blob([previewHtml], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${editedTemplate.name.replace(/\s+/g, '-').toLowerCase()}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setNotification({
      open: true,
      message: 'Template exported successfully!',
      severity: 'success'
    });
  };

  const updateTemplate = (field: string, value: any) => {
    setEditedTemplate(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateBusinessData = (field: string, value: any) => {
    setBusinessData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const tabs = [
    { label: 'Edit Content', icon: <Upload size={16} /> },
    { label: 'Theme', icon: <Eye size={16} /> },
    { label: 'Mobile Preview', icon: <Eye size={16} /> },
    { label: 'QR Code', icon: <Download size={16} /> }
  ];

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <AppBar position="static" color="default" elevation={1}>
        <Toolbar>
          <IconButton edge="start" onClick={onBack} sx={{ mr: 2 }}>
            <ArrowLeft size={20} />
          </IconButton>
          
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            Edit Template: {editedTemplate.name}
          </Typography>

          <Button
            variant="outlined"
            startIcon={<Eye size={16} />}
            onClick={() => setCurrentTab(2)}
            sx={{ mr: 1 }}
          >
            Preview
          </Button>

          <Button
            variant="outlined"
            startIcon={<Download size={16} />}
            onClick={handleExport}
            sx={{ mr: 1 }}
          >
            Export
          </Button>

          <Button
            variant="contained"
            startIcon={<Save size={16} />}
            onClick={handleSave}
          >
            Save
          </Button>
        </Toolbar>
      </AppBar>

      {/* Tabs */}
      <Paper square elevation={0} sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={currentTab}
          onChange={(_, newValue) => setCurrentTab(newValue)}
          variant="fullWidth"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Paper>

      {/* Tab Content */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <TabPanel value={currentTab} index={0}>
          <EditContentTab
            businessData={businessData}
            onUpdateBusinessData={updateBusinessData}
            template={editedTemplate}
            onUpdateTemplate={updateTemplate}
          />
        </TabPanel>

        <TabPanel value={currentTab} index={1}>
          <ThemeCustomizationTab
            template={editedTemplate}
            businessData={businessData}
            onUpdateTemplate={updateTemplate}
            onUpdateBusinessData={updateBusinessData}
          />
        </TabPanel>

        <TabPanel value={currentTab} index={2}>
          <MobilePreviewTab
            previewHtml={previewHtml}
            businessData={businessData}
          />
        </TabPanel>

        <TabPanel value={currentTab} index={3}>
          <QRCodeTab
            businessData={businessData}
            previewHtml={previewHtml}
            templateName={editedTemplate.name}
          />
        </TabPanel>
      </Box>

      {/* Notifications */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, open: false })}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TemplateEditorPage;
