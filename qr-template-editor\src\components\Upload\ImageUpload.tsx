import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  IconButton,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import { Upload, X, Image as ImageIcon, Download } from 'lucide-react';
import { uploadImage, type ImageUploadOptions, type ImageUploadResult } from '../../utils/imageUtils';

interface ImageUploadProps {
  label: string;
  description?: string;
  currentImage?: string;
  options?: ImageUploadOptions;
  onImageUpload: (result: ImageUploadResult) => void;
  onMultipleImageUpload?: (results: ImageUploadResult[]) => void;
  onImageRemove?: () => void;
  disabled?: boolean;
  multiple?: boolean;
  maxFiles?: number;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  label,
  description,
  currentImage,
  options,
  onImageUpload,
  onMultipleImageUpload,
  onImageRemove,
  disabled = false,
  multiple = false,
  maxFiles = 5
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    setIsUploading(true);
    setError(null);

    try {
      if (multiple && onMultipleImageUpload) {
        // Handle multiple files
        const filesToProcess = Array.from(files).slice(0, maxFiles);
        const results: ImageUploadResult[] = [];

        for (const file of filesToProcess) {
          const result = await uploadImage(file, options);
          results.push(result);
        }

        onMultipleImageUpload(results);
      } else {
        // Handle single file
        const file = files[0];
        const result = await uploadImage(file, options);
        onImageUpload(result);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload image(s)');
    } finally {
      setIsUploading(false);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    if (disabled) return;
    
    const files = e.dataTransfer.files;
    handleFileSelect(files);
  };

  const handleRemove = () => {
    if (onImageRemove) {
      onImageRemove();
    }
    setError(null);
  };

  const getAcceptedFormats = () => {
    return 'image/jpeg,image/jpg,image/png,image/webp,image/gif';
  };

  const getMaxSizeText = () => {
    if (options?.maxSizeKB) {
      return options.maxSizeKB < 1024 
        ? `${options.maxSizeKB}KB`
        : `${Math.round(options.maxSizeKB / 1024)}MB`;
    }
    return '10MB';
  };

  const getDimensionsText = () => {
    if (options?.maxWidth && options?.maxHeight) {
      return `${options.maxWidth}×${options.maxHeight}px`;
    }
    return '';
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
        {label}
      </Typography>
      
      {description && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {description}
        </Typography>
      )}

      {/* Current Image Preview */}
      {currentImage && (
        <Box sx={{ mb: 2 }}>
          <Paper
            elevation={1}
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              bgcolor: 'grey.50'
            }}
          >
            <Box
              component="img"
              src={currentImage}
              alt="Current image"
              sx={{
                width: 60,
                height: 60,
                objectFit: 'cover',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.300'
              }}
            />
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Current Image
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                <Chip
                  label="Preview"
                  size="small"
                  icon={<ImageIcon size={14} />}
                  variant="outlined"
                />
              </Box>
            </Box>
            {onImageRemove && (
              <IconButton
                onClick={handleRemove}
                size="small"
                color="error"
                disabled={disabled}
              >
                <X size={16} />
              </IconButton>
            )}
          </Paper>
        </Box>
      )}

      {/* Upload Area */}
      <Paper
        elevation={dragOver ? 3 : 1}
        sx={{
          p: 3,
          border: '2px dashed',
          borderColor: dragOver ? 'primary.main' : 'grey.300',
          bgcolor: dragOver ? 'primary.50' : 'grey.50',
          textAlign: 'center',
          cursor: disabled ? 'not-allowed' : 'pointer',
          transition: 'all 0.2s ease',
          opacity: disabled ? 0.6 : 1,
          '&:hover': disabled ? {} : {
            borderColor: 'primary.main',
            bgcolor: 'primary.50'
          }
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={!disabled ? handleButtonClick : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={getAcceptedFormats()}
          onChange={(e) => handleFileSelect(e.target.files)}
          style={{ display: 'none' }}
          disabled={disabled}
          multiple={multiple}
        />

        {isUploading ? (
          <Box sx={{ py: 2 }}>
            <CircularProgress size={40} sx={{ mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              Processing image...
            </Typography>
          </Box>
        ) : (
          <>
            <Upload size={40} color={dragOver ? '#1976d2' : '#666'} style={{ marginBottom: 16 }} />
            <Typography variant="h6" gutterBottom>
              {dragOver ? `Drop ${multiple ? 'images' : 'image'} here` : `Upload ${multiple ? 'Images' : 'Image'}`}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {multiple
                ? `Drag and drop up to ${maxFiles} images here, or click to select`
                : 'Drag and drop an image here, or click to select'
              }
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, flexWrap: 'wrap' }}>
              <Chip label="JPEG, PNG, WebP" size="small" variant="outlined" />
              <Chip label={`Max ${getMaxSizeText()}`} size="small" variant="outlined" />
              {getDimensionsText() && (
                <Chip label={`Max ${getDimensionsText()}`} size="small" variant="outlined" />
              )}
            </Box>

            <Button
              variant="contained"
              startIcon={<Upload size={16} />}
              sx={{ mt: 2 }}
              disabled={disabled}
            >
              {multiple ? 'Choose Files' : 'Choose File'}
            </Button>
          </>
        )}
      </Paper>

      {/* Error Message */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}
    </Box>
  );
};

export default ImageUpload;
