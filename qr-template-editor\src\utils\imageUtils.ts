export interface ImageUploadOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  maxSizeKB?: number;
}

export interface ImageUploadResult {
  dataUrl: string;
  file: File;
  width: number;
  height: number;
  sizeKB: number;
}

export const validateImage = (file: File): Promise<boolean> => {
  return new Promise((resolve) => {
    // Check file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    if (!validTypes.includes(file.type)) {
      resolve(false);
      return;
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      resolve(false);
      return;
    }

    resolve(true);
  });
};

export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({ width: img.width, height: img.height });
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };
    
    img.src = url;
  });
};

export const resizeImage = (
  file: File, 
  options: ImageUploadOptions = {}
): Promise<ImageUploadResult> => {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 1200,
      maxHeight = 1200,
      quality = 0.8,
      format = 'jpeg',
      maxSizeKB = 500
    } = options;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width = Math.round(width * ratio);
        height = Math.round(height * ratio);
      }

      // Set canvas size
      canvas.width = width;
      canvas.height = height;

      // Draw resized image
      ctx.drawImage(img, 0, 0, width, height);

      // Convert to blob
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to create blob'));
            return;
          }

          const sizeKB = Math.round(blob.size / 1024);
          
          // Check if size is acceptable
          if (sizeKB > maxSizeKB) {
            // Try with lower quality
            const newQuality = Math.max(0.1, quality * 0.7);
            if (newQuality < quality) {
              resizeImage(file, { ...options, quality: newQuality })
                .then(resolve)
                .catch(reject);
              return;
            }
          }

          // Create data URL
          const reader = new FileReader();
          reader.onload = () => {
            resolve({
              dataUrl: reader.result as string,
              file: new File([blob], file.name, { type: blob.type }),
              width,
              height,
              sizeKB
            });
          };
          reader.onerror = () => reject(new Error('Failed to read blob'));
          reader.readAsDataURL(blob);
        },
        format === 'png' ? 'image/png' : 'image/jpeg',
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

export const uploadImage = async (
  file: File,
  options: ImageUploadOptions = {}
): Promise<ImageUploadResult> => {
  // Validate image
  const isValid = await validateImage(file);
  if (!isValid) {
    throw new Error('Invalid image file. Please use JPEG, PNG, WebP, or GIF format under 10MB.');
  }

  // Get original dimensions
  const dimensions = await getImageDimensions(file);
  
  // Resize if needed
  const result = await resizeImage(file, options);
  
  console.log(`Image processed: ${dimensions.width}x${dimensions.height} → ${result.width}x${result.height}, ${result.sizeKB}KB`);
  
  return result;
};

// Preset configurations for different image types
export const IMAGE_PRESETS = {
  logo: {
    maxWidth: 400,
    maxHeight: 200,
    quality: 0.9,
    format: 'png' as const,
    maxSizeKB: 200
  },
  favicon: {
    maxWidth: 64,
    maxHeight: 64,
    quality: 0.9,
    format: 'png' as const,
    maxSizeKB: 50
  },
  gallery: {
    maxWidth: 800,
    maxHeight: 600,
    quality: 0.8,
    format: 'jpeg' as const,
    maxSizeKB: 300
  }
};

export const generateThumbnail = (
  file: File,
  size: number = 150
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      // Create square thumbnail
      canvas.width = size;
      canvas.height = size;

      // Calculate crop area for square thumbnail
      const { width, height } = img;
      const minDimension = Math.min(width, height);
      const x = (width - minDimension) / 2;
      const y = (height - minDimension) / 2;

      // Draw cropped and resized image
      ctx.drawImage(
        img,
        x, y, minDimension, minDimension,
        0, 0, size, size
      );

      // Convert to data URL
      resolve(canvas.toDataURL('image/jpeg', 0.8));
    };

    img.onerror = () => reject(new Error('Failed to load image for thumbnail'));
    img.src = URL.createObjectURL(file);
  });
};
