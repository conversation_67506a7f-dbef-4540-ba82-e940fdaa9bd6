import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  CardActions,
  Ty<PERSON>graphy,
  <PERSON>ton,
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  IconButton,
  Grid
} from '@mui/material';
import { Plus, Edit, Trash2, Eye, Download, QrCode } from 'lucide-react';
import type { BusinessInstance, Template } from '../types/template';
import BusinessEditor from './BusinessEditor';
import { templateEngine } from '../utils/templateEngine';

const BusinessManager: React.FC = () => {
  const [businesses, setBusinesses] = useState<BusinessInstance[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newBusinessName, setNewBusinessName] = useState('');
  const [selectedTemplateId, setSelectedTemplateId] = useState('');
  const [editBusiness, setEditBusiness] = useState<BusinessInstance | null>(null);
  const [editorOpen, setEditorOpen] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [businessesRes, templatesRes] = await Promise.all([
        fetch('http://localhost:3001/businesses'),
        fetch('http://localhost:3001/templates')
      ]);
      
      if (!businessesRes.ok || !templatesRes.ok) {
        throw new Error('Failed to load data');
      }
      
      const [businessesData, templatesData] = await Promise.all([
        businessesRes.json(),
        templatesRes.json()
      ]);
      
      setBusinesses(businessesData);
      setTemplates(templatesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBusiness = async () => {
    if (!newBusinessName.trim() || !selectedTemplateId) return;

    try {
      const response = await fetch('http://localhost:3001/businesses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: Date.now().toString(),
          businessName: newBusinessName,
          templateId: selectedTemplateId,
          templateVersion: '1.0.0',
          businessData: {
            businessName: newBusinessName,
            phone: '',
            email: '',
            address: '',
            services: [],
            offers: [],
            galleryImages: [],
            customSocialLinks: [],
            primaryColor: '#1976d2',
            secondaryColor: '#dc004e'
          },
          customizations: {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          viewCount: 0,
          isActive: true,
          isPublic: false,
          exportHistory: []
        })
      });

      if (!response.ok) throw new Error('Failed to create business');
      
      setCreateDialogOpen(false);
      setNewBusinessName('');
      setSelectedTemplateId('');
      loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create business');
    }
  };

  const handleDeleteBusiness = async (id: string) => {
    if (!confirm('Are you sure you want to delete this business?')) return;

    try {
      const response = await fetch(`http://localhost:3001/businesses/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete business');
      loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete business');
    }
  };

  const handleEditBusiness = (business: BusinessInstance) => {
    setEditBusiness(business);
    setEditorOpen(true);
  };

  const handleSaveBusiness = async (updatedBusiness: BusinessInstance) => {
    try {
      const response = await fetch(`http://localhost:3001/businesses/${updatedBusiness.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedBusiness)
      });

      if (!response.ok) throw new Error('Failed to update business');

      setEditorOpen(false);
      setEditBusiness(null);
      loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update business');
    }
  };

  const handlePreviewBusiness = async (business: BusinessInstance) => {
    try {
      // Get template
      const template = templates.find(t => t.id === business.templateId);
      if (!template) {
        setError('Template not found');
        return;
      }

      // Generate HTML
      const html = templateEngine.renderTemplate(template, business);

      // Open in new window
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(html);
        newWindow.document.close();
      }
    } catch (err) {
      setError('Failed to generate preview');
    }
  };

  const handleExportBusiness = async (business: BusinessInstance) => {
    try {
      // Get template
      const template = templates.find(t => t.id === business.templateId);
      if (!template) {
        setError('Template not found');
        return;
      }

      // Generate HTML
      const html = templateEngine.renderTemplate(template, business);

      // Download as file
      const blob = new Blob([html], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${business.businessName.toLowerCase().replace(/\s+/g, '-')}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // Update export history
      const exportRecord = {
        id: Date.now().toString(),
        exportedAt: new Date().toISOString(),
        format: 'html' as const,
        fileSize: blob.size,
      };

      const updatedBusiness = {
        ...business,
        exportHistory: [...business.exportHistory, exportRecord],
        lastExported: new Date().toISOString()
      };

      await fetch(`http://localhost:3001/businesses/${business.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedBusiness)
      });

      loadData();
    } catch (err) {
      setError('Failed to export business');
    }
  };

  const getTemplateName = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    return template?.name || 'Unknown Template';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
        <Button onClick={loadData} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Business Manager
        </Typography>
        <Button
          variant="contained"
          startIcon={<Plus size={20} />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create New Business
        </Button>
      </Box>

      {/* Businesses Grid */}
      <Grid container spacing={3}>
        {businesses.map((business) => (
          <Grid size={{ xs: 12, sm: 6, md: 4 }} key={business.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography gutterBottom variant="h6" component="div">
                  {business.businessName}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Template: {getTemplateName(business.templateId)}
                </Typography>

                {/* Status and Info */}
                <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip 
                    label={business.isActive ? 'Active' : 'Inactive'} 
                    size="small" 
                    color={business.isActive ? 'success' : 'default'}
                  />
                  <Chip 
                    label={business.isPublic ? 'Public' : 'Private'} 
                    size="small" 
                    variant="outlined"
                  />
                </Box>

                {/* Contact Info */}
                <Box sx={{ mb: 2 }}>
                  {business.businessData.phone && (
                    <Typography variant="body2" color="text.secondary">
                      📞 {business.businessData.phone}
                    </Typography>
                  )}
                  {business.businessData.email && (
                    <Typography variant="body2" color="text.secondary">
                      ✉️ {business.businessData.email}
                    </Typography>
                  )}
                  {business.businessData.address && (
                    <Typography variant="body2" color="text.secondary">
                      📍 {business.businessData.address}
                    </Typography>
                  )}
                </Box>

                {/* Stats */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Views: {business.viewCount}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Exports: {business.exportHistory.length}
                  </Typography>
                </Box>

                {/* Dates */}
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Created: {new Date(business.createdAt).toLocaleDateString()}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Updated: {new Date(business.updatedAt).toLocaleDateString()}
                </Typography>
              </CardContent>

              <CardActions>
                <Button 
                  size="small" 
                  startIcon={<Eye size={16} />}
                  onClick={() => handlePreviewBusiness(business)}
                >
                  Preview
                </Button>
                <Button 
                  size="small" 
                  startIcon={<Edit size={16} />}
                  onClick={() => handleEditBusiness(business)}
                >
                  Edit
                </Button>
                <Button 
                  size="small" 
                  startIcon={<Download size={16} />}
                  onClick={() => handleExportBusiness(business)}
                >
                  Export
                </Button>
                <IconButton 
                  size="small" 
                  color="error"
                  onClick={() => handleDeleteBusiness(business.id)}
                >
                  <Trash2 size={16} />
                </IconButton>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {businesses.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No businesses found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Create your first business to get started
          </Typography>
        </Box>
      )}

      {/* Create Business Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Business</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Business Name"
            fullWidth
            variant="outlined"
            value={newBusinessName}
            onChange={(e) => setNewBusinessName(e.target.value)}
            sx={{ mb: 2 }}
          />
          
          <FormControl fullWidth>
            <InputLabel>Template</InputLabel>
            <Select
              value={selectedTemplateId}
              label="Template"
              onChange={(e) => setSelectedTemplateId(e.target.value)}
            >
              {templates.filter(t => t.status === 'published').map((template) => (
                <MenuItem key={template.id} value={template.id}>
                  {template.name} ({template.category})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateBusiness}
            variant="contained"
            disabled={!newBusinessName.trim() || !selectedTemplateId}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Business Editor */}
      <BusinessEditor
        business={editBusiness}
        open={editorOpen}
        onClose={() => {
          setEditorOpen(false);
          setEditBusiness(null);
        }}
        onSave={handleSaveBusiness}
      />
    </Box>
  );
};

export default BusinessManager;
