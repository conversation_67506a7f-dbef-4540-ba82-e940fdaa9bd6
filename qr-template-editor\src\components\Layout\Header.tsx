import React from 'react';
import { AppB<PERSON>, Too<PERSON><PERSON>, <PERSON>po<PERSON>, Button, Box, IconButton } from '@mui/material';
import { Save, Download, Settings, Eye, Smartphone, Tablet, Monitor, Bug } from 'lucide-react';
import type { EditorState } from '../../types/index';
import { debugTemplate } from '../../utils/debugTemplate';

interface HeaderProps {
  editorState: EditorState;
  businessData: any;
  onSave: () => void;
  onExport: () => void;
  onPreviewDeviceChange: (device: 'mobile' | 'tablet' | 'desktop') => void;
  onTogglePreview: () => void;
}

const Header: React.FC<HeaderProps> = ({
  editorState,
  businessData,
  onSave,
  onExport,
  onPreviewDeviceChange,
  onTogglePreview
}) => {
  const handleDebug = async () => {
    try {
      await debugTemplate(businessData);
    } catch (error) {
      console.error('Debug failed:', error);
    }
  };
  return (
    <AppBar position="static" sx={{ bgcolor: 'white', color: 'text.primary', boxShadow: 1 }}>
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1, color: 'primary.main', fontWeight: 'bold' }}>
          QR Template Editor
        </Typography>

        {/* Preview Device Controls */}
        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
          <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary' }}>
            Preview:
          </Typography>
          <IconButton
            size="small"
            onClick={() => onPreviewDeviceChange('mobile')}
            color={editorState.previewDevice === 'mobile' ? 'primary' : 'default'}
          >
            <Smartphone size={18} />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => onPreviewDeviceChange('tablet')}
            color={editorState.previewDevice === 'tablet' ? 'primary' : 'default'}
          >
            <Tablet size={18} />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => onPreviewDeviceChange('desktop')}
            color={editorState.previewDevice === 'desktop' ? 'primary' : 'default'}
          >
            <Monitor size={18} />
          </IconButton>
        </Box>

        {/* Action Buttons */}
        <Button
          variant="outlined"
          startIcon={<Eye size={16} />}
          onClick={onTogglePreview}
          sx={{ mr: 1 }}
        >
          {editorState.isPreviewOpen ? 'Hide' : 'Show'} Preview
        </Button>

        <Button
          variant="outlined"
          startIcon={<Save size={16} />}
          onClick={onSave}
          sx={{ mr: 1 }}
        >
          Save
        </Button>

        <Button
          variant="contained"
          startIcon={<Download size={16} />}
          onClick={onExport}
          sx={{ mr: 1 }}
        >
          Export
        </Button>

        <Button
          variant="outlined"
          startIcon={<Bug size={16} />}
          onClick={handleDebug}
          sx={{ mr: 1 }}
          color="warning"
        >
          Debug
        </Button>

        <IconButton color="default">
          <Settings size={20} />
        </IconButton>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
