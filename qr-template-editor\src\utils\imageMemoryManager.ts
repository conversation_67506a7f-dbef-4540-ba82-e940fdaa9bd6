/**
 * Image Memory Manager - Handle temporary image storage with blob URLs
 * for real-time preview without immediate database saves
 */

export interface ImageMemoryItem {
  id: string;
  file: File;
  blobUrl: string;
  base64?: string;
  width?: number;
  height?: number;
  sizeKB?: number;
  timestamp: number;
}

class ImageMemoryManager {
  private images: Map<string, ImageMemoryItem> = new Map();
  private maxAge = 30 * 60 * 1000; // 30 minutes

  /**
   * Store image in memory with blob URL for immediate use
   */
  store(id: string, file: File, metadata?: Partial<ImageMemoryItem>): string {
    // Clean up old blob URL if exists
    this.cleanup(id);

    // Create new blob URL
    const blobUrl = URL.createObjectURL(file);
    
    const item: ImageMemoryItem = {
      id,
      file,
      blobUrl,
      timestamp: Date.now(),
      ...metadata
    };

    this.images.set(id, item);
    console.log(`📦 Stored image in memory: ${id} → ${blobUrl}`);
    
    return blobUrl;
  }

  /**
   * Get image from memory
   */
  get(id: string): ImageMemoryItem | null {
    const item = this.images.get(id);
    if (!item) return null;

    // Check if expired
    if (Date.now() - item.timestamp > this.maxAge) {
      this.cleanup(id);
      return null;
    }

    return item;
  }

  /**
   * Update image metadata (e.g., base64 after processing)
   */
  updateMetadata(id: string, metadata: Partial<ImageMemoryItem>): void {
    const item = this.images.get(id);
    if (item) {
      Object.assign(item, metadata);
      console.log(`📝 Updated metadata for: ${id}`);
    }
  }

  /**
   * Get blob URL for immediate display
   */
  getBlobUrl(id: string): string | null {
    const item = this.get(id);
    return item?.blobUrl || null;
  }

  /**
   * Get base64 for database save
   */
  getBase64(id: string): string | null {
    const item = this.get(id);
    return item?.base64 || null;
  }

  /**
   * Remove image from memory and cleanup blob URL
   */
  cleanup(id: string): void {
    const item = this.images.get(id);
    if (item) {
      URL.revokeObjectURL(item.blobUrl);
      this.images.delete(id);
      console.log(`🗑️ Cleaned up image: ${id}`);
    }
  }

  /**
   * Remove all images from memory
   */
  cleanupAll(): void {
    for (const [id] of this.images) {
      this.cleanup(id);
    }
    console.log('🗑️ Cleaned up all images from memory');
  }

  /**
   * Get all images for batch save
   */
  getAllForSave(): { id: string; base64: string }[] {
    const result: { id: string; base64: string }[] = [];
    
    for (const [id, item] of this.images) {
      if (item.base64) {
        result.push({ id, base64: item.base64 });
      }
    }
    
    return result;
  }

  /**
   * Check if image is in memory (not saved to DB yet)
   */
  isPending(id: string): boolean {
    return this.images.has(id);
  }

  /**
   * Get memory usage info
   */
  getMemoryInfo(): { count: number; totalSizeKB: number } {
    let totalSizeKB = 0;
    
    for (const item of this.images.values()) {
      totalSizeKB += item.sizeKB || 0;
    }
    
    return {
      count: this.images.size,
      totalSizeKB: Math.round(totalSizeKB)
    };
  }

  /**
   * Auto cleanup expired images
   */
  autoCleanup(): void {
    const now = Date.now();
    const expired: string[] = [];
    
    for (const [id, item] of this.images) {
      if (now - item.timestamp > this.maxAge) {
        expired.push(id);
      }
    }
    
    expired.forEach(id => this.cleanup(id));
    
    if (expired.length > 0) {
      console.log(`🧹 Auto-cleaned ${expired.length} expired images`);
    }
  }
}

// Singleton instance
export const imageMemoryManager = new ImageMemoryManager();

// Auto cleanup every 5 minutes
setInterval(() => {
  imageMemoryManager.autoCleanup();
}, 5 * 60 * 1000);

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  imageMemoryManager.cleanupAll();
});

export default imageMemoryManager;
