import type { BusinessData } from '../types/index';

export const generateHTML = (businessData: BusinessData): string => {
  const {
    pageTitle,
    businessName,
    logoUrl,
    faviconUrl,
    phone,
    email,
    address,
    hours,
    websiteUrl,
    bookingUrl,
    facebookUrl,
    instagramUrl,
    googleMapsUrl,
    template,
    primaryColor,
    secondaryColor,
    customCSS,
    galleryImages
  } = businessData;

  const templateStyles = getTemplateStyles(template, primaryColor, secondaryColor);
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <link rel="icon" type="image/x-icon" href="${faviconUrl}">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        ${templateStyles}
        ${customCSS || ''}
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            ${logoUrl ? `<img src="${logoUrl}" alt="${businessName}" class="logo">` : ''}
            <h1 class="business-name">${businessName}</h1>
        </header>

        <!-- Contact Info -->
        <section class="contact-section">
            <div class="contact-item">
                <i class="ri-phone-line"></i>
                <a href="tel:${phone.replace(/\D/g, '')}" class="contact-link">${phone}</a>
            </div>
            
            ${email ? `
            <div class="contact-item">
                <i class="ri-mail-line"></i>
                <a href="mailto:${email}" class="contact-link">${email}</a>
            </div>
            ` : ''}
            
            <div class="contact-item">
                <i class="ri-map-pin-line"></i>
                <span class="contact-text">${address.replace(/\n/g, '<br>')}</span>
            </div>
            
            <div class="contact-item">
                <i class="ri-time-line"></i>
                <span class="contact-text">${hours.replace(/\n/g, '<br>')}</span>
            </div>
        </section>

        <!-- Action Buttons -->
        <section class="actions-section">
            ${websiteUrl ? `<a href="${websiteUrl}" class="action-btn primary" target="_blank">
                <i class="ri-global-line"></i> Visit Website
            </a>` : ''}
            
            ${bookingUrl ? `<a href="${bookingUrl}" class="action-btn secondary" target="_blank">
                <i class="ri-calendar-line"></i> Book Appointment
            </a>` : ''}
            
            ${googleMapsUrl ? `<a href="${googleMapsUrl}" class="action-btn" target="_blank">
                <i class="ri-map-line"></i> Get Directions
            </a>` : ''}
        </section>

        <!-- Social Media -->
        ${(facebookUrl || instagramUrl) ? `
        <section class="social-section">
            <h3>Follow Us</h3>
            <div class="social-links">
                ${facebookUrl ? `<a href="${facebookUrl}" class="social-link" target="_blank">
                    <i class="ri-facebook-fill"></i>
                </a>` : ''}
                
                ${instagramUrl ? `<a href="${instagramUrl}" class="social-link" target="_blank">
                    <i class="ri-instagram-line"></i>
                </a>` : ''}
            </div>
        </section>
        ` : ''}

        <!-- Gallery -->
        ${galleryImages.length > 0 ? `
        <section class="gallery-section">
            <h3>Gallery</h3>
            <div class="gallery-grid">
                ${galleryImages.map(img => `<img src="${img}" alt="Gallery" class="gallery-image">`).join('')}
            </div>
        </section>
        ` : ''}
    </div>
</body>
</html>`;
};

const getTemplateStyles = (template: string, primaryColor: string, secondaryColor: string): string => {
  const baseStyles = `
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f5f5f5;
    }
    
    .container {
        max-width: 400px;
        margin: 0 auto;
        background: white;
        min-height: 100vh;
        padding: 20px;
    }
    
    .header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid ${primaryColor};
    }
    
    .logo {
        max-width: 120px;
        height: auto;
        margin-bottom: 15px;
    }
    
    .business-name {
        font-size: 24px;
        font-weight: bold;
        color: ${primaryColor};
        margin-bottom: 10px;
    }
  `;

  const templateSpecificStyles = {
    modern: `
      .contact-section {
        background: linear-gradient(135deg, ${primaryColor}10, ${secondaryColor}10);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
      }
      
      .action-btn {
        background: linear-gradient(135deg, ${primaryColor}, ${secondaryColor});
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        text-decoration: none;
        display: inline-block;
        margin: 5px;
        transition: transform 0.3s ease;
      }
      
      .action-btn:hover {
        transform: translateY(-2px);
      }
    `,
    luxury: `
      .contact-section {
        background: ${primaryColor}15;
        border: 2px solid ${primaryColor};
        padding: 25px;
        margin-bottom: 30px;
      }
      
      .action-btn {
        background: ${primaryColor};
        color: white;
        padding: 15px 25px;
        border: none;
        text-decoration: none;
        display: inline-block;
        margin: 8px;
        font-weight: bold;
        letter-spacing: 1px;
      }
    `,
    minimal: `
      .contact-section {
        border-left: 4px solid ${primaryColor};
        padding: 20px;
        margin-bottom: 25px;
        background: #fafafa;
      }
      
      .action-btn {
        background: transparent;
        color: ${primaryColor};
        border: 2px solid ${primaryColor};
        padding: 10px 20px;
        text-decoration: none;
        display: inline-block;
        margin: 5px;
        transition: all 0.3s ease;
      }
      
      .action-btn:hover {
        background: ${primaryColor};
        color: white;
      }
    `,
    cute: `
      .contact-section {
        background: ${primaryColor}20;
        border-radius: 20px;
        padding: 20px;
        margin-bottom: 25px;
        border: 3px dashed ${primaryColor};
      }
      
      .action-btn {
        background: ${primaryColor};
        color: white;
        padding: 12px 20px;
        border-radius: 20px;
        text-decoration: none;
        display: inline-block;
        margin: 5px;
        box-shadow: 0 4px 15px ${primaryColor}40;
      }
    `
  };

  const commonStyles = `
    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      font-size: 14px;
    }
    
    .contact-item i {
      color: ${primaryColor};
      margin-right: 10px;
      font-size: 18px;
      width: 20px;
    }
    
    .contact-link {
      color: ${secondaryColor};
      text-decoration: none;
    }
    
    .contact-text {
      color: #555;
    }
    
    .actions-section {
      text-align: center;
      margin-bottom: 25px;
    }
    
    .social-section {
      text-align: center;
      margin-bottom: 25px;
    }
    
    .social-section h3 {
      color: ${primaryColor};
      margin-bottom: 15px;
    }
    
    .social-links {
      display: flex;
      justify-content: center;
      gap: 15px;
    }
    
    .social-link {
      color: ${primaryColor};
      font-size: 24px;
      text-decoration: none;
      transition: transform 0.3s ease;
    }
    
    .social-link:hover {
      transform: scale(1.2);
    }
    
    .gallery-section h3 {
      color: ${primaryColor};
      margin-bottom: 15px;
      text-align: center;
    }
    
    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 10px;
    }
    
    .gallery-image {
      width: 100%;
      height: 100px;
      object-fit: cover;
      border-radius: 8px;
    }
  `;

  return baseStyles + (templateSpecificStyles[template as keyof typeof templateSpecificStyles] || templateSpecificStyles.modern) + commonStyles;
};
