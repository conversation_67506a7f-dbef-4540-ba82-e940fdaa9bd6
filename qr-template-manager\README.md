# QR Template Management System

A comprehensive template management system for creating and managing QR code business pages with customizable templates.

## 🚀 Features

### 📋 Template Library
- Browse and search templates by category
- Advanced template preview with live rendering
- **Full Template Editor** with 5-tab interface:
  - Basic Info: Name, category, description, version, author, tags
  - HTML Code: Live HTML editor with syntax highlighting
  - CSS/JS: Separate editors for styling and scripts
  - Preview: Real-time preview with sample data
  - Validation: Template validation with errors/warnings
- **Delete templates** with confirmation dialog
- Template validation and feature detection
- Download templates as HTML files
- Rating and usage statistics
- Success/error notifications

### 👥 Business Manager
- Create business instances from templates
- Full-featured business editor with tabbed interface
- Live preview of generated business pages
- Export business pages as HTML files
- Track export history and analytics

### 📤 Template Upload
- Upload custom HTML templates with validation
- Support for HTML, CSS, and JS files
- Automatic feature detection
- Template syntax validation
- Preview image upload

### 🔧 Template Engine
- Mustache-like template syntax
- Variable replacement: `{{BUSINESS_NAME}}`
- Conditional sections: `{{#HAS_LOGO}}...{{/HAS_LOGO}}`
- Array loops: `{{#SERVICES}}...{{/SERVICES}}`
- Responsive design support

## 🏗️ Tech Stack

- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: Material-UI v6+
- **Icons**: Lucide React
- **Backend**: JSON Server (for development)
- **Styling**: Material-UI Theme System

## 📦 Installation

```bash
# Install dependencies
npm install

# Start development server (both JSON server and React app)
npm run dev:full

# Or run separately:
npm run server  # JSON Server on port 3001
npm run dev     # React app on port 5173
```

## 🎯 New Features Added

### ✅ Template Editor
- **5-Tab Interface**: Basic Info, HTML Code, CSS/JS, Preview, Validation
- **Live Preview**: Real-time rendering with sample data
- **Syntax Validation**: Template syntax checking
- **Feature Detection**: Automatic feature identification
- **Version Control**: Template versioning support

### ✅ Template Management
- **Delete Templates**: Safe deletion with confirmation
- **Edit Templates**: Full CRUD operations
- **Notifications**: Success/error feedback
- **API Integration**: RESTful operations

### ✅ Enhanced UI/UX
- **Confirmation Dialogs**: Safe delete operations
- **Notification System**: User feedback
- **Error Handling**: Comprehensive error management
- **Material-UI v6+**: Latest Grid system compatibility

## 🎯 Usage

### Template Editor Features

#### Basic Info Tab
- Template name and description
- Category selection
- Version and author information
- Tags management
- Public/private visibility

#### HTML Code Tab
- Full HTML editor with monospace font
- Real-time validation
- Template variable support
- Syntax highlighting

#### CSS/JS Tab
- Separate editors for CSS and JavaScript
- Optional styling and scripting
- Monospace font for code editing

#### Preview Tab
- Live iframe preview
- Sample data injection
- Real-time updates
- Responsive preview

#### Validation Tab
- Template syntax validation
- Error and warning display
- Feature detection results
- Validation score

### Template Management

#### Edit Template
1. Click **Edit** button on any template
2. Modify template in 5-tab editor
3. Preview changes in real-time
4. Save with validation

#### Delete Template
1. Click **Delete** button on template
2. Confirm deletion in dialog
3. Template removed permanently
4. Success notification shown

## 📁 Project Structure

```
qr-template-manager/
├── src/
│   ├── components/
│   │   ├── TemplateLibrary.tsx      # Browse & manage templates
│   │   ├── TemplateEditor.tsx       # 5-tab template editor
│   │   ├── TemplatePreview.tsx      # Template preview modal
│   │   ├── BusinessManager.tsx      # Manage businesses
│   │   ├── BusinessEditor.tsx       # Business editor modal
│   │   ├── TemplateUpload.tsx       # Upload new templates
│   │   ├── ConfirmDialog.tsx        # Confirmation dialogs
│   │   ├── NotificationSnackbar.tsx # Success/error notifications
│   │   └── ApiStatus.tsx            # API connection status
│   ├── types/
│   │   └── template.ts              # TypeScript interfaces
│   ├── utils/
│   │   ├── templateEngine.ts        # Template rendering engine
│   │   ├── templateValidator.ts     # Template validation
│   │   └── api.ts                   # API utilities
│   └── App.tsx                      # Main application
├── db.json                          # Sample data (JSON Server)
└── package.json                     # Dependencies and scripts
```

## 🔧 API Endpoints

### Templates
- `GET /templates` - Get all templates
- `GET /templates/:id` - Get template by ID
- `POST /templates` - Create new template
- `PUT /templates/:id` - Update template
- `DELETE /templates/:id` - Delete template

### Businesses
- `GET /businesses` - Get all businesses
- `GET /businesses/:id` - Get business by ID
- `POST /businesses` - Create new business
- `PUT /businesses/:id` - Update business
- `DELETE /businesses/:id` - Delete business

## 🚀 Development

```bash
# Install dependencies
npm install

# Start development
npm run dev:full

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📝 Template Requirements

Templates must follow this syntax for compatibility:

### Required Variables
```html
{{BUSINESS_NAME}} {{PHONE}} {{EMAIL}} {{ADDRESS}}
```

### Conditional Sections
```html
{{#HAS_LOGO}}<img src="{{LOGO_URL}}">{{/HAS_LOGO}}
{{#HAS_SERVICES}}...{{/HAS_SERVICES}}
```

### Array Loops
```html
{{#SERVICES}}
<div class="service">
  <h3>{{name}}</h3>
  <p>{{description}}</p>
  <span>{{price}}</span>
</div>
{{/SERVICES}}
```

## 🎨 Template Categories

- `business` - General business templates
- `restaurant` - Restaurant and food service
- `beauty` - Beauty salons and spas
- `healthcare` - Medical and healthcare
- `retail` - Retail and shopping
- `service` - Service-based businesses
- `event` - Events and entertainment
- `portfolio` - Personal portfolios
- `custom` - Custom templates

## 📞 Support

For support and questions, please open an issue in the repository.
