/**
 * API Utility Functions
 */

const API_BASE_URL = 'http://localhost:3001';

export class ApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Enhanced fetch with error handling
 */
export async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new ApiError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    
    // Network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new ApiError('Network error: Unable to connect to server. Please check if the server is running on port 3001.');
    }
    
    throw new ApiError(`Request failed: ${error.message}`);
  }
}

/**
 * Check if API server is available
 */
export async function checkApiHealth(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/templates?_limit=1`);
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * API endpoints
 */
export const api = {
  // Templates
  getTemplates: (params?: URLSearchParams) => 
    apiRequest<any[]>(`/templates${params ? `?${params}` : ''}`),
  
  getTemplate: (id: string) => 
    apiRequest<any>(`/templates/${id}`),
  
  createTemplate: (data: any) => 
    apiRequest<any>('/templates', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
  
  updateTemplate: (id: string, data: any) => 
    apiRequest<any>(`/templates/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
  
  deleteTemplate: (id: string) => 
    apiRequest<void>(`/templates/${id}`, {
      method: 'DELETE',
    }),

  // Businesses
  getBusinesses: () => 
    apiRequest<any[]>('/businesses'),
  
  getBusiness: (id: string) => 
    apiRequest<any>(`/businesses/${id}`),
  
  createBusiness: (data: any) => 
    apiRequest<any>('/businesses', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
  
  updateBusiness: (id: string, data: any) => 
    apiRequest<any>(`/businesses/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
  
  deleteBusiness: (id: string) => 
    apiRequest<void>(`/businesses/${id}`, {
      method: 'DELETE',
    }),
};
