import React from 'react';
import {
  <PERSON>,
  CardContent,
  TextField,
  Typography,
  <PERSON>,
  Alert,
  Button
} from '@mui/material';
import { Image, Plus } from 'lucide-react';
import type { BusinessData } from '../../types/index';
import ImageUpload from '../Upload/ImageUpload';
import GalleryDisplay from '../Upload/GalleryDisplay';
import { IMAGE_PRESETS, type ImageUploadResult } from '../../utils/imageUtils';

interface MediaTabProps {
  businessData: BusinessData;
  onUpdate: (field: keyof BusinessData, value: any) => void;
  onBatchUpdate?: (updates: Partial<BusinessData>) => void;
}

const MediaTab: React.FC<MediaTabProps> = ({ businessData, onUpdate, onBatchUpdate }) => {
  const [newImageUrl, setNewImageUrl] = React.useState('');
  const [isUploading, setIsUploading] = React.useState(false);

  const handleAddImage = () => {
    if (newImageUrl.trim()) {
      const updatedImages = [...businessData.galleryImages, newImageUrl.trim()];
      onUpdate('galleryImages', updatedImages);
      setNewImageUrl('');
    }
  };



  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddImage();
    }
  };

  const handleGalleryUpload = async (result: ImageUploadResult) => {
    setIsUploading(true);
    try {
      const updatedImages = [...businessData.galleryImages, result.dataUrl];
      const updatedFiles = [...(businessData.galleryFiles || []), result.file];

      // 🚀 BATCH UPDATE - Update both fields atomically
      if (onBatchUpdate) {
        onBatchUpdate({
          galleryImages: updatedImages,
          galleryFiles: updatedFiles
        });
      } else {
        // Fallback to individual updates
        onUpdate('galleryImages', updatedImages);
        onUpdate('galleryFiles', updatedFiles);
      }
    } catch (error) {
      console.error('Failed to add gallery image:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleMultipleGalleryUpload = async (results: ImageUploadResult[]) => {
    setIsUploading(true);
    try {
      const newImages = results.map(result => result.dataUrl);
      const newFiles = results.map(result => result.file);

      const updatedImages = [...businessData.galleryImages, ...newImages];
      const updatedFiles = [...(businessData.galleryFiles || []), ...newFiles];

      // 🚀 BATCH UPDATE - Update both fields atomically
      if (onBatchUpdate) {
        onBatchUpdate({
          galleryImages: updatedImages,
          galleryFiles: updatedFiles
        });
      } else {
        // Fallback to individual updates
        onUpdate('galleryImages', updatedImages);
        onUpdate('galleryFiles', updatedFiles);
      }
    } catch (error) {
      console.error('Failed to add gallery images:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveGalleryImage = (index: number) => {
    const updatedImages = businessData.galleryImages.filter((_, i) => i !== index);
    const updatedFiles = (businessData.galleryFiles || []).filter((_, i) => i !== index);

    // 🚀 BATCH UPDATE - Update both fields atomically
    if (onBatchUpdate) {
      onBatchUpdate({
        galleryImages: updatedImages,
        galleryFiles: updatedFiles
      });
    } else {
      // Fallback to individual updates
      onUpdate('galleryImages', updatedImages);
      onUpdate('galleryFiles', updatedFiles);
    }
  };

  const handleReorderGalleryImages = (fromIndex: number, toIndex: number) => {
    const updatedImages = [...businessData.galleryImages];
    const updatedFiles = [...(businessData.galleryFiles || [])];

    // Move image
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);

    // Move corresponding file if exists
    if (updatedFiles[fromIndex]) {
      const [movedFile] = updatedFiles.splice(fromIndex, 1);
      updatedFiles.splice(toIndex, 0, movedFile);
    }

    // 🚀 BATCH UPDATE - Update both fields atomically
    if (onBatchUpdate) {
      onBatchUpdate({
        galleryImages: updatedImages,
        galleryFiles: updatedFiles
      });
    } else {
      // Fallback to individual updates
      onUpdate('galleryImages', updatedImages);
      onUpdate('galleryFiles', updatedFiles);
    }
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              bgcolor: 'warning.main',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            <Image size={20} color="white" />
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Media & Gallery
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Add images to showcase your business
            </Typography>
          </Box>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          Add image URLs to create a gallery that showcases your business. Images should be publicly accessible URLs.
        </Alert>

        <ImageUpload
          label="Upload Gallery Images"
          description="Add high-quality images to showcase your business. You can upload up to 5 images at once."
          options={IMAGE_PRESETS.gallery}
          onImageUpload={handleGalleryUpload}
          onMultipleImageUpload={handleMultipleGalleryUpload}
          multiple={true}
          maxFiles={5}
        />

        {/* Logo URL */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Logo
          </Typography>
          <TextField
            label="Logo URL"
            value={businessData.logoUrl}
            onChange={(e) => onUpdate('logoUrl', e.target.value)}
            fullWidth
            placeholder="https://example.com/logo.png"
            helperText="URL to your business logo image"
          />
        </Box>

        {/* Favicon URL */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Favicon
          </Typography>
          <TextField
            label="Favicon URL"
            value={businessData.faviconUrl}
            onChange={(e) => onUpdate('faviconUrl', e.target.value)}
            fullWidth
            placeholder="https://example.com/favicon.ico"
            helperText="Small icon that appears in browser tabs"
          />
        </Box>

        {/* Gallery Images */}
        <Box>
          <Typography variant="h6" gutterBottom>
            Gallery Images
          </Typography>
          
          {/* Add new image */}
          <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
            <TextField
              label="Image URL"
              value={newImageUrl}
              onChange={(e) => setNewImageUrl(e.target.value)}
              onKeyDown={handleKeyPress}
              fullWidth
              placeholder="https://example.com/image.jpg"
              size="small"
            />
            <Button
              variant="contained"
              startIcon={<Plus size={16} />}
              onClick={handleAddImage}
              disabled={!newImageUrl.trim()}
              sx={{ minWidth: 'auto', px: 2 }}
            >
              Add
            </Button>
          </Box>

          {/* Gallery Display */}
          <GalleryDisplay
            images={businessData.galleryImages}
            onRemove={handleRemoveGalleryImage}
            onReorder={handleReorderGalleryImages}
            maxImages={10}
            title="Gallery Images"
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default MediaTab;
