import type { ValidationResult } from '../types/index';

export const validateRequired = (value: string, fieldName: string): ValidationResult => {
  if (!value.trim()) {
    return {
      isValid: false,
      message: `${fieldName} is required`,
      type: 'error'
    };
  }
  return {
    isValid: true,
    message: 'Looks good!',
    type: 'success'
  };
};

export const validateEmail = (email: string): ValidationResult => {
  if (!email.trim()) {
    return { isValid: true, message: '', type: 'success' }; // Optional field
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return {
      isValid: false,
      message: 'Please enter a valid email address',
      type: 'error'
    };
  }
  
  return {
    isValid: true,
    message: 'Valid email address',
    type: 'success'
  };
};

export const validateUrl = (url: string, fieldName: string): ValidationResult => {
  if (!url.trim()) {
    return { isValid: true, message: '', type: 'success' }; // Optional field
  }
  
  try {
    new URL(url);
    return {
      isValid: true,
      message: 'Valid URL',
      type: 'success'
    };
  } catch {
    return {
      isValid: false,
      message: `Please enter a valid ${fieldName} URL`,
      type: 'error'
    };
  }
};

export const validatePhone = (phone: string): ValidationResult => {
  if (!phone.trim()) {
    return {
      isValid: false,
      message: 'Phone number is required',
      type: 'error'
    };
  }
  
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
  
  if (!phoneRegex.test(cleanPhone) || cleanPhone.length < 10) {
    return {
      isValid: false,
      message: 'Please enter a valid phone number',
      type: 'error'
    };
  }
  
  return {
    isValid: true,
    message: 'Valid phone number',
    type: 'success'
  };
};

export const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0,3)}) ${cleaned.slice(3,6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1,4)}) ${cleaned.slice(4,7)}-${cleaned.slice(7)}`;
  }
  
  return phone; // Return original if can't format
};

export const validateBusinessData = (data: any) => {
  const validations: { [key: string]: ValidationResult } = {};
  
  validations.pageTitle = validateRequired(data.pageTitle, 'Page Title');
  validations.businessName = validateRequired(data.businessName, 'Business Name');
  validations.phone = validatePhone(data.phone);
  validations.address = validateRequired(data.address, 'Address');
  validations.hours = validateRequired(data.hours, 'Business Hours');
  
  if (data.email) {
    validations.email = validateEmail(data.email);
  }
  
  if (data.websiteUrl) {
    validations.websiteUrl = validateUrl(data.websiteUrl, 'Website');
  }
  
  if (data.facebookUrl) {
    validations.facebookUrl = validateUrl(data.facebookUrl, 'Facebook');
  }
  
  if (data.instagramUrl) {
    validations.instagramUrl = validateUrl(data.instagramUrl, 'Instagram');
  }
  
  if (data.logoUrl) {
    validations.logoUrl = validateUrl(data.logoUrl, 'Logo');
  }
  
  if (data.faviconUrl) {
    validations.faviconUrl = validateUrl(data.faviconUrl, 'Favicon');
  }
  
  return validations;
};
