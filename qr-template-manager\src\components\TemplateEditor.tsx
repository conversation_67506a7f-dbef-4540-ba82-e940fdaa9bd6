import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Tabs,
  Tab,
  IconButton,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Alert,
  Paper,
  Grid
} from '@mui/material';
import { X, Save, Eye, Code, CheckCircle, AlertCircle } from 'lucide-react';
import type { Template, TemplateCategory } from '../types/template';
import { templateValidator } from '../utils/templateValidator';

interface TemplateEditorProps {
  template: Template | null;
  open: boolean;
  onClose: () => void;
  onSave: (template: Template) => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`editor-tabpanel-${index}`}
      aria-labelledby={`editor-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2 }}>{children}</Box>}
    </div>
  );
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({
  template,
  open,
  onClose,
  onSave
}) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [editedTemplate, setEditedTemplate] = useState<Template | null>(null);
  const [validation, setValidation] = useState<any>(null);
  const [previewHtml, setPreviewHtml] = useState('');

  useEffect(() => {
    if (template) {
      setEditedTemplate({ ...template });
      validateTemplate(template.htmlContent);
      generatePreview(template.htmlContent);
    }
  }, [template]);

  const validateTemplate = (html: string) => {
    try {
      const result = templateValidator.validateTemplate(html);
      setValidation(result);
    } catch (error) {
      setValidation({
        isValid: false,
        errors: ['Validation failed'],
        warnings: [],
        features: []
      });
    }
  };

  const generatePreview = (html: string) => {
    // Generate preview with sample data
    const sampleData = {
      BUSINESS_NAME: 'Sample Business',
      SLOGAN: 'Your Success is Our Mission',
      DESCRIPTION: 'We provide excellent services to help your business grow.',
      PHONE: '(*************',
      EMAIL: '<EMAIL>',
      ADDRESS: '123 Main Street, City, State 12345',
      HOURS: 'Mon-Fri: 9AM-6PM',
      LOGO_URL: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTUwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzE5NzZkMiIvPjx0ZXh0IHg9Ijc1IiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+TE9HTzwvdGV4dD48L3N2Zz4=',
      PRIMARY_COLOR: '#1976d2',
      SECONDARY_COLOR: '#dc004e',
      FACEBOOK_URL: 'https://facebook.com/sample',
      INSTAGRAM_URL: 'https://instagram.com/sample'
    };

    let processedHtml = html;

    // Replace simple variables
    Object.entries(sampleData).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      processedHtml = processedHtml.replace(regex, value);
    });

    // Handle conditional sections
    processedHtml = processedHtml.replace(/\{\{#HAS_LOGO\}\}([\s\S]*?)\{\{\/HAS_LOGO\}\}/g, '$1');
    processedHtml = processedHtml.replace(/\{\{#HAS_SERVICES\}\}([\s\S]*?)\{\{\/HAS_SERVICES\}\}/g, 
      '<div class="services"><div class="service"><h3>Sample Service</h3><p>Service description</p><p><strong>$50</strong></p></div></div>');
    processedHtml = processedHtml.replace(/\{\{#HAS_SOCIAL_LINKS\}\}([\s\S]*?)\{\{\/HAS_SOCIAL_LINKS\}\}/g, '$1');
    
    // Remove any remaining template syntax
    processedHtml = processedHtml.replace(/\{\{#[^}]+\}\}/g, '');
    processedHtml = processedHtml.replace(/\{\{\/[^}]+\}\}/g, '');
    processedHtml = processedHtml.replace(/\{\{[^}]+\}\}/g, '');

    setPreviewHtml(processedHtml);
  };

  const handleSave = () => {
    if (!editedTemplate) return;

    const updatedTemplate = {
      ...editedTemplate,
      updatedAt: new Date().toISOString(),
      validationResult: validation
    };

    onSave(updatedTemplate);
  };

  const updateTemplate = (field: string, value: any) => {
    if (!editedTemplate) return;
    
    const updated = { ...editedTemplate, [field]: value };
    setEditedTemplate(updated);

    // Re-validate and preview if HTML changed
    if (field === 'htmlContent') {
      validateTemplate(value);
      generatePreview(value);
    }
  };

  const updateMetadata = (field: string, value: any) => {
    if (!editedTemplate) return;
    
    setEditedTemplate({
      ...editedTemplate,
      metadata: {
        ...editedTemplate.metadata,
        [field]: value
      }
    });
  };

  if (!editedTemplate) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '90vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant="h6">Edit Template</Typography>
          <Typography variant="body2" color="text.secondary">
            {editedTemplate.name}
          </Typography>
        </Box>
        <IconButton onClick={onClose}>
          <X size={20} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
            <Tab label="Basic Info" />
            <Tab label="HTML Code" />
            <Tab label="CSS/JS" />
            <Tab label="Preview" />
            <Tab label="Validation" />
          </Tabs>
        </Box>

        <TabPanel value={currentTab} index={0}>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Template Name"
                value={editedTemplate.name}
                onChange={(e) => updateTemplate('name', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Category</InputLabel>
                <Select
                  value={editedTemplate.category}
                  label="Category"
                  onChange={(e) => updateTemplate('category', e.target.value)}
                >
                  <MenuItem value="business">Business</MenuItem>
                  <MenuItem value="restaurant">Restaurant</MenuItem>
                  <MenuItem value="beauty">Beauty</MenuItem>
                  <MenuItem value="healthcare">Healthcare</MenuItem>
                  <MenuItem value="retail">Retail</MenuItem>
                  <MenuItem value="service">Service</MenuItem>
                  <MenuItem value="event">Event</MenuItem>
                  <MenuItem value="portfolio">Portfolio</MenuItem>
                  <MenuItem value="custom">Custom</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label="Description"
                value={editedTemplate.description}
                onChange={(e) => updateTemplate('description', e.target.value)}
                margin="normal"
                multiline
                rows={3}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Version"
                value={editedTemplate.version}
                onChange={(e) => updateTemplate('version', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                label="Author"
                value={editedTemplate.author || ''}
                onChange={(e) => updateTemplate('author', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label="Tags (comma separated)"
                value={editedTemplate.metadata.tags.join(', ')}
                onChange={(e) => updateMetadata('tags', e.target.value.split(',').map(tag => tag.trim()).filter(Boolean))}
                margin="normal"
              />
            </Grid>
            <Grid size={{ xs: 12 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={editedTemplate.isPublic}
                    onChange={(e) => updateTemplate('isPublic', e.target.checked)}
                  />
                }
                label="Public Template"
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={currentTab} index={1}>
          <TextField
            fullWidth
            label="HTML Content"
            value={editedTemplate.htmlContent}
            onChange={(e) => updateTemplate('htmlContent', e.target.value)}
            multiline
            rows={20}
            sx={{
              '& .MuiInputBase-input': {
                fontFamily: 'monospace',
                fontSize: '14px'
              }
            }}
          />
        </TabPanel>

        <TabPanel value={currentTab} index={2}>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label="CSS Content"
                value={editedTemplate.cssContent || ''}
                onChange={(e) => updateTemplate('cssContent', e.target.value)}
                multiline
                rows={10}
                sx={{
                  '& .MuiInputBase-input': {
                    fontFamily: 'monospace',
                    fontSize: '14px'
                  }
                }}
              />
            </Grid>
            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label="JavaScript Content"
                value={editedTemplate.jsContent || ''}
                onChange={(e) => updateTemplate('jsContent', e.target.value)}
                multiline
                rows={10}
                sx={{
                  '& .MuiInputBase-input': {
                    fontFamily: 'monospace',
                    fontSize: '14px'
                  }
                }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={currentTab} index={3}>
          <Box
            component="iframe"
            srcDoc={previewHtml}
            sx={{
              width: '100%',
              height: '60vh',
              border: '1px solid #e0e0e0',
              borderRadius: 1
            }}
          />
        </TabPanel>

        <TabPanel value={currentTab} index={4}>
          {validation && (
            <Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                {validation.isValid ? (
                  <CheckCircle color="green" size={20} />
                ) : (
                  <AlertCircle color="orange" size={20} />
                )}
                <Typography variant="h6">
                  {validation.isValid ? 'Template is valid' : 'Template has issues'}
                </Typography>
              </Box>

              {validation.errors.length > 0 && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2">Errors:</Typography>
                  {validation.errors.map((error: string, index: number) => (
                    <Typography key={index} variant="body2">• {error}</Typography>
                  ))}
                </Alert>
              )}

              {validation.warnings.length > 0 && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2">Warnings:</Typography>
                  {validation.warnings.map((warning: string, index: number) => (
                    <Typography key={index} variant="body2">• {warning}</Typography>
                  ))}
                </Alert>
              )}

              {validation.features.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>Detected Features:</Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {validation.features.map((feature: string) => (
                      <Chip key={feature} label={feature} size="small" />
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          variant="contained"
          startIcon={<Save size={16} />}
          onClick={handleSave}
          disabled={validation && !validation.isValid}
        >
          Save Template
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TemplateEditor;
