<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Page Template Editor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">
    <style>
        .editor-panel {
            height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        .preview-panel {
            height: calc(100vh - 4rem);
            overflow-y: auto;
        }
        /* Enhanced Device Frames */
        .device-frame {
            margin: 0 auto;
            transition: all 0.3s ease;
        }

        .mobile-frame {
            width: 375px;
            border: 8px solid #333;
            border-radius: 25px;
            background: #333;
            padding: 10px;
        }

        .tablet-frame {
            width: 768px;
            border: 6px solid #333;
            border-radius: 15px;
            background: #333;
            padding: 8px;
        }

        .desktop-frame {
            width: 100%;
            max-width: 1200px;
            border: 4px solid #333;
            border-radius: 8px;
            background: #333;
            padding: 6px;
        }

        .device-screen {
            width: 100%;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        }

        .mobile-frame .device-screen {
            height: 667px;
            border-radius: 15px;
        }

        .tablet-frame .device-screen {
            height: 1024px;
            border-radius: 10px;
        }

        .desktop-frame .device-screen {
            height: 800px;
            border-radius: 4px;
        }

        .device-btn {
            transition: all 0.3s ease;
        }

        .device-btn.active {
            background-color: #3B82F6 !important;
            color: white !important;
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            pointer-events: none;
            z-index: 10;
        }
        .template-option {
            transition: all 0.3s ease;
        }
        .template-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* Image Upload Styles */
        .drop-zone {
            transition: all 0.3s ease;
        }
        .drop-zone.drag-over {
            border-color: #3B82F6;
            background-color: #EFF6FF;
        }
        .gallery-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .gallery-item:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .gallery-item .remove-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(239, 68, 68, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .gallery-item:hover .remove-btn {
            opacity: 1;
        }
        .gallery-item .caption-input {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            padding: 8px;
            font-size: 12px;
        }
        .gallery-item .caption-input::placeholder {
            color: rgba(255,255,255,0.7);
        }

        /* Layout Options Styles */
        .layout-style-option {
            transition: all 0.3s ease;
        }
        .layout-style-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .section-toggle {
            transition: all 0.3s ease;
        }
        .section-order-item {
            cursor: move;
            transition: all 0.3s ease;
        }
        .section-order-item:hover {
            background-color: #f3f4f6;
            transform: translateX(4px);
        }
        .section-order-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }
        .section-order-item .drag-handle {
            cursor: grab;
        }
        .section-order-item .drag-handle:active {
            cursor: grabbing;
        }

        /* Enhanced Content Editor Styles */
        .content-view {
            transition: all 0.3s ease;
        }
        .content-view.hidden {
            display: none;
        }
        .content-view.active {
            display: block;
        }
        .content-view-btn {
            transition: all 0.3s ease;
        }
        .content-view-btn.active {
            background-color: white !important;
            color: #374151 !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* Content Blocks */
        .content-block-item {
            transition: all 0.3s ease;
        }
        .content-block-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .content-block-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        /* Content Canvas */
        #contentCanvas {
            transition: all 0.3s ease;
        }
        #contentCanvas.drag-over {
            border-color: #3B82F6;
            background-color: #EFF6FF;
        }
        .canvas-block {
            position: relative;
            margin: 8px 0;
            padding: 12px;
            border: 2px solid transparent;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .canvas-block:hover {
            border-color: #D1D5DB;
            background-color: #F9FAFB;
        }
        .canvas-block.selected {
            border-color: #3B82F6;
            background-color: #EFF6FF;
        }
        .canvas-block .block-controls {
            position: absolute;
            top: -12px;
            right: 8px;
            display: none;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            padding: 4px;
        }
        .canvas-block:hover .block-controls,
        .canvas-block.selected .block-controls {
            display: flex;
        }
        .block-control-btn {
            padding: 4px 6px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .block-control-btn:hover {
            background-color: #F3F4F6;
        }

        /* Rich Text Editor */
        #richTextEditor {
            outline: none;
        }
        #richTextEditor:empty:before {
            content: attr(placeholder);
            color: #9CA3AF;
            pointer-events: none;
        }
        .editor-btn {
            padding: 6px 8px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s ease;
            color: #6B7280;
        }
        .editor-btn:hover {
            background-color: #E5E7EB;
            color: #374151;
        }
        .editor-btn.active {
            background-color: #3B82F6;
            color: white;
        }

        /* Block Properties Panel */
        #blockPropertiesPanel {
            border-top: 1px solid #E5E7EB;
            padding-top: 16px;
            margin-top: 16px;
        }
        .property-group {
            margin-bottom: 16px;
            padding: 12px;
            background-color: #F9FAFB;
            border-radius: 8px;
        }
        .property-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }
        .property-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #D1D5DB;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .property-input:focus {
            outline: none;
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* CRITICAL LAYOUT - DO NOT OVERRIDE */
        .main-container {
            display: flex !important;
            flex-direction: row !important;
            flex-wrap: nowrap !important;
            height: calc(100vh - 80px) !important;
            width: 100vw !important;
            max-width: 100vw !important;
            overflow: hidden !important;
            position: relative !important;
        }
        .editor-panel {
            width: 50vw !important;
            min-width: 50vw !important;
            max-width: 50vw !important;
            flex: 0 0 50vw !important;
            flex-shrink: 0 !important;
            flex-grow: 0 !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
            border-right: 1px solid #e5e7eb !important;
            height: calc(100vh - 80px) !important;
            position: relative !important;
        }
        .preview-panel {
            width: 50vw !important;
            min-width: 50vw !important;
            max-width: 50vw !important;
            flex: 0 0 50vw !important;
            flex-shrink: 0 !important;
            flex-grow: 0 !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
            height: calc(100vh - 80px) !important;
            position: relative !important;
        }

        /* Force layout integrity */
        .main-container > * {
            float: none !important;
            position: relative !important;
            display: block !important;
        }

        /* Additional layout fixes */
        body {
            margin: 0 !important;
            padding: 0 !important;
            overflow-x: hidden !important;
            width: 100vw !important;
            height: 100vh !important;
        }

        /* Ensure no floating or positioning issues */
        .main-container * {
            box-sizing: border-box !important;
        }

        /* Override any conflicting styles */
        .main-container .editor-panel,
        .main-container .preview-panel {
            clear: none !important;
            float: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* ULTIMATE LAYOUT ENFORCEMENT */
        .main-container {
            background: #f8f9fa !important;
            display: flex !important;
            flex-direction: row !important;
            flex-wrap: nowrap !important;
        }
        .editor-panel {
            background: white !important;
            border-right: 1px solid #e5e7eb !important;
            display: block !important;
            float: none !important;
        }
        .preview-panel {
            background: #f9fafb !important;
            display: block !important;
            float: none !important;
        }

        /* Force side-by-side layout */
        .main-container > .editor-panel {
            order: 1 !important;
        }
        .main-container > .preview-panel {
            order: 2 !important;
        }

        /* Clean layout without debug borders */
        .main-container {
            background: #f8f9fa !important;
        }
        .main-container > .editor-panel {
            background: white !important;
            border-right: 1px solid #e5e7eb !important;
        }
        .main-container > .preview-panel {
            background: #f9fafb !important;
        }

        /* Enhanced Basic View UX Styles */
        .basic-tab {
            transition: all 0.3s ease;
            position: relative;
        }
        .basic-tab.active {
            border-bottom-color: #3B82F6 !important;
            color: #3B82F6 !important;
        }
        .basic-tab:hover:not(.active) {
            border-bottom-color: #D1D5DB !important;
            color: #374151 !important;
        }

        .basic-tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }
        .basic-tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Smart Input Enhancements */
        .smart-input {
            transition: all 0.3s ease;
            position: relative;
        }
        .smart-input:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }
        .smart-input.valid {
            border-color: #10B981;
            background-color: #F0FDF4;
        }
        .smart-input.invalid {
            border-color: #EF4444;
            background-color: #FEF2F2;
        }

        /* Form Group Enhancements */
        .form-group {
            transition: all 0.3s ease;
        }
        .form-group:hover {
            transform: translateY(-1px);
        }

        /* Validation Icons */
        .validation-icon {
            transition: all 0.3s ease;
        }
        .validation-icon.show {
            display: inline-block;
            animation: bounceIn 0.5s ease;
        }

        @keyframes bounceIn {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* Input Feedback */
        .input-feedback {
            transition: all 0.3s ease;
        }
        .input-feedback.show {
            display: block;
        }
        .input-feedback.success {
            color: #10B981;
        }
        .input-feedback.error {
            color: #EF4444;
        }
        .input-feedback.warning {
            color: #F59E0B;
        }

        /* Progress Bar Animation */
        #progressBar {
            transition: width 0.5s ease-in-out;
        }

        /* Button Hover Effects */
        .upload-btn, .preview-btn, .auto-detect-btn, .template-suggestion-btn,
        .import-data-btn, .next-tab-btn, .prev-tab-btn {
            transition: all 0.3s ease;
        }
        .upload-btn:hover, .preview-btn:hover, .auto-detect-btn:hover,
        .template-suggestion-btn:hover, .import-data-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* Tab Navigation Responsive */
        @media (max-width: 768px) {
            .basic-tab {
                font-size: 12px;
                padding: 8px 12px;
            }
            .basic-tab i {
                display: none;
            }
        }

        /* Card Hover Effects */
        .bg-gradient-to-r {
            transition: all 0.3s ease;
        }
        .bg-gradient-to-r:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* Statistics Cards */
        .text-center.p-3 {
            transition: all 0.3s ease;
        }
        .text-center.p-3:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900">
                    <i class="ri-edit-box-line mr-2"></i>
                    QR Page Template Editor
                </h1>
                <div class="flex space-x-3">
                    <button id="exportBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="ri-download-line mr-2"></i>Export HTML
                    </button>
                    <button id="qrBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="ri-qr-code-line mr-2"></i>Generate QR
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-container">
        <!-- Editor Panel -->
        <div class="bg-white border-r editor-panel">
            <div class="p-6">
                <!-- Enhanced Edit Content Header -->
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="ri-edit-line mr-2 text-blue-600"></i>
                        Edit Content
                    </h2>
                    <div class="flex items-center space-x-2">
                        <!-- Content View Toggle -->
                        <div class="flex bg-gray-100 rounded-lg p-1">
                            <button id="basicView" class="content-view-btn active px-3 py-1 rounded text-sm font-medium bg-white shadow-sm">
                                <i class="ri-edit-2-line mr-1"></i>Basic
                            </button>
                            <button id="visualView" class="content-view-btn px-3 py-1 rounded text-sm font-medium text-gray-600 hover:bg-gray-50">
                                <i class="ri-palette-line mr-1"></i>Visual
                            </button>
                            <button id="advancedView" class="content-view-btn px-3 py-1 rounded text-sm font-medium text-gray-600 hover:bg-gray-50">
                                <i class="ri-settings-3-line mr-1"></i>Advanced
                            </button>
                        </div>

                        <!-- Content Actions -->
                        <button id="contentTemplates" class="p-2 bg-purple-100 text-purple-600 rounded-lg hover:bg-purple-200 transition-colors" title="Content Templates">
                            <i class="ri-file-copy-line"></i>
                        </button>
                        <button id="contentPresets" class="p-2 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors" title="Quick Presets">
                            <i class="ri-magic-line"></i>
                        </button>
                    </div>
                </div>

                <!-- Content Editor Container -->
                <div id="contentEditorContainer">
                    <!-- Basic View (Default) -->
                    <div id="basicContentView" class="content-view active">

                        <!-- Basic View Tab Navigation -->
                        <div class="mb-6">
                            <div class="flex flex-wrap border-b border-gray-200">
                                <button class="basic-tab active px-4 py-2 text-sm font-medium border-b-2 border-blue-500 text-blue-600" data-tab="basic-info">
                                    <i class="ri-information-line mr-2"></i>Basic Info
                                </button>
                                <button class="basic-tab px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="media">
                                    <i class="ri-image-line mr-2"></i>Media
                                </button>
                                <button class="basic-tab px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="contact">
                                    <i class="ri-phone-line mr-2"></i>Contact
                                </button>
                                <button class="basic-tab px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="business">
                                    <i class="ri-time-line mr-2"></i>Business
                                </button>
                                <button class="basic-tab px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="social">
                                    <i class="ri-share-line mr-2"></i>Social
                                </button>
                                <button class="basic-tab px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="design">
                                    <i class="ri-palette-line mr-2"></i>Design
                                </button>
                                <button class="basic-tab px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="advanced">
                                    <i class="ri-settings-line mr-2"></i>Advanced
                                </button>
                            </div>
                        </div>

                        <!-- Tab Content Container -->
                        <div id="basicTabContent">

                            <!-- Basic Info Tab -->
                            <div id="basic-info-tab" class="basic-tab-content active">
                                <!-- Progress Indicator -->
                                <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-blue-800">Setup Progress</span>
                                        <span class="text-sm text-blue-600" id="progressText">2/7 Complete</span>
                                    </div>
                                    <div class="w-full bg-blue-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 28%" id="progressBar"></div>
                                    </div>
                                </div>

                                <!-- Enhanced Basic Info Section -->
                                <div class="space-y-6">
                                    <!-- Quick Setup Card -->
                                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
                                        <div class="flex items-center mb-4">
                                            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                                <i class="ri-rocket-line text-white text-lg"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-lg font-semibold text-gray-800">Quick Setup</h3>
                                                <p class="text-sm text-gray-600">Get started with essential information</p>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-1 gap-4">
                                            <!-- Smart Input with Validation -->
                                            <div class="form-group">
                                                <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                                    <i class="ri-text mr-2 text-blue-500"></i>
                                                    Page Title
                                                    <span class="ml-1 text-red-500">*</span>
                                                    <div class="ml-auto">
                                                        <span class="validation-icon hidden">
                                                            <i class="ri-check-line text-green-500"></i>
                                                        </span>
                                                    </div>
                                                </label>
                                                <input type="text" id="pageTitle"
                                                       class="smart-input w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                       value="Unique Nails Lounge - Premium Nail Care"
                                                       placeholder="Enter your page title..."
                                                       data-required="true">
                                                <div class="input-feedback mt-1 text-xs hidden"></div>
                                            </div>

                                            <div class="form-group">
                                                <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                                    <i class="ri-store-line mr-2 text-blue-500"></i>
                                                    Business Name
                                                    <span class="ml-1 text-red-500">*</span>
                                                    <div class="ml-auto">
                                                        <span class="validation-icon hidden">
                                                            <i class="ri-check-line text-green-500"></i>
                                                        </span>
                                                    </div>
                                                </label>
                                                <input type="text" id="businessName"
                                                       class="smart-input w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                       value="UNIQUE NAILS LOUNGE"
                                                       placeholder="Enter your business name..."
                                                       data-required="true">
                                                <div class="input-feedback mt-1 text-xs hidden"></div>
                                            </div>

                                            <!-- Logo Upload Enhanced -->
                                            <div class="form-group">
                                                <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                                    <i class="ri-image-line mr-2 text-blue-500"></i>
                                                    Business Logo
                                                    <div class="ml-auto">
                                                        <span class="validation-icon hidden">
                                                            <i class="ri-check-line text-green-500"></i>
                                                        </span>
                                                    </div>
                                                </label>
                                                <div class="logo-upload-container">
                                                    <input type="url" id="logoUrl"
                                                           class="smart-input w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                           value="https://uniquenaillounge.com/wp-content/uploads/2025/05/Unique-Nails-Lounge-Logo-h300.webp"
                                                           placeholder="Enter logo URL or upload file...">
                                                    <div class="mt-2 flex items-center space-x-2">
                                                        <button type="button" class="upload-btn bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm hover:bg-blue-200 transition-colors">
                                                            <i class="ri-upload-line mr-1"></i>Upload File
                                                        </button>
                                                        <button type="button" class="preview-btn bg-gray-100 text-gray-700 px-3 py-1 rounded-lg text-sm hover:bg-gray-200 transition-colors">
                                                            <i class="ri-eye-line mr-1"></i>Preview
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="input-feedback mt-1 text-xs hidden"></div>
                                            </div>

                                            <!-- Favicon with Smart Detection -->
                                            <div class="form-group">
                                                <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                                    <i class="ri-bookmark-line mr-2 text-blue-500"></i>
                                                    Favicon
                                                    <button type="button" class="ml-2 text-gray-400 hover:text-gray-600" title="Small icon shown in browser tab">
                                                        <i class="ri-question-line text-xs"></i>
                                                    </button>
                                                    <div class="ml-auto">
                                                        <button type="button" class="auto-detect-btn text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                                            Auto-detect
                                                        </button>
                                                    </div>
                                                </label>
                                                <input type="url" id="faviconUrl"
                                                       class="smart-input w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                                       value="https://uniquenaillounge.com/wp-content/uploads/2025/05/Unique-Nails-Lounge-Favicon-h40.webp#7"
                                                       placeholder="Enter favicon URL...">
                                                <div class="input-feedback mt-1 text-xs hidden"></div>
                                            </div>
                                        </div>

                                        <!-- Quick Actions -->
                                        <div class="mt-6 flex items-center justify-between">
                                            <div class="flex items-center space-x-2">
                                                <button type="button" class="template-suggestion-btn bg-purple-100 text-purple-700 px-3 py-2 rounded-lg text-sm hover:bg-purple-200 transition-colors">
                                                    <i class="ri-magic-line mr-1"></i>Use Template
                                                </button>
                                                <button type="button" class="import-data-btn bg-orange-100 text-orange-700 px-3 py-2 rounded-lg text-sm hover:bg-orange-200 transition-colors">
                                                    <i class="ri-download-line mr-1"></i>Import Data
                                                </button>
                                            </div>
                                            <button type="button" class="next-tab-btn bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                                Next: Media <i class="ri-arrow-right-line ml-1"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Tips & Suggestions -->
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                        <div class="flex items-start">
                                            <i class="ri-lightbulb-line text-yellow-600 mt-1 mr-3"></i>
                                            <div>
                                                <h4 class="text-sm font-medium text-yellow-800 mb-1">Pro Tips</h4>
                                                <ul class="text-sm text-yellow-700 space-y-1">
                                                    <li>• Use a clear, memorable business name for better recognition</li>
                                                    <li>• High-quality logos work best (PNG with transparent background)</li>
                                                    <li>• Page title appears in browser tabs and search results</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Media Tab -->
                            <div id="media-tab" class="basic-tab-content hidden">
                                <!-- Media Management Card -->
                                <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                                    <div class="flex items-center mb-4">
                                        <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="ri-image-line text-white text-lg"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-800">Media Management</h3>
                                            <p class="text-sm text-gray-600">Upload and manage your business images</p>
                                        </div>
                                    </div>

                <!-- Image Upload & Management -->
                <div class="space-y-6">
                    <h3 class="text-lg font-medium mb-4 text-gray-700">
                        <i class="ri-image-line mr-2"></i>Image Management
                    </h3>

                    <!-- Logo Upload -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Logo Upload</h4>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors"
                             id="logoDropZone"
                             ondragover="templateEditor.handleDragOver(event)"
                             ondrop="templateEditor.handleDrop(event, 'logo')">
                            <div id="logoPreview" class="mb-3">
                                <img id="logoPreviewImg" src="" alt="Logo Preview" class="max-h-20 mx-auto hidden">
                                <div id="logoPlaceholder" class="text-gray-400">
                                    <i class="ri-image-add-line text-3xl mb-2 block"></i>
                                    <p>Drag & drop logo here or click to upload</p>
                                    <p class="text-sm">Max size: 5MB | Recommended: 400x200px</p>
                                </div>
                            </div>
                            <input type="file" id="logoUpload" accept="image/*" class="hidden">
                            <button type="button" onclick="document.getElementById('logoUpload').click()"
                                    class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="ri-upload-line mr-2"></i>Choose Logo
                            </button>
                            <button type="button" id="removeLogo" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors ml-2 hidden">
                                <i class="ri-delete-bin-line mr-2"></i>Remove
                            </button>
                        </div>
                    </div>

                    <!-- Gallery Upload -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Gallery Images</h4>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors"
                             id="galleryDropZone"
                             ondragover="templateEditor.handleDragOver(event)"
                             ondrop="templateEditor.handleDrop(event, 'gallery')">
                            <div class="text-gray-400 mb-3">
                                <i class="ri-gallery-line text-3xl mb-2 block"></i>
                                <p>Drag & drop gallery images here or click to upload</p>
                                <p class="text-sm">Max size: 5MB each | Recommended: 800x600px</p>
                            </div>
                            <input type="file" id="galleryUpload" accept="image/*" multiple class="hidden">
                            <button type="button" onclick="document.getElementById('galleryUpload').click()"
                                    class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                                <i class="ri-add-line mr-2"></i>Add Images
                            </button>
                        </div>

                        <!-- Gallery Preview -->
                        <div id="galleryPreview" class="mt-4 grid grid-cols-2 gap-3 hidden">
                            <!-- Gallery images will be dynamically added here -->
                        </div>
                    </div>

                                    <!-- Media Statistics -->
                                    <div class="mt-6 grid grid-cols-3 gap-4">
                                        <div class="text-center p-3 bg-white rounded-lg border">
                                            <div class="text-2xl font-bold text-blue-600" id="logoCount">0</div>
                                            <div class="text-xs text-gray-500">Logo</div>
                                        </div>
                                        <div class="text-center p-3 bg-white rounded-lg border">
                                            <div class="text-2xl font-bold text-green-600" id="galleryCount">0</div>
                                            <div class="text-xs text-gray-500">Gallery</div>
                                        </div>
                                        <div class="text-center p-3 bg-white rounded-lg border">
                                            <div class="text-2xl font-bold text-purple-600" id="totalSize">0 MB</div>
                                            <div class="text-xs text-gray-500">Total Size</div>
                                        </div>
                                    </div>

                                    <!-- Media Actions -->
                                    <div class="mt-6 flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <button type="button" class="optimize-images-btn bg-blue-100 text-blue-700 px-3 py-2 rounded-lg text-sm hover:bg-blue-200 transition-colors">
                                                <i class="ri-magic-line mr-1"></i>Optimize All
                                            </button>
                                            <button type="button" class="clear-media-btn bg-red-100 text-red-700 px-3 py-2 rounded-lg text-sm hover:bg-red-200 transition-colors">
                                                <i class="ri-delete-bin-line mr-1"></i>Clear All
                                            </button>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <button type="button" class="prev-tab-btn bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                                                <i class="ri-arrow-left-line mr-1"></i>Back
                                            </button>
                                            <button type="button" class="next-tab-btn bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                                                Next: Contact <i class="ri-arrow-right-line ml-1"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Tab -->
                            <div id="contact-tab" class="basic-tab-content hidden">
                                <!-- Contact Information Card -->
                                <div class="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-xl p-6">
                                    <div class="flex items-center mb-4">
                                        <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="ri-phone-line text-white text-lg"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-800">Contact Information</h3>
                                            <p class="text-sm text-gray-600">How customers can reach you</p>
                                        </div>
                                    </div>

                                    <div class="space-y-6">
                                        <!-- Phone Number with Validation -->
                                        <div class="form-group">
                                            <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                                <i class="ri-phone-line mr-2 text-orange-500"></i>
                                                Phone Number
                                                <span class="ml-1 text-red-500">*</span>
                                                <div class="ml-auto">
                                                    <span class="validation-icon hidden">
                                                        <i class="ri-check-line text-green-500"></i>
                                                    </span>
                                                </div>
                                            </label>
                                            <div class="relative">
                                                <input type="tel" id="phone"
                                                       class="smart-input w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all"
                                                       value="(*************"
                                                       placeholder="(*************"
                                                       data-required="true">
                                                <div class="absolute left-3 top-3 text-gray-400">
                                                    <i class="ri-phone-line"></i>
                                                </div>
                                            </div>
                                            <div class="mt-2 flex items-center space-x-2">
                                                <button type="button" class="format-phone-btn bg-orange-100 text-orange-700 px-2 py-1 rounded text-xs hover:bg-orange-200 transition-colors">
                                                    <i class="ri-magic-line mr-1"></i>Auto Format
                                                </button>
                                                <button type="button" class="test-call-btn bg-green-100 text-green-700 px-2 py-1 rounded text-xs hover:bg-green-200 transition-colors">
                                                    <i class="ri-phone-line mr-1"></i>Test Call
                                                </button>
                                            </div>
                                            <div class="input-feedback mt-1 text-xs hidden"></div>
                                        </div>

                                        <!-- Address with Map Integration -->
                                        <div class="form-group">
                                            <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                                <i class="ri-map-pin-line mr-2 text-orange-500"></i>
                                                Business Address
                                                <span class="ml-1 text-red-500">*</span>
                                                <div class="ml-auto">
                                                    <button type="button" class="verify-address-btn text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                                                        <i class="ri-map-line mr-1"></i>Verify
                                                    </button>
                                                </div>
                                            </label>
                                            <textarea id="address" rows="3"
                                                      class="smart-input w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all"
                                                      placeholder="Enter your business address..."
                                                      data-required="true">2021 Gorden Crossing, Ste #150
Gallatin, TN 37066</textarea>
                                            <div class="mt-2 flex items-center space-x-2">
                                                <button type="button" class="get-coordinates-btn bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs hover:bg-blue-200 transition-colors">
                                                    <i class="ri-navigation-line mr-1"></i>Get Coordinates
                                                </button>
                                                <button type="button" class="preview-map-btn bg-green-100 text-green-700 px-2 py-1 rounded text-xs hover:bg-green-200 transition-colors">
                                                    <i class="ri-map-2-line mr-1"></i>Preview Map
                                                </button>
                                            </div>
                                            <div class="input-feedback mt-1 text-xs hidden"></div>
                                        </div>

                                        <!-- Business Hours Smart Input -->
                                        <div class="form-group">
                                            <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                                <i class="ri-time-line mr-2 text-orange-500"></i>
                                                Business Hours
                                                <div class="ml-auto flex items-center space-x-2">
                                                    <button type="button" class="hours-format-toggle text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                                                        <i class="ri-settings-line mr-1"></i>Advanced
                                                    </button>
                                                    <button type="button" class="hours-template-btn text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                                        <i class="ri-file-copy-line mr-1"></i>Template
                                                    </button>
                                                </div>
                                            </label>
                                            <textarea id="hours" rows="3"
                                                      class="smart-input w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all"
                                                      placeholder="Enter your business hours...">Mon - Fri: 9:00 AM – 8:00 PM
Saturday: 9:00 AM – 6:00 PM
Sunday: 11:00 AM – 5:00 PM</textarea>
                                            <div class="input-feedback mt-1 text-xs hidden"></div>
                                        </div>

                                        <!-- Email (Optional) -->
                                        <div class="form-group">
                                            <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                                <i class="ri-mail-line mr-2 text-orange-500"></i>
                                                Email Address
                                                <span class="ml-2 text-xs text-gray-500">(Optional)</span>
                                            </label>
                                            <div class="relative">
                                                <input type="email" id="email"
                                                       class="smart-input w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all"
                                                       placeholder="<EMAIL>">
                                                <div class="absolute left-3 top-3 text-gray-400">
                                                    <i class="ri-mail-line"></i>
                                                </div>
                                            </div>
                                            <div class="input-feedback mt-1 text-xs hidden"></div>
                                        </div>
                                    </div>

                                    <!-- Contact Actions -->
                                    <div class="mt-6 flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <button type="button" class="import-contact-btn bg-purple-100 text-purple-700 px-3 py-2 rounded-lg text-sm hover:bg-purple-200 transition-colors">
                                                <i class="ri-contacts-line mr-1"></i>Import vCard
                                            </button>
                                            <button type="button" class="validate-all-btn bg-blue-100 text-blue-700 px-3 py-2 rounded-lg text-sm hover:bg-blue-200 transition-colors">
                                                <i class="ri-shield-check-line mr-1"></i>Validate All
                                            </button>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <button type="button" class="prev-tab-btn bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                                                <i class="ri-arrow-left-line mr-1"></i>Back
                                            </button>
                                            <button type="button" class="next-tab-btn bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                                                Next: Business <i class="ri-arrow-right-line ml-1"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                <!-- Business Hours & Services -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-4 text-gray-700">
                        <i class="ri-time-line mr-2"></i>Business Hours & Services
                    </h3>

                    <!-- Operating Hours -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Operating Hours</h4>
                        <div class="space-y-3" id="businessHours">
                            <!-- Days will be dynamically generated -->
                        </div>
                        <button type="button" id="useSimpleHours" class="mt-2 text-sm text-blue-600 hover:text-blue-800">
                            Use simple text format instead
                        </button>
                    </div>

                    <!-- Services -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Services & Pricing</h4>
                        <div id="servicesList" class="space-y-3">
                            <!-- Services will be dynamically added -->
                        </div>
                        <button type="button" id="addService" class="mt-3 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                            <i class="ri-add-line mr-2"></i>Add Service
                        </button>
                    </div>

                    <!-- Special Offers -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Special Offers</h4>
                        <div id="offersList" class="space-y-3">
                            <!-- Offers will be dynamically added -->
                        </div>
                        <button type="button" id="addOffer" class="mt-3 bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-colors">
                            <i class="ri-gift-line mr-2"></i>Add Special Offer
                        </button>
                    </div>
                </div>

                <!-- Links -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-4 text-gray-700">Links & Social Media</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Website URL</label>
                            <input type="url" id="websiteUrl" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                   value="https://uniquenaillounge.com/">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Booking URL</label>
                            <input type="url" id="bookingUrl" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                                   value="https://www.lldtek.org/salon/appt/VkU1Zk1URXpNVFE9">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Facebook URL</label>
                            <input type="url" id="facebookUrl" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   value="https://www.facebook.com/profile.php?id=100091754925855">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Instagram URL</label>
                            <input type="url" id="instagramUrl" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   value="" placeholder="https://instagram.com/yourbusiness">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Google Maps URL</label>
                            <input type="url" id="googleMapsUrl" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   value="https://maps.app.goo.gl/U9buMXZH1SWxdSzc9">
                        </div>
                    </div>
                </div>

                <!-- Template Selection -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-4 text-gray-700">Template Selection</h3>

                    <div class="grid grid-cols-2 gap-3">
                        <div class="template-option border-2 border-blue-500 bg-blue-50 p-3 rounded-lg cursor-pointer" data-template="modern">
                            <div class="w-full h-20 bg-gradient-to-br from-blue-400 to-purple-500 rounded mb-2"></div>
                            <p class="text-sm font-medium text-center">Modern</p>
                        </div>

                        <div class="template-option border-2 border-gray-300 p-3 rounded-lg cursor-pointer hover:border-blue-400" data-template="luxury">
                            <div class="w-full h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded mb-2"></div>
                            <p class="text-sm font-medium text-center">Luxury</p>
                        </div>

                        <div class="template-option border-2 border-gray-300 p-3 rounded-lg cursor-pointer hover:border-blue-400" data-template="minimal">
                            <div class="w-full h-20 bg-gradient-to-br from-gray-400 to-gray-600 rounded mb-2"></div>
                            <p class="text-sm font-medium text-center">Minimal</p>
                        </div>

                        <div class="template-option border-2 border-gray-300 p-3 rounded-lg cursor-pointer hover:border-blue-400" data-template="cute">
                            <div class="w-full h-20 bg-gradient-to-br from-pink-400 to-red-400 rounded mb-2"></div>
                            <p class="text-sm font-medium text-center">Cute</p>
                        </div>
                    </div>
                </div>

                <!-- Advanced Layout Options -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-4 text-gray-700">
                        <i class="ri-layout-line mr-2"></i>Layout Options
                    </h3>

                    <!-- Layout Style -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Layout Style</h4>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="layout-style-option border-2 border-blue-500 bg-blue-50 p-3 rounded-lg cursor-pointer" data-layout="card">
                                <div class="w-full h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded mb-2 flex items-center justify-center">
                                    <div class="space-y-1">
                                        <div class="w-8 h-2 bg-blue-400 rounded"></div>
                                        <div class="w-6 h-1 bg-blue-300 rounded"></div>
                                        <div class="w-7 h-1 bg-blue-300 rounded"></div>
                                    </div>
                                </div>
                                <p class="text-sm font-medium text-center">Card Layout</p>
                            </div>

                            <div class="layout-style-option border-2 border-gray-300 p-3 rounded-lg cursor-pointer hover:border-blue-400" data-layout="full">
                                <div class="w-full h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded mb-2 flex items-center justify-center">
                                    <div class="space-y-1 w-full px-2">
                                        <div class="w-full h-2 bg-gray-400 rounded"></div>
                                        <div class="w-3/4 h-1 bg-gray-300 rounded"></div>
                                        <div class="w-5/6 h-1 bg-gray-300 rounded"></div>
                                    </div>
                                </div>
                                <p class="text-sm font-medium text-center">Full Width</p>
                            </div>
                        </div>
                    </div>

                    <!-- Section Visibility -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Section Visibility</h4>
                        <div class="space-y-3" id="sectionToggles">
                            <!-- Section toggles will be dynamically generated -->
                        </div>
                    </div>

                    <!-- Section Order -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Section Order</h4>
                        <div id="sectionOrder" class="space-y-2">
                            <!-- Draggable section order will be generated -->
                        </div>
                        <p class="text-xs text-gray-500 mt-2">Drag to reorder sections</p>
                    </div>

                    <!-- Responsive Settings -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Responsive Settings</h4>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" id="mobileOptimized" checked class="mr-2">
                                <span class="text-sm">Mobile Optimized</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="showOnDesktop" checked class="mr-2">
                                <span class="text-sm">Show on Desktop</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="compactMode" class="mr-2">
                                <span class="text-sm">Compact Mode</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Theme Settings -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-4 text-gray-700">Theme Settings</h3>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Primary Color</label>
                            <input type="color" id="primaryColor" class="w-full h-12 border border-gray-300 rounded-lg" value="#d4af37">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Secondary Color</label>
                            <input type="color" id="secondaryColor" class="w-full h-12 border border-gray-300 rounded-lg" value="#222222">
                        </div>
                    </div>
                </div>

                <!-- Custom CSS Editor -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-4 text-gray-700">Custom CSS (Advanced)</h3>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Additional CSS</label>
                            <textarea id="customCSS" rows="6" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                                      placeholder="/* Add your custom CSS here */
.custom-button {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-radius: 25px;
}"></textarea>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="enableCustomCSS" class="mr-2">
                            <label for="enableCustomCSS" class="text-sm text-gray-700">Enable Custom CSS</label>
                        </div>
                    </div>
                </div>

                <!-- Data Management -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-4 text-gray-700">
                        <i class="ri-database-line mr-2"></i>Data Management
                    </h3>

                    <!-- Save/Load Business Profile -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Business Profiles</h4>
                        <div class="space-y-3">
                            <div class="flex space-x-2">
                                <input type="text" id="profileName" placeholder="Profile name..."
                                       class="flex-1 p-2 border border-gray-300 rounded-lg text-sm">
                                <button id="saveProfile" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                                    <i class="ri-save-line mr-1"></i>Save
                                </button>
                            </div>

                            <div class="max-h-32 overflow-y-auto">
                                <div id="savedProfiles" class="space-y-2">
                                    <!-- Saved profiles will be listed here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Projects -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Recent Projects</h4>
                        <div class="max-h-32 overflow-y-auto">
                            <div id="recentProjects" class="space-y-2">
                                <!-- Recent projects will be listed here -->
                            </div>
                        </div>
                        <button id="clearRecent" class="mt-2 text-sm text-red-600 hover:text-red-800">
                            Clear recent projects
                        </button>
                    </div>

                    <!-- Backup & Restore -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Backup & Restore</h4>
                        <div class="space-y-3">
                            <div class="flex space-x-2">
                                <button id="exportBackup" class="flex-1 bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                    <i class="ri-download-cloud-line mr-2"></i>Export Backup
                                </button>
                                <button id="importBackup" class="flex-1 bg-orange-500 text-white py-2 rounded-lg hover:bg-orange-600 transition-colors">
                                    <i class="ri-upload-cloud-line mr-2"></i>Import Backup
                                </button>
                            </div>
                            <input type="file" id="backupFile" accept=".json" class="hidden">
                        </div>
                    </div>

                    <!-- Bulk Operations -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium mb-3 text-gray-600">Bulk Operations</h4>
                        <div class="space-y-3">
                            <button id="bulkEdit" class="w-full bg-indigo-500 text-white py-2 rounded-lg hover:bg-indigo-600 transition-colors">
                                <i class="ri-edit-box-line mr-2"></i>Bulk Edit Businesses
                            </button>
                            <button id="resetAll" class="w-full bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition-colors">
                                <i class="ri-refresh-line mr-2"></i>Reset All Data
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Batch Export -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-4 text-gray-700">Batch Export</h3>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Business List (JSON)</label>
                            <textarea id="businessList" rows="8" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                                      placeholder='[
  {
    "businessName": "Nail Salon 1",
    "phone": "(*************",
    "address": "123 Main St\nCity, State 12345",
    "websiteUrl": "https://salon1.com",
    "facebookUrl": "https://facebook.com/salon1"
  },
  {
    "businessName": "Nail Salon 2",
    "phone": "(*************",
    "address": "456 Oak Ave\nCity, State 67890",
    "websiteUrl": "https://salon2.com",
    "facebookUrl": "https://facebook.com/salon2"
  }
]'></textarea>
                        </div>

                        <button id="batchExportBtn" class="w-full bg-purple-600 text-white py-3 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="ri-download-2-line mr-2"></i>Batch Export All
                        </button>
                    </div>
                </div>
                    </div>

                    <!-- Visual Content Editor -->
                    <div id="visualContentView" class="content-view hidden">
                        <!-- Content Blocks -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium mb-4 text-gray-700">
                                <i class="ri-drag-drop-line mr-2"></i>Content Blocks
                            </h3>

                            <!-- Available Blocks -->
                            <div class="mb-4">
                                <h4 class="text-sm font-medium mb-2 text-gray-600">Available Blocks</h4>
                                <div class="grid grid-cols-2 gap-2" id="availableBlocks">
                                    <div class="content-block-item" data-type="header" draggable="true">
                                        <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg cursor-move hover:bg-blue-100 transition-colors">
                                            <i class="ri-h-1 text-blue-600 mr-2"></i>
                                            <span class="text-sm font-medium">Header</span>
                                        </div>
                                    </div>
                                    <div class="content-block-item" data-type="text" draggable="true">
                                        <div class="p-3 bg-green-50 border border-green-200 rounded-lg cursor-move hover:bg-green-100 transition-colors">
                                            <i class="ri-text text-green-600 mr-2"></i>
                                            <span class="text-sm font-medium">Text</span>
                                        </div>
                                    </div>
                                    <div class="content-block-item" data-type="button" draggable="true">
                                        <div class="p-3 bg-purple-50 border border-purple-200 rounded-lg cursor-move hover:bg-purple-100 transition-colors">
                                            <i class="ri-external-link-line text-purple-600 mr-2"></i>
                                            <span class="text-sm font-medium">Button</span>
                                        </div>
                                    </div>
                                    <div class="content-block-item" data-type="contact" draggable="true">
                                        <div class="p-3 bg-orange-50 border border-orange-200 rounded-lg cursor-move hover:bg-orange-100 transition-colors">
                                            <i class="ri-phone-line text-orange-600 mr-2"></i>
                                            <span class="text-sm font-medium">Contact</span>
                                        </div>
                                    </div>
                                    <div class="content-block-item" data-type="social" draggable="true">
                                        <div class="p-3 bg-pink-50 border border-pink-200 rounded-lg cursor-move hover:bg-pink-100 transition-colors">
                                            <i class="ri-share-line text-pink-600 mr-2"></i>
                                            <span class="text-sm font-medium">Social</span>
                                        </div>
                                    </div>
                                    <div class="content-block-item" data-type="spacer" draggable="true">
                                        <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg cursor-move hover:bg-gray-100 transition-colors">
                                            <i class="ri-separator text-gray-600 mr-2"></i>
                                            <span class="text-sm font-medium">Spacer</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Content Canvas -->
                            <div class="mb-4">
                                <h4 class="text-sm font-medium mb-2 text-gray-600">Page Content</h4>
                                <div id="contentCanvas" class="min-h-96 p-4 bg-white border-2 border-dashed border-gray-300 rounded-lg">
                                    <div class="text-center text-gray-500 py-8">
                                        <i class="ri-drag-drop-line text-3xl mb-2 block"></i>
                                        <p>Drag content blocks here to build your page</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Block Properties Panel -->
                        <div id="blockPropertiesPanel" class="hidden">
                            <h3 class="text-lg font-medium mb-4 text-gray-700">
                                <i class="ri-settings-line mr-2"></i>Block Properties
                            </h3>
                            <div id="blockPropertiesContent">
                                <!-- Properties will be dynamically loaded based on selected block -->
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Content Editor -->
                    <div id="advancedContentView" class="content-view hidden">
                        <!-- Rich Text Editor -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium mb-4 text-gray-700">
                                <i class="ri-code-line mr-2"></i>Advanced Editor
                            </h3>

                            <!-- Editor Toolbar -->
                            <div class="mb-4 p-3 bg-gray-50 rounded-lg border">
                                <div class="flex items-center space-x-2 flex-wrap">
                                    <!-- Text Formatting -->
                                    <div class="flex items-center space-x-1 border-r border-gray-300 pr-2">
                                        <button class="editor-btn" data-command="bold" title="Bold">
                                            <i class="ri-bold"></i>
                                        </button>
                                        <button class="editor-btn" data-command="italic" title="Italic">
                                            <i class="ri-italic"></i>
                                        </button>
                                        <button class="editor-btn" data-command="underline" title="Underline">
                                            <i class="ri-underline"></i>
                                        </button>
                                    </div>

                                    <!-- Alignment -->
                                    <div class="flex items-center space-x-1 border-r border-gray-300 pr-2">
                                        <button class="editor-btn" data-command="justifyLeft" title="Align Left">
                                            <i class="ri-align-left"></i>
                                        </button>
                                        <button class="editor-btn" data-command="justifyCenter" title="Align Center">
                                            <i class="ri-align-center"></i>
                                        </button>
                                        <button class="editor-btn" data-command="justifyRight" title="Align Right">
                                            <i class="ri-align-right"></i>
                                        </button>
                                    </div>

                                    <!-- Lists -->
                                    <div class="flex items-center space-x-1 border-r border-gray-300 pr-2">
                                        <button class="editor-btn" data-command="insertUnorderedList" title="Bullet List">
                                            <i class="ri-list-unordered"></i>
                                        </button>
                                        <button class="editor-btn" data-command="insertOrderedList" title="Numbered List">
                                            <i class="ri-list-ordered"></i>
                                        </button>
                                    </div>

                                    <!-- Links & Media -->
                                    <div class="flex items-center space-x-1">
                                        <button class="editor-btn" id="insertLink" title="Insert Link">
                                            <i class="ri-link"></i>
                                        </button>
                                        <button class="editor-btn" id="insertImage" title="Insert Image">
                                            <i class="ri-image-line"></i>
                                        </button>
                                        <button class="editor-btn" id="insertIcon" title="Insert Icon">
                                            <i class="ri-emotion-line"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Rich Text Area -->
                            <div id="richTextEditor" contenteditable="true"
                                 class="min-h-64 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                 placeholder="Start typing your content here...">
                            </div>
                        </div>

                        <!-- Custom CSS Editor -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium mb-3 text-gray-600">Custom Styling</h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Background Color</label>
                                    <input type="color" id="contentBgColor" class="w-full h-10 border border-gray-300 rounded">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Text Color</label>
                                    <input type="color" id="contentTextColor" class="w-full h-10 border border-gray-300 rounded">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Font Size</label>
                                    <select id="contentFontSize" class="w-full p-2 border border-gray-300 rounded">
                                        <option value="12px">12px</option>
                                        <option value="14px" selected>14px</option>
                                        <option value="16px">16px</option>
                                        <option value="18px">18px</option>
                                        <option value="20px">20px</option>
                                        <option value="24px">24px</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Font Weight</label>
                                    <select id="contentFontWeight" class="w-full p-2 border border-gray-300 rounded">
                                        <option value="normal">Normal</option>
                                        <option value="bold">Bold</option>
                                        <option value="lighter">Light</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Preview Panel -->
        <div class="bg-gray-50 preview-panel">
            <div class="p-6">
                <!-- Preview Header -->
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold">Live Preview</h2>
                    <div class="flex items-center space-x-2">
                        <!-- Device Selection -->
                        <div class="flex bg-white rounded-lg p-1 shadow-sm">
                            <button id="mobileView" class="device-btn active px-3 py-1 rounded text-sm font-medium bg-blue-500 text-white">
                                <i class="ri-smartphone-line mr-1"></i>Mobile
                            </button>
                            <button id="tabletView" class="device-btn px-3 py-1 rounded text-sm font-medium text-gray-600 hover:bg-gray-100">
                                <i class="ri-tablet-line mr-1"></i>Tablet
                            </button>
                            <button id="desktopView" class="device-btn px-3 py-1 rounded text-sm font-medium text-gray-600 hover:bg-gray-100">
                                <i class="ri-computer-line mr-1"></i>Desktop
                            </button>
                        </div>

                        <!-- Preview Actions -->
                        <button id="fullscreenPreview" class="p-2 bg-white rounded-lg shadow-sm hover:bg-gray-50 transition-colors" title="Fullscreen Preview">
                            <i class="ri-fullscreen-line text-gray-600"></i>
                        </button>
                        <button id="sharePreview" class="p-2 bg-white rounded-lg shadow-sm hover:bg-gray-50 transition-colors" title="Share Preview">
                            <i class="ri-share-line text-gray-600"></i>
                        </button>
                        <button id="refreshPreview" class="p-2 bg-white rounded-lg shadow-sm hover:bg-gray-50 transition-colors" title="Refresh Preview">
                            <i class="ri-refresh-line text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- Preview Settings -->
                <div class="mb-4 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center text-sm">
                            <input type="checkbox" id="autoRefresh" checked class="mr-2">
                            <span>Auto Refresh</span>
                        </label>
                        <label class="flex items-center text-sm">
                            <input type="checkbox" id="showGrid" class="mr-2">
                            <span>Show Grid</span>
                        </label>
                    </div>
                    <div class="text-sm text-gray-500" id="previewStatus">
                        Ready
                    </div>
                </div>

                <!-- Device Frame Container -->
                <div id="deviceFrame" class="device-frame mobile-frame">
                    <div class="device-screen">
                        <iframe id="previewFrame" src="about:blank" class="w-full h-full border-0"></iframe>
                    </div>
                </div>

                <!-- Preview URL -->
                <div class="mt-4 p-3 bg-white rounded-lg shadow-sm">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <label class="block text-xs text-gray-500 mb-1">Preview URL</label>
                            <input type="text" id="previewUrl" readonly
                                   class="w-full text-sm bg-gray-50 border border-gray-200 rounded px-2 py-1 font-mono"
                                   placeholder="Generate preview to get shareable URL">
                        </div>
                        <button id="copyPreviewUrl" class="ml-2 p-2 text-gray-500 hover:text-blue-600 transition-colors" title="Copy URL">
                            <i class="ri-file-copy-line"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fullscreen Preview Modal -->
    <div id="fullscreenModal" class="fixed inset-0 bg-black bg-opacity-95 hidden items-center justify-center z-50">
        <div class="w-full h-full flex flex-col">
            <!-- Fullscreen Header -->
            <div class="flex items-center justify-between p-4 bg-black bg-opacity-50">
                <div class="flex items-center space-x-4">
                    <h3 class="text-white text-lg font-semibold">Fullscreen Preview</h3>
                    <div class="flex bg-gray-800 rounded-lg p-1">
                        <button id="fullscreenMobile" class="fullscreen-device-btn active px-3 py-1 rounded text-sm font-medium bg-blue-500 text-white">
                            <i class="ri-smartphone-line mr-1"></i>Mobile
                        </button>
                        <button id="fullscreenTablet" class="fullscreen-device-btn px-3 py-1 rounded text-sm font-medium text-gray-300 hover:bg-gray-700">
                            <i class="ri-tablet-line mr-1"></i>Tablet
                        </button>
                        <button id="fullscreenDesktop" class="fullscreen-device-btn px-3 py-1 rounded text-sm font-medium text-gray-300 hover:bg-gray-700">
                            <i class="ri-computer-line mr-1"></i>Desktop
                        </button>
                    </div>
                </div>
                <button id="closeFullscreen" class="text-white hover:text-gray-300 p-2">
                    <i class="ri-close-line text-xl"></i>
                </button>
            </div>

            <!-- Fullscreen Content -->
            <div class="flex-1 flex items-center justify-center p-4">
                <div id="fullscreenDeviceFrame" class="device-frame mobile-frame">
                    <div class="device-screen">
                        <iframe id="fullscreenPreviewFrame" src="about:blank" class="w-full h-full border-0"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Edit Modal -->
    <div id="bulkEditModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-8 rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 class="text-xl font-semibold mb-6 flex items-center">
                <i class="ri-edit-box-line mr-2"></i>
                Bulk Edit Businesses
            </h3>

            <!-- Bulk Edit Controls -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="font-medium mb-3">Apply to All Businesses</h4>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Template</label>
                        <select id="bulkTemplate" class="w-full p-2 border border-gray-300 rounded">
                            <option value="">Keep individual settings</option>
                            <option value="modern">Modern</option>
                            <option value="luxury">Luxury</option>
                            <option value="minimal">Minimal</option>
                            <option value="cute">Cute</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Primary Color</label>
                        <input type="color" id="bulkPrimaryColor" class="w-full h-10 border border-gray-300 rounded">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Logo URL</label>
                        <input type="url" id="bulkLogoUrl" placeholder="Apply same logo to all"
                               class="w-full p-2 border border-gray-300 rounded">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Website Domain</label>
                        <input type="text" id="bulkWebsiteDomain" placeholder="e.g., mycompany.com"
                               class="w-full p-2 border border-gray-300 rounded">
                    </div>
                </div>
                <button id="applyBulkChanges" class="mt-3 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Apply Changes to All
                </button>
            </div>

            <!-- Business List -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium">Business List</h4>
                    <div class="flex space-x-2">
                        <button id="addBulkBusiness" class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">
                            <i class="ri-add-line mr-1"></i>Add Business
                        </button>
                        <button id="loadFromProfiles" class="bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600">
                            <i class="ri-database-line mr-1"></i>Load from Profiles
                        </button>
                    </div>
                </div>
                <div id="bulkBusinessList" class="space-y-3 max-h-96 overflow-y-auto">
                    <!-- Business items will be dynamically added -->
                </div>
            </div>

            <div class="flex space-x-3">
                <button id="exportBulkBusinesses" class="flex-1 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="ri-download-line mr-2"></i>Export All
                </button>
                <button id="closeBulkEdit" class="flex-1 bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="ri-close-line mr-2"></i>Close
                </button>
            </div>
        </div>
    </div>

    <!-- Share Preview Modal -->
    <div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-8 rounded-lg max-w-md w-full mx-4">
            <h3 class="text-xl font-semibold mb-4 flex items-center">
                <i class="ri-share-line mr-2"></i>
                Share Preview
            </h3>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Preview URL</label>
                <div class="flex items-center space-x-2">
                    <input type="text" id="shareUrl" readonly
                           class="flex-1 p-3 border border-gray-300 rounded-lg bg-gray-50 font-mono text-sm">
                    <button id="copyShareUrl" class="p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="ri-file-copy-line"></i>
                    </button>
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">QR Code</label>
                <div id="shareQrCode" class="text-center p-4 bg-gray-50 rounded-lg">
                    <!-- QR code will be generated here -->
                </div>
            </div>

            <div class="flex space-x-3">
                <button id="generateShareUrl" class="flex-1 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="ri-link mr-2"></i>Generate URL
                </button>
                <button id="closeShareModal" class="flex-1 bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="ri-close-line mr-2"></i>Close
                </button>
            </div>
        </div>
    </div>

    <!-- Content Templates Modal -->
    <div id="contentTemplatesModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-8 rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 class="text-xl font-semibold mb-6 flex items-center">
                <i class="ri-file-copy-line mr-2"></i>
                Content Templates
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <!-- Business Card Template -->
                <div class="template-card p-4 border border-gray-200 rounded-lg hover:border-blue-400 cursor-pointer transition-colors" data-template="business-card">
                    <div class="mb-3">
                        <div class="w-full h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                            <i class="ri-briefcase-line text-3xl text-blue-600"></i>
                        </div>
                    </div>
                    <h4 class="font-medium mb-2">Business Card</h4>
                    <p class="text-sm text-gray-600">Simple contact information layout</p>
                </div>

                <!-- Service Menu Template -->
                <div class="template-card p-4 border border-gray-200 rounded-lg hover:border-blue-400 cursor-pointer transition-colors" data-template="service-menu">
                    <div class="mb-3">
                        <div class="w-full h-32 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center">
                            <i class="ri-service-line text-3xl text-green-600"></i>
                        </div>
                    </div>
                    <h4 class="font-medium mb-2">Service Menu</h4>
                    <p class="text-sm text-gray-600">Services with pricing and descriptions</p>
                </div>

                <!-- Event Promotion Template -->
                <div class="template-card p-4 border border-gray-200 rounded-lg hover:border-blue-400 cursor-pointer transition-colors" data-template="event-promo">
                    <div class="mb-3">
                        <div class="w-full h-32 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center">
                            <i class="ri-calendar-event-line text-3xl text-purple-600"></i>
                        </div>
                    </div>
                    <h4 class="font-medium mb-2">Event Promotion</h4>
                    <p class="text-sm text-gray-600">Event details with call-to-action</p>
                </div>

                <!-- Restaurant Menu Template -->
                <div class="template-card p-4 border border-gray-200 rounded-lg hover:border-blue-400 cursor-pointer transition-colors" data-template="restaurant-menu">
                    <div class="mb-3">
                        <div class="w-full h-32 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center">
                            <i class="ri-restaurant-line text-3xl text-orange-600"></i>
                        </div>
                    </div>
                    <h4 class="font-medium mb-2">Restaurant Menu</h4>
                    <p class="text-sm text-gray-600">Food menu with categories and prices</p>
                </div>

                <!-- Portfolio Template -->
                <div class="template-card p-4 border border-gray-200 rounded-lg hover:border-blue-400 cursor-pointer transition-colors" data-template="portfolio">
                    <div class="mb-3">
                        <div class="w-full h-32 bg-gradient-to-br from-pink-100 to-pink-200 rounded-lg flex items-center justify-center">
                            <i class="ri-gallery-line text-3xl text-pink-600"></i>
                        </div>
                    </div>
                    <h4 class="font-medium mb-2">Portfolio</h4>
                    <p class="text-sm text-gray-600">Showcase work with image gallery</p>
                </div>

                <!-- Contact Page Template -->
                <div class="template-card p-4 border border-gray-200 rounded-lg hover:border-blue-400 cursor-pointer transition-colors" data-template="contact-page">
                    <div class="mb-3">
                        <div class="w-full h-32 bg-gradient-to-br from-teal-100 to-teal-200 rounded-lg flex items-center justify-center">
                            <i class="ri-phone-line text-3xl text-teal-600"></i>
                        </div>
                    </div>
                    <h4 class="font-medium mb-2">Contact Page</h4>
                    <p class="text-sm text-gray-600">Complete contact information and map</p>
                </div>
            </div>

            <div class="flex space-x-3">
                <button id="applyTemplate" class="flex-1 bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="ri-check-line mr-2"></i>Apply Template
                </button>
                <button id="closeContentTemplates" class="flex-1 bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="ri-close-line mr-2"></i>Close
                </button>
            </div>
        </div>
    </div>

    <!-- Icon Selector Modal -->
    <div id="iconSelectorModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-8 rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 class="text-xl font-semibold mb-6 flex items-center">
                <i class="ri-emotion-line mr-2"></i>
                Select Icon
            </h3>

            <!-- Icon Search -->
            <div class="mb-4">
                <input type="text" id="iconSearch" placeholder="Search icons..."
                       class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
            </div>

            <!-- Icon Categories -->
            <div class="mb-4">
                <div class="flex flex-wrap gap-2">
                    <button class="icon-category-btn active px-3 py-1 rounded-full text-sm bg-blue-500 text-white" data-category="all">All</button>
                    <button class="icon-category-btn px-3 py-1 rounded-full text-sm bg-gray-200 text-gray-700 hover:bg-gray-300" data-category="business">Business</button>
                    <button class="icon-category-btn px-3 py-1 rounded-full text-sm bg-gray-200 text-gray-700 hover:bg-gray-300" data-category="communication">Communication</button>
                    <button class="icon-category-btn px-3 py-1 rounded-full text-sm bg-gray-200 text-gray-700 hover:bg-gray-300" data-category="social">Social</button>
                    <button class="icon-category-btn px-3 py-1 rounded-full text-sm bg-gray-200 text-gray-700 hover:bg-gray-300" data-category="arrows">Arrows</button>
                    <button class="icon-category-btn px-3 py-1 rounded-full text-sm bg-gray-200 text-gray-700 hover:bg-gray-300" data-category="media">Media</button>
                </div>
            </div>

            <!-- Icon Grid -->
            <div id="iconGrid" class="grid grid-cols-8 gap-2 mb-6 max-h-96 overflow-y-auto">
                <!-- Icons will be dynamically populated -->
            </div>

            <div class="flex space-x-3">
                <button id="insertSelectedIcon" class="flex-1 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="ri-check-line mr-2"></i>Insert Icon
                </button>
                <button id="closeIconSelector" class="flex-1 bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="ri-close-line mr-2"></i>Close
                </button>
            </div>
        </div>
    </div>

    <!-- Enhanced QR Code Modal -->
    <div id="qrModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white p-8 rounded-lg max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 class="text-xl font-semibold mb-6 flex items-center">
                <i class="ri-qr-code-line mr-2"></i>
                QR Code Generator
            </h3>

            <!-- URL Input -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Website URL</label>
                <input type="url" id="qrUrl" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                       placeholder="https://your-website.com">
            </div>

            <!-- QR Customization -->
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-4 text-gray-700">Customization</h4>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Size</label>
                        <select id="qrSize" class="w-full p-2 border border-gray-300 rounded-lg">
                            <option value="128">Small (128px)</option>
                            <option value="256" selected>Medium (256px)</option>
                            <option value="512">Large (512px)</option>
                            <option value="1024">Extra Large (1024px)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Error Correction</label>
                        <select id="qrErrorLevel" class="w-full p-2 border border-gray-300 rounded-lg">
                            <option value="L">Low (7%)</option>
                            <option value="M" selected>Medium (15%)</option>
                            <option value="Q">Quartile (25%)</option>
                            <option value="H">High (30%)</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Foreground Color</label>
                        <div class="flex items-center space-x-2">
                            <input type="color" id="qrForeground" value="#000000" class="w-12 h-10 border border-gray-300 rounded">
                            <span id="qrForegroundText" class="text-sm font-mono">#000000</span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Background Color</label>
                        <div class="flex items-center space-x-2">
                            <input type="color" id="qrBackground" value="#ffffff" class="w-12 h-10 border border-gray-300 rounded">
                            <span id="qrBackgroundText" class="text-sm font-mono">#ffffff</span>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="qrLogo" class="mr-2">
                        <span class="text-sm font-medium text-gray-700">Include Business Logo</span>
                    </label>
                    <p class="text-xs text-gray-500 mt-1">Logo will be placed in the center of QR code</p>
                </div>
            </div>

            <!-- QR Preview -->
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">Preview</h4>
                <div id="qrcode" class="text-center p-4 bg-gray-50 rounded-lg min-h-[200px] flex items-center justify-center">
                    <p class="text-gray-500">Click "Generate QR" to see preview</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-3">
                <button id="generateQr" class="flex-1 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="ri-refresh-line mr-2"></i>Generate QR
                </button>
                <button id="downloadQr" class="flex-1 bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors hidden">
                    <i class="ri-download-line mr-2"></i>Download PNG
                </button>
                <button id="downloadQrSvg" class="flex-1 bg-purple-600 text-white py-3 rounded-lg hover:bg-purple-700 transition-colors hidden">
                    <i class="ri-file-code-line mr-2"></i>Download SVG
                </button>
                <button id="closeQrModal" class="flex-1 bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="ri-close-line mr-2"></i>Close
                </button>
            </div>
        </div>
    </div>

    <script src="template-editor.js"></script>
</body>
</html>
