/**
 * Template Validator - Validates HTML templates for compatibility
 */

export interface TemplateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  requiredVariables: string[];
  optionalVariables: string[];
  features: string[];
}

export interface TemplateMetadata {
  id: string;
  name: string;
  description: string;
  category: string;
  version: string;
  author?: string;
  preview?: string;
  features: string[];
  customizable: {
    colors: boolean;
    fonts: boolean;
    layout: boolean;
    header: boolean;
  };
  requiredVariables: string[];
  optionalVariables: string[];
}

export class TemplateValidator {
  private requiredVariables = [
    'BUSINESS_NAME',
    'PHONE',
    'EMAIL',
    'ADDRESS'
  ];

  private optionalVariables = [
    'SLOGAN',
    'DESCRIPTION',
    'LOGO_URL',
    'FAVICON_URL',
    'HOURS',
    'FACEBOOK_URL',
    'INSTAGRAM_URL',
    'YELP_URL',
    'PRIMARY_COLOR',
    'SECONDARY_COLOR',
    'HEADER_BACKGROUND_TYPE',
    'HEADER_BACKGROUND_VALUE',
    'HEADER_TEXT_COLOR'
  ];

  private conditionalSections = [
    'HAS_LOGO',
    'HAS_SERVICES',
    'HAS_OFFERS',
    'HAS_GALLERY',
    'HAS_SOCIAL_LINKS'
  ];

  private arrayVariables = [
    'SERVICES',
    'OFFERS',
    'GALLERY_IMAGES',
    'CUSTOM_SOCIAL_LINKS'
  ];

  /**
   * Validate HTML template for compatibility
   */
  validateTemplate(html: string, metadata?: TemplateMetadata): TemplateValidationResult {
    const result: TemplateValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      requiredVariables: [],
      optionalVariables: [],
      features: []
    };

    try {
      // 1. Check basic HTML structure
      this.validateHTMLStructure(html, result);

      // 2. Extract and validate template variables
      this.validateTemplateVariables(html, result);

      // 3. Check conditional sections
      this.validateConditionalSections(html, result);

      // 4. Validate array loops
      this.validateArrayLoops(html, result);

      // 5. Check for responsive design
      this.validateResponsiveDesign(html, result);

      // 6. Validate metadata if provided
      if (metadata) {
        this.validateMetadata(metadata, result);
      }

      // 7. Detect features
      this.detectFeatures(html, result);

      // Set overall validity
      result.isValid = result.errors.length === 0;

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Validation error: ${error.message}`);
    }

    return result;
  }

  private validateHTMLStructure(html: string, result: TemplateValidationResult): void {
    // Check for basic HTML structure
    if (!html.includes('<html') && !html.includes('<HTML')) {
      result.warnings.push('Template should include <html> tag for complete document');
    }

    if (!html.includes('<head') && !html.includes('<HEAD')) {
      result.warnings.push('Template should include <head> section');
    }

    if (!html.includes('<body') && !html.includes('<BODY')) {
      result.errors.push('Template must include <body> tag');
    }

    // Check for meta viewport (responsive)
    if (!html.includes('viewport')) {
      result.warnings.push('Template should include viewport meta tag for mobile responsiveness');
    }
  }

  private validateTemplateVariables(html: string, result: TemplateValidationResult): void {
    // Extract all template variables {{VARIABLE}}
    const variableRegex = /\{\{([A-Z_]+)\}\}/g;
    const foundVariables = new Set<string>();
    let match;

    while ((match = variableRegex.exec(html)) !== null) {
      foundVariables.add(match[1]);
    }

    // Check required variables
    for (const required of this.requiredVariables) {
      if (foundVariables.has(required)) {
        result.requiredVariables.push(required);
      } else {
        result.warnings.push(`Recommended variable missing: {{${required}}}`);
      }
    }

    // Check optional variables
    for (const optional of this.optionalVariables) {
      if (foundVariables.has(optional)) {
        result.optionalVariables.push(optional);
      }
    }

    // Check for unknown variables
    for (const variable of foundVariables) {
      if (!this.requiredVariables.includes(variable) && 
          !this.optionalVariables.includes(variable) &&
          !this.conditionalSections.includes(variable) &&
          !this.arrayVariables.includes(variable)) {
        result.warnings.push(`Unknown variable found: {{${variable}}}`);
      }
    }
  }

  private validateConditionalSections(html: string, result: TemplateValidationResult): void {
    // Check for conditional sections {{#CONDITION}}...{{/CONDITION}}
    const conditionalRegex = /\{\{#([A-Z_]+)\}\}([\s\S]*?)\{\{\/\1\}\}/g;
    let match;

    while ((match = conditionalRegex.exec(html)) !== null) {
      const condition = match[1];
      const content = match[2];

      if (this.conditionalSections.includes(condition)) {
        result.features.push(`conditional-${condition.toLowerCase()}`);
        
        // Validate content inside conditional
        if (content.trim().length === 0) {
          result.warnings.push(`Empty conditional section: {{#${condition}}}`);
        }
      } else {
        result.warnings.push(`Unknown conditional section: {{#${condition}}}`);
      }
    }
  }

  private validateArrayLoops(html: string, result: TemplateValidationResult): void {
    // Check for array loops
    for (const arrayVar of this.arrayVariables) {
      const loopRegex = new RegExp(`\\{\\{#${arrayVar}\\}\\}([\\s\\S]*?)\\{\\{\\/${arrayVar}\\}\\}`, 'g');
      const matches = html.match(loopRegex);
      
      if (matches) {
        result.features.push(`array-${arrayVar.toLowerCase()}`);
        
        // Check if loop content has proper structure
        for (const match of matches) {
          if (!match.includes('{{') || match.split('{{').length < 3) {
            result.warnings.push(`Array loop for ${arrayVar} should contain template variables`);
          }
        }
      }
    }
  }

  private validateResponsiveDesign(html: string, result: TemplateValidationResult): void {
    // Check for responsive design indicators
    const responsiveIndicators = [
      'viewport',
      'media query',
      '@media',
      'responsive',
      'mobile',
      'tablet',
      'desktop'
    ];

    let hasResponsive = false;
    for (const indicator of responsiveIndicators) {
      if (html.toLowerCase().includes(indicator)) {
        hasResponsive = true;
        break;
      }
    }

    if (hasResponsive) {
      result.features.push('responsive');
    } else {
      result.warnings.push('Template may not be responsive - consider adding mobile support');
    }
  }

  private validateMetadata(metadata: TemplateMetadata, result: TemplateValidationResult): void {
    // Validate required metadata fields
    if (!metadata.id || metadata.id.trim().length === 0) {
      result.errors.push('Template metadata must include valid ID');
    }

    if (!metadata.name || metadata.name.trim().length === 0) {
      result.errors.push('Template metadata must include name');
    }

    if (!metadata.category || metadata.category.trim().length === 0) {
      result.warnings.push('Template metadata should include category');
    }

    // Validate version format
    if (metadata.version && !/^\d+\.\d+\.\d+$/.test(metadata.version)) {
      result.warnings.push('Template version should follow semantic versioning (e.g., 1.0.0)');
    }
  }

  private detectFeatures(html: string, result: TemplateValidationResult): void {
    const htmlLower = html.toLowerCase();

    // Detect common features
    if (htmlLower.includes('gallery') || htmlLower.includes('image')) {
      result.features.push('gallery');
    }

    if (htmlLower.includes('service') || htmlLower.includes('product')) {
      result.features.push('services');
    }

    if (htmlLower.includes('offer') || htmlLower.includes('special') || htmlLower.includes('deal')) {
      result.features.push('offers');
    }

    if (htmlLower.includes('social') || htmlLower.includes('facebook') || htmlLower.includes('instagram')) {
      result.features.push('social');
    }

    if (htmlLower.includes('contact') || htmlLower.includes('phone') || htmlLower.includes('email')) {
      result.features.push('contact');
    }

    // Remove duplicates
    result.features = [...new Set(result.features)];
  }

  /**
   * Generate template metadata from HTML
   */
  generateMetadata(html: string, templateId: string, templateName: string): TemplateMetadata {
    const validation = this.validateTemplate(html);
    
    return {
      id: templateId,
      name: templateName,
      description: `Auto-generated template: ${templateName}`,
      category: 'custom',
      version: '1.0.0',
      features: validation.features,
      customizable: {
        colors: html.includes('PRIMARY_COLOR') || html.includes('SECONDARY_COLOR'),
        fonts: html.includes('font') || html.includes('Font'),
        layout: false, // Default to false for safety
        header: html.includes('HEADER_') || html.includes('header')
      },
      requiredVariables: validation.requiredVariables,
      optionalVariables: validation.optionalVariables
    };
  }
}

export const templateValidator = new TemplateValidator();
