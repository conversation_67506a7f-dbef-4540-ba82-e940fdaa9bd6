import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  Tabs,
  Tab,
  IconButton,
  Chip,
  Grid,
  Paper
} from '@mui/material';
import { X, Code, Eye, Download, Star } from 'lucide-react';
import type { Template } from '../types/template';

interface TemplatePreviewProps {
  template: Template | null;
  open: boolean;
  onClose: () => void;
  onUseTemplate?: (template: Template) => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`preview-tabpanel-${index}`}
      aria-labelledby={`preview-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2 }}>{children}</Box>}
    </div>
  );
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  template,
  open,
  onClose,
  onUseTemplate
}) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [previewHtml, setPreviewHtml] = useState('');

  useEffect(() => {
    if (template) {
      // Generate preview with sample data
      const sampleData = {
        BUSINESS_NAME: 'Sample Business',
        SLOGAN: 'Your Success is Our Mission',
        DESCRIPTION: 'We provide excellent services to help your business grow and succeed in today\'s competitive market.',
        PHONE: '(*************',
        EMAIL: '<EMAIL>',
        ADDRESS: '123 Main Street, City, State 12345',
        HOURS: 'Mon-Fri: 9AM-6PM, Sat: 10AM-4PM',
        LOGO_URL: 'https://via.placeholder.com/150x100/1976d2/ffffff?text=LOGO',
        FAVICON_URL: 'https://via.placeholder.com/32x32/1976d2/ffffff?text=F',
        PRIMARY_COLOR: '#1976d2',
        SECONDARY_COLOR: '#dc004e',
        FACEBOOK_URL: 'https://facebook.com/samplebusiness',
        INSTAGRAM_URL: 'https://instagram.com/samplebusiness',
        YELP_URL: 'https://yelp.com/biz/samplebusiness'
      };

      let html = template.htmlContent;

      // Replace simple variables
      Object.entries(sampleData).forEach(([key, value]) => {
        const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        html = html.replace(regex, value);
      });

      // Handle conditional sections
      html = html.replace(/\{\{#HAS_LOGO\}\}([\s\S]*?)\{\{\/HAS_LOGO\}\}/g, '$1');
      html = html.replace(/\{\{#HAS_SERVICES\}\}([\s\S]*?)\{\{\/HAS_SERVICES\}\}/g, 
        '<div class="services"><div class="service"><h3>Sample Service 1</h3><p>Description of service 1</p><p><strong>Price: $50</strong></p></div><div class="service"><h3>Sample Service 2</h3><p>Description of service 2</p><p><strong>Price: $75</strong></p></div></div>');
      html = html.replace(/\{\{#HAS_SOCIAL_LINKS\}\}([\s\S]*?)\{\{\/HAS_SOCIAL_LINKS\}\}/g, '$1');
      
      // Remove any remaining template syntax
      html = html.replace(/\{\{#[^}]+\}\}/g, '');
      html = html.replace(/\{\{\/[^}]+\}\}/g, '');
      html = html.replace(/\{\{[^}]+\}\}/g, '');

      setPreviewHtml(html);
    }
  }, [template]);

  const handleDownload = () => {
    if (!template) return;
    
    const blob = new Blob([template.htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${template.name.toLowerCase().replace(/\s+/g, '-')}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleUseTemplate = () => {
    if (template && onUseTemplate) {
      onUseTemplate(template);
    }
  };

  if (!template) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '90vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant="h6">{template.name}</Typography>
          <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
            <Chip label={template.category} size="small" color="primary" />
            <Chip label={template.status} size="small" color="success" />
            {template.rating && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Star size={16} fill="currentColor" />
                <Typography variant="body2">{template.rating}</Typography>
              </Box>
            )}
          </Box>
        </Box>
        <IconButton onClick={onClose}>
          <X size={20} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
            <Tab icon={<Eye size={16} />} label="Preview" />
            <Tab icon={<Code size={16} />} label="HTML Code" />
            <Tab label="Details" />
          </Tabs>
        </Box>

        <TabPanel value={currentTab} index={0}>
          <Box
            component="iframe"
            srcDoc={previewHtml}
            sx={{
              width: '100%',
              height: '60vh',
              border: '1px solid #e0e0e0',
              borderRadius: 1
            }}
          />
        </TabPanel>

        <TabPanel value={currentTab} index={1}>
          <Paper
            sx={{
              p: 2,
              height: '60vh',
              overflow: 'auto',
              backgroundColor: '#f5f5f5',
              fontFamily: 'monospace'
            }}
          >
            <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
              {template.htmlContent}
            </pre>
          </Paper>
        </TabPanel>

        <TabPanel value={currentTab} index={2}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>Template Information</Typography>
              <Typography variant="body2" paragraph>
                <strong>Description:</strong> {template.description}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Version:</strong> {template.version}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Author:</strong> {template.author || 'Unknown'}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Created:</strong> {new Date(template.createdAt).toLocaleDateString()}
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>Downloads:</strong> {template.downloadCount}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>Features</Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                {template.metadata.features.map((feature) => (
                  <Chip key={feature} label={feature} size="small" variant="outlined" />
                ))}
              </Box>

              <Typography variant="h6" gutterBottom>Customizable</Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                {Object.entries(template.metadata.customizable)
                  .filter(([_, value]) => value)
                  .map(([key]) => (
                    <Chip key={key} label={key} size="small" color="secondary" />
                  ))}
              </Box>

              <Typography variant="h6" gutterBottom>Required Variables</Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                {template.metadata.requiredVariables.map((variable) => (
                  <Chip key={variable} label={`{{${variable}}}`} size="small" />
                ))}
              </Box>

              {template.metadata.tags.length > 0 && (
                <>
                  <Typography variant="h6" gutterBottom>Tags</Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {template.metadata.tags.map((tag) => (
                      <Chip key={tag} label={tag} size="small" variant="outlined" />
                    ))}
                  </Box>
                </>
              )}
            </Grid>
          </Grid>
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
        <Button
          startIcon={<Download size={16} />}
          onClick={handleDownload}
        >
          Download
        </Button>
        {onUseTemplate && (
          <Button
            variant="contained"
            onClick={handleUseTemplate}
          >
            Use This Template
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default TemplatePreview;
