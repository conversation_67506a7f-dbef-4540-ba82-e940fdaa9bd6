import React, { useState } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Tabs,
  Tab,
  TextField,
  Button,
  IconButton,
  Card,
  CardContent,
  CardActions,
  Divider,
  Switch,
  FormControlLabel,
  Chip,
  Alert
} from '@mui/material';
import { Plus, Trash2, Upload, Image as ImageIcon } from 'lucide-react';
import type { Template } from '../../types/template';

interface EditContentTabProps {
  businessData: any;
  onUpdateBusinessData: (field: string, value: any) => void;
  template: Template;
  onUpdateTemplate: (field: string, value: any) => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`content-tabpanel-${index}`}
      aria-labelledby={`content-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 2 }}>{children}</Box>}
    </div>
  );
}

const EditContentTab: React.FC<EditContentTabProps> = ({
  businessData,
  onUpdateBusinessData,
  template,
  onUpdateTemplate
}) => {
  const [currentTab, setCurrentTab] = useState(0);

  const handleImageUpload = (field: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        onUpdateBusinessData(field, result);
      };
      reader.readAsDataURL(file);
    }
  };

  const addService = () => {
    const newService = {
      id: Date.now().toString(),
      name: 'New Service',
      description: 'Service description',
      price: '$50',
      duration: '30 min',
      category: 'general',
      isActive: true,
      order: businessData.services.length + 1
    };
    onUpdateBusinessData('services', [...businessData.services, newService]);
  };

  const updateService = (index: number, field: string, value: any) => {
    const updatedServices = [...businessData.services];
    updatedServices[index] = { ...updatedServices[index], [field]: value };
    onUpdateBusinessData('services', updatedServices);
  };

  const removeService = (index: number) => {
    const updatedServices = businessData.services.filter((_: any, i: number) => i !== index);
    onUpdateBusinessData('services', updatedServices);
  };

  const addOffer = () => {
    const newOffer = {
      id: Date.now().toString(),
      title: 'Special Offer',
      description: 'Limited time offer',
      originalPrice: '$100',
      discountPrice: '$80',
      validUntil: new Date().toISOString().split('T')[0],
      isActive: true,
      order: businessData.offers.length + 1
    };
    onUpdateBusinessData('offers', [...businessData.offers, newOffer]);
  };

  const updateOffer = (index: number, field: string, value: any) => {
    const updatedOffers = [...businessData.offers];
    updatedOffers[index] = { ...updatedOffers[index], [field]: value };
    onUpdateBusinessData('offers', updatedOffers);
  };

  const removeOffer = (index: number) => {
    const updatedOffers = businessData.offers.filter((_: any, i: number) => i !== index);
    onUpdateBusinessData('offers', updatedOffers);
  };

  const addGalleryImage = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.multiple = true;
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (files) {
        Array.from(files).forEach((file) => {
          const reader = new FileReader();
          reader.onload = (event) => {
            const newImage = {
              id: Date.now().toString() + Math.random(),
              url: event.target?.result as string,
              alt: file.name,
              caption: '',
              order: businessData.galleryImages.length + 1
            };
            onUpdateBusinessData('galleryImages', [...businessData.galleryImages, newImage]);
          };
          reader.readAsDataURL(file);
        });
      }
    };
    input.click();
  };

  const removeGalleryImage = (index: number) => {
    const updatedImages = businessData.galleryImages.filter((_: any, i: number) => i !== index);
    onUpdateBusinessData('galleryImages', updatedImages);
  };

  const contentTabs = [
    'Basic Info',
    'Services',
    'Offers',
    'Media',
    'Social Media'
  ];

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Edit Content
      </Typography>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={(_, newValue) => setCurrentTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
        >
          {contentTabs.map((tab, index) => (
            <Tab key={index} label={tab} />
          ))}
        </Tabs>
      </Paper>

      {/* Basic Info Tab */}
      <TabPanel value={currentTab} index={0}>
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Business Name"
              value={businessData.businessName}
              onChange={(e) => onUpdateBusinessData('businessName', e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Slogan"
              value={businessData.slogan || ''}
              onChange={(e) => onUpdateBusinessData('slogan', e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid size={{ xs: 12 }}>
            <TextField
              fullWidth
              label="Description"
              value={businessData.description || ''}
              onChange={(e) => onUpdateBusinessData('description', e.target.value)}
              margin="normal"
              multiline
              rows={3}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Phone"
              value={businessData.phone}
              onChange={(e) => onUpdateBusinessData('phone', e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Email"
              value={businessData.email}
              onChange={(e) => onUpdateBusinessData('email', e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid size={{ xs: 12 }}>
            <TextField
              fullWidth
              label="Address"
              value={businessData.address}
              onChange={(e) => onUpdateBusinessData('address', e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Hours"
              value={businessData.hours || ''}
              onChange={(e) => onUpdateBusinessData('hours', e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Website"
              value={businessData.website || ''}
              onChange={(e) => onUpdateBusinessData('website', e.target.value)}
              margin="normal"
            />
          </Grid>
        </Grid>
      </TabPanel>

      {/* Services Tab */}
      <TabPanel value={currentTab} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Services</Typography>
          <Button
            variant="contained"
            startIcon={<Plus size={16} />}
            onClick={addService}
          >
            Add Service
          </Button>
        </Box>

        <Grid container spacing={2}>
          {businessData.services.map((service: any, index: number) => (
            <Grid size={{ xs: 12, md: 6 }} key={service.id}>
              <Card>
                <CardContent>
                  <TextField
                    fullWidth
                    label="Service Name"
                    value={service.name}
                    onChange={(e) => updateService(index, 'name', e.target.value)}
                    margin="normal"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="Description"
                    value={service.description}
                    onChange={(e) => updateService(index, 'description', e.target.value)}
                    margin="normal"
                    size="small"
                    multiline
                    rows={2}
                  />
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 6 }}>
                      <TextField
                        fullWidth
                        label="Price"
                        value={service.price}
                        onChange={(e) => updateService(index, 'price', e.target.value)}
                        margin="normal"
                        size="small"
                      />
                    </Grid>
                    <Grid size={{ xs: 6 }}>
                      <TextField
                        fullWidth
                        label="Duration"
                        value={service.duration}
                        onChange={(e) => updateService(index, 'duration', e.target.value)}
                        margin="normal"
                        size="small"
                      />
                    </Grid>
                  </Grid>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={service.isActive}
                        onChange={(e) => updateService(index, 'isActive', e.target.checked)}
                      />
                    }
                    label="Active"
                    sx={{ mt: 1 }}
                  />
                </CardContent>
                <CardActions>
                  <IconButton
                    color="error"
                    onClick={() => removeService(index)}
                  >
                    <Trash2 size={16} />
                  </IconButton>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        {businessData.services.length === 0 && (
          <Alert severity="info">
            No services added yet. Click "Add Service" to get started.
          </Alert>
        )}
      </TabPanel>

      {/* Offers Tab */}
      <TabPanel value={currentTab} index={2}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Special Offers</Typography>
          <Button
            variant="contained"
            startIcon={<Plus size={16} />}
            onClick={addOffer}
          >
            Add Offer
          </Button>
        </Box>

        <Grid container spacing={2}>
          {businessData.offers.map((offer: any, index: number) => (
            <Grid size={{ xs: 12, md: 6 }} key={offer.id}>
              <Card>
                <CardContent>
                  <TextField
                    fullWidth
                    label="Offer Title"
                    value={offer.title}
                    onChange={(e) => updateOffer(index, 'title', e.target.value)}
                    margin="normal"
                    size="small"
                  />
                  <TextField
                    fullWidth
                    label="Description"
                    value={offer.description}
                    onChange={(e) => updateOffer(index, 'description', e.target.value)}
                    margin="normal"
                    size="small"
                    multiline
                    rows={2}
                  />
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 4 }}>
                      <TextField
                        fullWidth
                        label="Original Price"
                        value={offer.originalPrice}
                        onChange={(e) => updateOffer(index, 'originalPrice', e.target.value)}
                        margin="normal"
                        size="small"
                      />
                    </Grid>
                    <Grid size={{ xs: 4 }}>
                      <TextField
                        fullWidth
                        label="Discount Price"
                        value={offer.discountPrice}
                        onChange={(e) => updateOffer(index, 'discountPrice', e.target.value)}
                        margin="normal"
                        size="small"
                      />
                    </Grid>
                    <Grid size={{ xs: 4 }}>
                      <TextField
                        fullWidth
                        label="Valid Until"
                        value={offer.validUntil}
                        onChange={(e) => updateOffer(index, 'validUntil', e.target.value)}
                        margin="normal"
                        size="small"
                        type="date"
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                  </Grid>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={offer.isActive}
                        onChange={(e) => updateOffer(index, 'isActive', e.target.checked)}
                      />
                    }
                    label="Active"
                    sx={{ mt: 1 }}
                  />
                </CardContent>
                <CardActions>
                  <IconButton
                    color="error"
                    onClick={() => removeOffer(index)}
                  >
                    <Trash2 size={16} />
                  </IconButton>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        {businessData.offers.length === 0 && (
          <Alert severity="info">
            No offers added yet. Click "Add Offer" to create special promotions.
          </Alert>
        )}
      </TabPanel>

      {/* Media Tab */}
      <TabPanel value={currentTab} index={3}>
        <Grid container spacing={3}>
          {/* Logo Upload */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>Logo</Typography>
              {businessData.logoUrl ? (
                <Box>
                  <img
                    src={businessData.logoUrl}
                    alt="Logo"
                    style={{ maxWidth: '200px', maxHeight: '100px', marginBottom: '16px' }}
                  />
                  <br />
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={() => onUpdateBusinessData('logoUrl', '')}
                  >
                    Remove Logo
                  </Button>
                </Box>
              ) : (
                <Box>
                  <ImageIcon size={48} style={{ color: '#ccc', marginBottom: '16px' }} />
                  <br />
                  <Button
                    variant="contained"
                    component="label"
                    startIcon={<Upload size={16} />}
                  >
                    Upload Logo
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={(e) => handleImageUpload('logoUrl', e)}
                    />
                  </Button>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Favicon Upload */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>Favicon</Typography>
              {businessData.faviconUrl ? (
                <Box>
                  <img
                    src={businessData.faviconUrl}
                    alt="Favicon"
                    style={{ width: '32px', height: '32px', marginBottom: '16px' }}
                  />
                  <br />
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={() => onUpdateBusinessData('faviconUrl', '')}
                  >
                    Remove Favicon
                  </Button>
                </Box>
              ) : (
                <Box>
                  <ImageIcon size={32} style={{ color: '#ccc', marginBottom: '16px' }} />
                  <br />
                  <Button
                    variant="contained"
                    component="label"
                    startIcon={<Upload size={16} />}
                  >
                    Upload Favicon
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={(e) => handleImageUpload('faviconUrl', e)}
                    />
                  </Button>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Gallery */}
          <Grid size={{ xs: 12 }}>
            <Paper sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Gallery</Typography>
                <Button
                  variant="contained"
                  startIcon={<Plus size={16} />}
                  onClick={addGalleryImage}
                >
                  Add Images
                </Button>
              </Box>

              {businessData.galleryImages.length > 0 ? (
                <Grid container spacing={2}>
                  {businessData.galleryImages.map((image: any, index: number) => (
                    <Grid size={{ xs: 6, sm: 4, md: 3 }} key={image.id}>
                      <Card>
                        <Box sx={{ position: 'relative' }}>
                          <img
                            src={image.url}
                            alt={image.alt}
                            style={{
                              width: '100%',
                              height: '120px',
                              objectFit: 'cover'
                            }}
                          />
                          <IconButton
                            sx={{
                              position: 'absolute',
                              top: 4,
                              right: 4,
                              bgcolor: 'rgba(255,255,255,0.8)'
                            }}
                            size="small"
                            onClick={() => removeGalleryImage(index)}
                          >
                            <Trash2 size={16} />
                          </IconButton>
                        </Box>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Alert severity="info">
                  No gallery images added yet. Click "Add Images" to showcase your work.
                </Alert>
              )}
            </Paper>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Social Media Tab */}
      <TabPanel value={currentTab} index={4}>
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Facebook URL"
              value={businessData.facebookUrl || ''}
              onChange={(e) => onUpdateBusinessData('facebookUrl', e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Instagram URL"
              value={businessData.instagramUrl || ''}
              onChange={(e) => onUpdateBusinessData('instagramUrl', e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6 }}>
            <TextField
              fullWidth
              label="Yelp URL"
              value={businessData.yelpUrl || ''}
              onChange={(e) => onUpdateBusinessData('yelpUrl', e.target.value)}
              margin="normal"
            />
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default EditContentTab;
