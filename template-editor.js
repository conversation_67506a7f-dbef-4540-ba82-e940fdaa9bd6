// Template Editor JavaScript
class TemplateEditor {
    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.updatePreview();
    }

    initializeElements() {
        // Form elements
        this.pageTitle = document.getElementById('pageTitle');
        this.businessName = document.getElementById('businessName');
        this.logoUrl = document.getElementById('logoUrl');
        this.faviconUrl = document.getElementById('faviconUrl');
        this.phone = document.getElementById('phone');
        this.address = document.getElementById('address');
        this.hours = document.getElementById('hours');
        this.websiteUrl = document.getElementById('websiteUrl');
        this.bookingUrl = document.getElementById('bookingUrl');
        this.facebookUrl = document.getElementById('facebookUrl');
        this.instagramUrl = document.getElementById('instagramUrl');
        this.googleMapsUrl = document.getElementById('googleMapsUrl');
        this.primaryColor = document.getElementById('primaryColor');
        this.secondaryColor = document.getElementById('secondaryColor');
        this.customCSS = document.getElementById('customCSS');
        this.enableCustomCSS = document.getElementById('enableCustomCSS');
        this.businessList = document.getElementById('businessList');

        // Image upload elements
        this.logoUpload = document.getElementById('logoUpload');
        this.galleryUpload = document.getElementById('galleryUpload');
        this.logoPreviewImg = document.getElementById('logoPreviewImg');
        this.logoPlaceholder = document.getElementById('logoPlaceholder');
        this.removeLogo = document.getElementById('removeLogo');
        this.galleryPreview = document.getElementById('galleryPreview');
        this.logoDropZone = document.getElementById('logoDropZone');
        this.galleryDropZone = document.getElementById('galleryDropZone');

        // Template selection
        this.templateOptions = document.querySelectorAll('.template-option');
        this.selectedTemplate = 'modern';

        // Preview elements
        this.previewFrame = document.getElementById('previewFrame');

        // Buttons
        this.exportBtn = document.getElementById('exportBtn');
        this.qrBtn = document.getElementById('qrBtn');
        this.batchExportBtn = document.getElementById('batchExportBtn');

        // QR Modal elements
        this.qrModal = document.getElementById('qrModal');
        this.qrUrl = document.getElementById('qrUrl');
        this.generateQr = document.getElementById('generateQr');
        this.downloadQr = document.getElementById('downloadQr');
        this.downloadQrSvg = document.getElementById('downloadQrSvg');
        this.closeQrModal = document.getElementById('closeQrModal');

        // QR Customization elements
        this.qrSize = document.getElementById('qrSize');
        this.qrErrorLevel = document.getElementById('qrErrorLevel');
        this.qrForeground = document.getElementById('qrForeground');
        this.qrBackground = document.getElementById('qrBackground');
        this.qrForegroundText = document.getElementById('qrForegroundText');
        this.qrBackgroundText = document.getElementById('qrBackgroundText');
        this.qrLogo = document.getElementById('qrLogo');

        // Enhanced Preview elements
        this.deviceFrame = document.getElementById('deviceFrame');
        this.mobileView = document.getElementById('mobileView');
        this.tabletView = document.getElementById('tabletView');
        this.desktopView = document.getElementById('desktopView');
        this.fullscreenPreview = document.getElementById('fullscreenPreview');
        this.sharePreview = document.getElementById('sharePreview');
        this.refreshPreview = document.getElementById('refreshPreview');
        this.autoRefresh = document.getElementById('autoRefresh');
        this.showGrid = document.getElementById('showGrid');
        this.previewStatus = document.getElementById('previewStatus');
        this.previewUrl = document.getElementById('previewUrl');
        this.copyPreviewUrl = document.getElementById('copyPreviewUrl');

        // Fullscreen Modal elements
        this.fullscreenModal = document.getElementById('fullscreenModal');
        this.fullscreenDeviceFrame = document.getElementById('fullscreenDeviceFrame');
        this.fullscreenPreviewFrame = document.getElementById('fullscreenPreviewFrame');
        this.fullscreenMobile = document.getElementById('fullscreenMobile');
        this.fullscreenTablet = document.getElementById('fullscreenTablet');
        this.fullscreenDesktop = document.getElementById('fullscreenDesktop');
        this.closeFullscreen = document.getElementById('closeFullscreen');

        // Share Modal elements
        this.shareModal = document.getElementById('shareModal');
        this.shareUrl = document.getElementById('shareUrl');
        this.copyShareUrl = document.getElementById('copyShareUrl');
        this.shareQrCode = document.getElementById('shareQrCode');
        this.generateShareUrl = document.getElementById('generateShareUrl');
        this.closeShareModal = document.getElementById('closeShareModal');

        // Data Management elements
        this.profileName = document.getElementById('profileName');
        this.saveProfile = document.getElementById('saveProfile');
        this.savedProfiles = document.getElementById('savedProfiles');
        this.recentProjects = document.getElementById('recentProjects');
        this.clearRecent = document.getElementById('clearRecent');
        this.exportBackup = document.getElementById('exportBackup');
        this.importBackup = document.getElementById('importBackup');
        this.backupFile = document.getElementById('backupFile');
        this.bulkEdit = document.getElementById('bulkEdit');
        this.resetAll = document.getElementById('resetAll');

        // Bulk Edit Modal elements
        this.bulkEditModal = document.getElementById('bulkEditModal');
        this.bulkTemplate = document.getElementById('bulkTemplate');
        this.bulkPrimaryColor = document.getElementById('bulkPrimaryColor');
        this.bulkLogoUrl = document.getElementById('bulkLogoUrl');
        this.bulkWebsiteDomain = document.getElementById('bulkWebsiteDomain');
        this.applyBulkChanges = document.getElementById('applyBulkChanges');
        this.bulkBusinessList = document.getElementById('bulkBusinessList');
        this.addBulkBusiness = document.getElementById('addBulkBusiness');
        this.loadFromProfiles = document.getElementById('loadFromProfiles');
        this.exportBulkBusinesses = document.getElementById('exportBulkBusinesses');
        this.closeBulkEdit = document.getElementById('closeBulkEdit');

        // Enhanced Content Editor elements
        this.basicView = document.getElementById('basicView');
        this.visualView = document.getElementById('visualView');
        this.advancedView = document.getElementById('advancedView');
        this.basicContentView = document.getElementById('basicContentView');
        this.visualContentView = document.getElementById('visualContentView');
        this.advancedContentView = document.getElementById('advancedContentView');
        this.contentTemplates = document.getElementById('contentTemplates');
        this.contentPresets = document.getElementById('contentPresets');

        // Visual Editor elements
        this.availableBlocks = document.getElementById('availableBlocks');
        this.contentCanvas = document.getElementById('contentCanvas');
        this.blockPropertiesPanel = document.getElementById('blockPropertiesPanel');
        this.blockPropertiesContent = document.getElementById('blockPropertiesContent');

        // Advanced Editor elements
        this.richTextEditor = document.getElementById('richTextEditor');
        this.contentBgColor = document.getElementById('contentBgColor');
        this.contentTextColor = document.getElementById('contentTextColor');
        this.contentFontSize = document.getElementById('contentFontSize');
        this.contentFontWeight = document.getElementById('contentFontWeight');
        this.insertLink = document.getElementById('insertLink');
        this.insertImage = document.getElementById('insertImage');
        this.insertIcon = document.getElementById('insertIcon');

        // Content Templates Modal elements
        this.contentTemplatesModal = document.getElementById('contentTemplatesModal');
        this.applyTemplate = document.getElementById('applyTemplate');
        this.closeContentTemplates = document.getElementById('closeContentTemplates');

        // Icon Selector Modal elements
        this.iconSelectorModal = document.getElementById('iconSelectorModal');
        this.iconSearch = document.getElementById('iconSearch');
        this.iconGrid = document.getElementById('iconGrid');
        this.insertSelectedIcon = document.getElementById('insertSelectedIcon');
        this.closeIconSelector = document.getElementById('closeIconSelector');

        // Business Hours & Services elements
        this.businessHours = document.getElementById('businessHours');
        this.useSimpleHours = document.getElementById('useSimpleHours');
        this.servicesList = document.getElementById('servicesList');
        this.addService = document.getElementById('addService');
        this.offersList = document.getElementById('offersList');
        this.addOffer = document.getElementById('addOffer');

        // Layout Options elements
        this.layoutStyleOptions = document.querySelectorAll('.layout-style-option');
        this.sectionToggles = document.getElementById('sectionToggles');
        this.sectionOrder = document.getElementById('sectionOrder');
        this.mobileOptimized = document.getElementById('mobileOptimized');
        this.showOnDesktop = document.getElementById('showOnDesktop');
        this.compactMode = document.getElementById('compactMode');

        // Initialize business data
        this.businessData = {
            gallery: [],
            operatingHours: {
                monday: { open: '09:00', close: '20:00', closed: false },
                tuesday: { open: '09:00', close: '20:00', closed: false },
                wednesday: { open: '09:00', close: '20:00', closed: false },
                thursday: { open: '09:00', close: '20:00', closed: false },
                friday: { open: '09:00', close: '20:00', closed: false },
                saturday: { open: '09:00', close: '18:00', closed: false },
                sunday: { open: '11:00', close: '17:00', closed: false }
            },
            services: [
                { id: 1, name: 'Manicure', price: '$25', description: 'Classic nail care' },
                { id: 2, name: 'Pedicure', price: '$35', description: 'Foot care and nail polish' },
                { id: 3, name: 'Gel Nails', price: '$45', description: 'Long-lasting gel polish' }
            ],
            offers: [
                { id: 1, title: 'New Customer Special', description: '20% off first visit', validUntil: '2024-12-31' }
            ],
            useSimpleHoursFormat: true,
            layout: {
                style: 'card', // 'card' or 'full'
                sections: {
                    social: { visible: true, order: 1 },
                    services: { visible: true, order: 2 },
                    offers: { visible: true, order: 3 },
                    gallery: { visible: true, order: 4 },
                    contact: { visible: true, order: 5 }
                },
                responsive: {
                    mobileOptimized: true,
                    showOnDesktop: true,
                    compactMode: false
                }
            },
            preview: {
                currentDevice: 'mobile',
                autoRefresh: true,
                showGrid: false,
                shareUrl: null,
                lastUpdate: null
            },
            dataManagement: {
                savedProfiles: [],
                recentProjects: [],
                bulkBusinesses: []
            },
            contentEditor: {
                currentView: 'basic',
                currentBasicTab: 'basic-info',
                visualBlocks: [],
                selectedBlock: null,
                richTextContent: '',
                customStyling: {
                    backgroundColor: '#ffffff',
                    textColor: '#000000',
                    fontSize: '14px',
                    fontWeight: 'normal'
                }
            }
        };
    }

    bindEvents() {
        // Form change events
        const formElements = [
            this.pageTitle, this.businessName, this.logoUrl, this.faviconUrl,
            this.phone, this.address, this.hours, this.websiteUrl, this.bookingUrl,
            this.facebookUrl, this.instagramUrl, this.googleMapsUrl, this.primaryColor,
            this.secondaryColor, this.customCSS, this.enableCustomCSS
        ];

        formElements.forEach(element => {
            element.addEventListener('input', () => this.updatePreview());
            element.addEventListener('change', () => this.updatePreview());
        });

        // Image upload events
        this.logoUpload.addEventListener('change', (e) => this.handleImageUpload(e, 'logo'));
        this.galleryUpload.addEventListener('change', (e) => this.handleImageUpload(e, 'gallery'));
        this.removeLogo.addEventListener('click', () => this.removeLogoImage());

        // Drag and drop events
        this.logoDropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.logoDropZone.addEventListener('drop', (e) => this.handleDrop(e, 'logo'));
        this.logoDropZone.addEventListener('dragenter', () => this.logoDropZone.classList.add('drag-over'));
        this.logoDropZone.addEventListener('dragleave', () => this.logoDropZone.classList.remove('drag-over'));

        this.galleryDropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.galleryDropZone.addEventListener('drop', (e) => this.handleDrop(e, 'gallery'));
        this.galleryDropZone.addEventListener('dragenter', () => this.galleryDropZone.classList.add('drag-over'));
        this.galleryDropZone.addEventListener('dragleave', () => this.galleryDropZone.classList.remove('drag-over'));

        // Template selection events
        this.templateOptions.forEach(option => {
            option.addEventListener('click', () => this.selectTemplate(option.dataset.template));
        });

        // Button events
        this.exportBtn.addEventListener('click', () => this.exportHTML());
        this.qrBtn.addEventListener('click', () => this.showQRModal());
        this.batchExportBtn.addEventListener('click', () => this.batchExport());

        // QR Modal events
        this.generateQr.addEventListener('click', () => this.generateQRCode());
        this.downloadQr.addEventListener('click', () => this.downloadQRCode());
        this.downloadQrSvg.addEventListener('click', () => this.downloadQRCodeSVG());
        this.closeQrModal.addEventListener('click', () => this.hideQRModal());

        // QR Customization events
        this.qrForeground.addEventListener('input', () => {
            this.qrForegroundText.textContent = this.qrForeground.value;
        });
        this.qrBackground.addEventListener('input', () => {
            this.qrBackgroundText.textContent = this.qrBackground.value;
        });

        // Business Hours & Services events
        this.useSimpleHours.addEventListener('click', () => this.toggleHoursFormat());
        this.addService.addEventListener('click', () => this.addNewService());
        this.addOffer.addEventListener('click', () => this.addNewOffer());

        // Layout Options events
        this.layoutStyleOptions.forEach(option => {
            option.addEventListener('click', () => this.selectLayoutStyle(option.dataset.layout));
        });
        this.mobileOptimized.addEventListener('change', () => this.updateResponsiveSettings());
        this.showOnDesktop.addEventListener('change', () => this.updateResponsiveSettings());
        this.compactMode.addEventListener('change', () => this.updateResponsiveSettings());

        // Enhanced Preview events
        this.mobileView.addEventListener('click', () => this.switchDevice('mobile'));
        this.tabletView.addEventListener('click', () => this.switchDevice('tablet'));
        this.desktopView.addEventListener('click', () => this.switchDevice('desktop'));
        this.fullscreenPreview.addEventListener('click', () => this.showFullscreenPreview());
        this.sharePreview.addEventListener('click', () => this.showShareModal());
        this.refreshPreview.addEventListener('click', () => this.forceRefreshPreview());
        this.autoRefresh.addEventListener('change', () => this.toggleAutoRefresh());
        this.showGrid.addEventListener('change', () => this.toggleGrid());
        this.copyPreviewUrl.addEventListener('click', (e) => this.copyToClipboard(this.previewUrl.value, e.target));

        // Fullscreen Modal events
        this.fullscreenMobile.addEventListener('click', () => this.switchFullscreenDevice('mobile'));
        this.fullscreenTablet.addEventListener('click', () => this.switchFullscreenDevice('tablet'));
        this.fullscreenDesktop.addEventListener('click', () => this.switchFullscreenDevice('desktop'));
        this.closeFullscreen.addEventListener('click', () => this.hideFullscreenPreview());

        // Share Modal events
        this.generateShareUrl.addEventListener('click', () => this.generateShareableUrl());
        this.copyShareUrl.addEventListener('click', (e) => this.copyToClipboard(this.shareUrl.value, e.target));
        this.closeShareModal.addEventListener('click', () => this.hideShareModal());

        // Data Management events
        this.saveProfile.addEventListener('click', () => this.saveBusinessProfile());
        this.clearRecent.addEventListener('click', () => this.clearRecentProjects());
        this.exportBackup.addEventListener('click', () => this.exportBackupData());
        this.importBackup.addEventListener('click', () => this.backupFile.click());
        this.backupFile.addEventListener('change', (e) => this.importBackupData(e));
        this.bulkEdit.addEventListener('click', () => this.showBulkEditModal());
        this.resetAll.addEventListener('click', () => this.resetAllData());

        // Bulk Edit Modal events
        this.applyBulkChanges.addEventListener('click', () => this.applyBulkChangesToAll());
        this.addBulkBusiness.addEventListener('click', () => this.addBulkBusiness());
        this.loadFromProfiles.addEventListener('click', () => this.loadBusinessesFromProfiles());
        this.exportBulkBusinesses.addEventListener('click', () => this.exportBulkBusinesses());
        this.closeBulkEdit.addEventListener('click', () => this.hideBulkEditModal());

        // Enhanced Content Editor events
        this.basicView.addEventListener('click', () => this.switchContentView('basic'));
        this.visualView.addEventListener('click', () => this.switchContentView('visual'));
        this.advancedView.addEventListener('click', () => this.switchContentView('advanced'));
        this.contentTemplates.addEventListener('click', () => this.showContentTemplatesModal());
        this.contentPresets.addEventListener('click', () => this.showContentPresets());

        // Visual Editor events
        this.contentCanvas.addEventListener('dragover', (e) => this.handleCanvasDragOver(e));
        this.contentCanvas.addEventListener('drop', (e) => this.handleCanvasDrop(e));
        this.contentCanvas.addEventListener('click', (e) => this.handleCanvasClick(e));

        // Advanced Editor events
        this.richTextEditor.addEventListener('input', () => this.updateRichTextContent());
        this.contentBgColor.addEventListener('change', () => this.updateCustomStyling());
        this.contentTextColor.addEventListener('change', () => this.updateCustomStyling());
        this.contentFontSize.addEventListener('change', () => this.updateCustomStyling());
        this.contentFontWeight.addEventListener('change', () => this.updateCustomStyling());
        this.insertLink.addEventListener('click', () => this.insertLinkDialog());
        this.insertImage.addEventListener('click', () => this.insertImageDialog());
        this.insertIcon.addEventListener('click', () => this.showIconSelector());

        // Rich Text Editor toolbar events
        document.querySelectorAll('.editor-btn[data-command]').forEach(btn => {
            btn.addEventListener('click', (e) => this.executeEditorCommand(e.target.dataset.command));
        });

        // Content Templates Modal events
        this.applyTemplate.addEventListener('click', () => this.applySelectedTemplate());
        this.closeContentTemplates.addEventListener('click', () => this.hideContentTemplatesModal());

        // Icon Selector Modal events
        this.iconSearch.addEventListener('input', () => this.filterIcons());
        this.insertSelectedIcon.addEventListener('click', () => this.insertSelectedIconToEditor());
        this.closeIconSelector.addEventListener('click', () => this.hideIconSelector());

        // Close modals on outside click
        this.fullscreenModal.addEventListener('click', (e) => {
            if (e.target === this.fullscreenModal) this.hideFullscreenPreview();
        });
        this.shareModal.addEventListener('click', (e) => {
            if (e.target === this.shareModal) this.hideShareModal();
        });
        this.bulkEditModal.addEventListener('click', (e) => {
            if (e.target === this.bulkEditModal) this.hideBulkEditModal();
        });
        this.contentTemplatesModal.addEventListener('click', (e) => {
            if (e.target === this.contentTemplatesModal) this.hideContentTemplatesModal();
        });
        this.iconSelectorModal.addEventListener('click', (e) => {
            if (e.target === this.iconSelectorModal) this.hideIconSelector();
        });

        // Initialize all components
        this.initializeBusinessHours();
        this.renderServices();
        this.renderOffers();
        this.initializeSectionToggles();
        this.initializeSectionOrder();
        this.renderSavedProfiles();
        this.renderRecentProjects();

        // Initialize enhanced content editor
        this.switchContentView('basic'); // Start with basic view

        // Initialize enhanced basic view UX
        this.initializeEnhancedBasicView();

        // Close modal on outside click
        this.qrModal.addEventListener('click', (e) => {
            if (e.target === this.qrModal) this.hideQRModal();
        });
    }

    selectTemplate(templateName) {
        this.selectedTemplate = templateName;

        // Update UI
        this.templateOptions.forEach(option => {
            option.classList.remove('border-blue-500', 'bg-blue-50');
            option.classList.add('border-gray-300');
        });

        const selectedOption = document.querySelector(`[data-template="${templateName}"]`);
        selectedOption.classList.remove('border-gray-300');
        selectedOption.classList.add('border-blue-500', 'bg-blue-50');

        // Update colors based on template
        this.updateTemplateColors(templateName);
        this.updatePreview();
    }

    updateTemplateColors(templateName) {
        const colorSchemes = {
            modern: { primary: '#3B82F6', secondary: '#1F2937' },
            luxury: { primary: '#D4AF37', secondary: '#222222' },
            minimal: { primary: '#6B7280', secondary: '#111827' },
            cute: { primary: '#EC4899', secondary: '#BE185D' }
        };

        const scheme = colorSchemes[templateName];
        if (scheme) {
            this.primaryColor.value = scheme.primary;
            this.secondaryColor.value = scheme.secondary;
        }
    }

    // Enhanced Image Upload Handler with optimization
    handleImageUpload(event, type) {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        if (type === 'gallery') {
            // Handle multiple files for gallery
            Array.from(files).forEach(file => this.processImageFile(file, type));
        } else {
            // Handle single file for logo
            this.processImageFile(files[0], type);
        }
    }

    processImageFile(file, type) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file');
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('Image size should be less than 5MB');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            // Create image for optimization
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Set max dimensions based on type
                let maxWidth, maxHeight;
                if (type === 'logo') {
                    maxWidth = 400;
                    maxHeight = 200;
                } else {
                    maxWidth = 800;
                    maxHeight = 600;
                }

                // Calculate new dimensions
                let { width, height } = img;
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }

                // Resize image
                canvas.width = width;
                canvas.height = height;
                ctx.drawImage(img, 0, 0, width, height);

                // Convert to optimized data URL
                const optimizedDataUrl = canvas.toDataURL('image/jpeg', 0.8);

                if (type === 'logo') {
                    this.setLogo(optimizedDataUrl);
                } else if (type === 'gallery') {
                    this.addGalleryImage({
                        id: Date.now() + Math.random(),
                        url: optimizedDataUrl,
                        caption: '',
                        filename: file.name
                    });
                }
                this.updatePreview();
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    setLogo(dataUrl) {
        this.logoUrl.value = dataUrl;
        this.logoPreviewImg.src = dataUrl;
        this.logoPreviewImg.classList.remove('hidden');
        this.logoPlaceholder.classList.add('hidden');
        this.removeLogo.classList.remove('hidden');
    }

    removeLogoImage() {
        this.logoUrl.value = '';
        this.logoPreviewImg.src = '';
        this.logoPreviewImg.classList.add('hidden');
        this.logoPlaceholder.classList.remove('hidden');
        this.removeLogo.classList.add('hidden');
        this.updatePreview();
    }

    addGalleryImage(imageData) {
        this.businessData.gallery.push(imageData);
        this.renderGalleryPreview();
    }

    removeGalleryImage(imageId) {
        this.businessData.gallery = this.businessData.gallery.filter(img => img.id !== imageId);
        this.renderGalleryPreview();
        this.updatePreview();
    }

    updateImageCaption(imageId, caption) {
        const image = this.businessData.gallery.find(img => img.id === imageId);
        if (image) {
            image.caption = caption;
            this.updatePreview();
        }
    }

    renderGalleryPreview() {
        const container = this.galleryPreview;

        if (this.businessData.gallery.length === 0) {
            container.classList.add('hidden');
            return;
        }

        container.classList.remove('hidden');
        container.innerHTML = '';

        this.businessData.gallery.forEach(image => {
            const imageDiv = document.createElement('div');
            imageDiv.className = 'gallery-item relative';
            imageDiv.innerHTML = `
                <img src="${image.url}" alt="${image.caption || 'Gallery image'}" class="w-full h-24 object-cover">
                <button class="remove-btn" onclick="templateEditor.removeGalleryImage('${image.id}')">
                    <i class="ri-close-line text-xs"></i>
                </button>
                <input type="text"
                       class="caption-input"
                       placeholder="Add caption..."
                       value="${image.caption}"
                       onchange="templateEditor.updateImageCaption('${image.id}', this.value)">
            `;
            container.appendChild(imageDiv);
        });
    }

    // Handle drag and drop for images
    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
    }

    handleDrop(event, type) {
        event.preventDefault();
        event.target.classList.remove('drag-over');

        const files = event.dataTransfer.files;
        if (files.length > 0) {
            if (type === 'gallery') {
                Array.from(files).forEach(file => this.processImageFile(file, type));
            } else {
                this.processImageFile(files[0], type);
            }
        }
    }

    // Business Hours & Services Methods
    initializeBusinessHours() {
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        this.businessHours.innerHTML = '';

        days.forEach((day, index) => {
            const dayData = this.businessData.operatingHours[day];
            const dayDiv = document.createElement('div');
            dayDiv.className = 'flex items-center space-x-3 p-3 bg-gray-50 rounded-lg';
            dayDiv.innerHTML = `
                <div class="w-20 text-sm font-medium">${dayNames[index]}</div>
                <label class="flex items-center">
                    <input type="checkbox" ${dayData.closed ? '' : 'checked'}
                           onchange="templateEditor.toggleDayStatus('${day}', this.checked)"
                           class="mr-2">
                    <span class="text-sm">Open</span>
                </label>
                <div class="flex items-center space-x-2 ${dayData.closed ? 'opacity-50' : ''}">
                    <input type="time" value="${dayData.open}"
                           onchange="templateEditor.updateDayHours('${day}', 'open', this.value)"
                           ${dayData.closed ? 'disabled' : ''}
                           class="text-sm border border-gray-300 rounded px-2 py-1">
                    <span class="text-sm">to</span>
                    <input type="time" value="${dayData.close}"
                           onchange="templateEditor.updateDayHours('${day}', 'close', this.value)"
                           ${dayData.closed ? 'disabled' : ''}
                           class="text-sm border border-gray-300 rounded px-2 py-1">
                </div>
            `;
            this.businessHours.appendChild(dayDiv);
        });
    }

    toggleDayStatus(day, isOpen) {
        this.businessData.operatingHours[day].closed = !isOpen;
        this.initializeBusinessHours();
        this.updatePreview();
    }

    updateDayHours(day, timeType, value) {
        this.businessData.operatingHours[day][timeType] = value;
        this.updatePreview();
    }

    toggleHoursFormat() {
        this.businessData.useSimpleHoursFormat = !this.businessData.useSimpleHoursFormat;

        if (this.businessData.useSimpleHoursFormat) {
            this.businessHours.style.display = 'none';
            this.hours.style.display = 'block';
            this.useSimpleHours.textContent = 'Use detailed hours format instead';
        } else {
            this.businessHours.style.display = 'block';
            this.hours.style.display = 'none';
            this.useSimpleHours.textContent = 'Use simple text format instead';
            this.initializeBusinessHours();
        }
        this.updatePreview();
    }

    addNewService() {
        const newService = {
            id: Date.now(),
            name: 'New Service',
            price: '$0',
            description: 'Service description'
        };
        this.businessData.services.push(newService);
        this.renderServices();
        this.updatePreview();
    }

    removeService(serviceId) {
        this.businessData.services = this.businessData.services.filter(s => s.id !== serviceId);
        this.renderServices();
        this.updatePreview();
    }

    updateService(serviceId, field, value) {
        const service = this.businessData.services.find(s => s.id === serviceId);
        if (service) {
            service[field] = value;
            this.updatePreview();
        }
    }

    renderServices() {
        this.servicesList.innerHTML = '';

        this.businessData.services.forEach(service => {
            const serviceDiv = document.createElement('div');
            serviceDiv.className = 'p-3 bg-gray-50 rounded-lg';
            serviceDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <input type="text" value="${service.name}"
                           onchange="templateEditor.updateService(${service.id}, 'name', this.value)"
                           class="font-medium bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2 py-1">
                    <div class="flex items-center space-x-2">
                        <input type="text" value="${service.price}"
                               onchange="templateEditor.updateService(${service.id}, 'price', this.value)"
                               class="text-sm bg-transparent border border-gray-300 rounded px-2 py-1 w-20">
                        <button onclick="templateEditor.removeService(${service.id})"
                                class="text-red-500 hover:text-red-700">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                </div>
                <textarea onchange="templateEditor.updateService(${service.id}, 'description', this.value)"
                          class="w-full text-sm bg-transparent border border-gray-300 rounded px-2 py-1 resize-none"
                          rows="2" placeholder="Service description">${service.description}</textarea>
            `;
            this.servicesList.appendChild(serviceDiv);
        });
    }

    addNewOffer() {
        const newOffer = {
            id: Date.now(),
            title: 'Special Offer',
            description: 'Offer description',
            validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
        };
        this.businessData.offers.push(newOffer);
        this.renderOffers();
        this.updatePreview();
    }

    removeOffer(offerId) {
        this.businessData.offers = this.businessData.offers.filter(o => o.id !== offerId);
        this.renderOffers();
        this.updatePreview();
    }

    updateOffer(offerId, field, value) {
        const offer = this.businessData.offers.find(o => o.id === offerId);
        if (offer) {
            offer[field] = value;
            this.updatePreview();
        }
    }

    renderOffers() {
        this.offersList.innerHTML = '';

        this.businessData.offers.forEach(offer => {
            const offerDiv = document.createElement('div');
            offerDiv.className = 'p-3 bg-purple-50 rounded-lg border border-purple-200';
            offerDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <input type="text" value="${offer.title}"
                           onchange="templateEditor.updateOffer(${offer.id}, 'title', this.value)"
                           class="font-medium bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-purple-500 rounded px-2 py-1">
                    <button onclick="templateEditor.removeOffer(${offer.id})"
                            class="text-red-500 hover:text-red-700">
                        <i class="ri-delete-bin-line"></i>
                    </button>
                </div>
                <textarea onchange="templateEditor.updateOffer(${offer.id}, 'description', this.value)"
                          class="w-full text-sm bg-transparent border border-purple-300 rounded px-2 py-1 resize-none mb-2"
                          rows="2" placeholder="Offer description">${offer.description}</textarea>
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-gray-600">Valid until:</label>
                    <input type="date" value="${offer.validUntil}"
                           onchange="templateEditor.updateOffer(${offer.id}, 'validUntil', this.value)"
                           class="text-sm border border-purple-300 rounded px-2 py-1">
                </div>
            `;
            this.offersList.appendChild(offerDiv);
        });
    }

    generateFormattedHours() {
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

        return days.map((day, index) => {
            const dayData = this.businessData.operatingHours[day];
            if (dayData.closed) {
                return `<div class="text-sm"><span class="font-medium">${dayNames[index]}:</span> Closed</div>`;
            } else {
                const openTime = this.formatTime(dayData.open);
                const closeTime = this.formatTime(dayData.close);
                return `<div class="text-sm"><span class="font-medium">${dayNames[index]}:</span> ${openTime} - ${closeTime}</div>`;
            }
        }).join('');
    }

    formatTime(time24) {
        const [hours, minutes] = time24.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const hour12 = hour % 12 || 12;
        return `${hour12}:${minutes} ${ampm}`;
    }

    // Layout Options Methods
    selectLayoutStyle(layoutStyle) {
        this.businessData.layout.style = layoutStyle;

        // Update UI
        this.layoutStyleOptions.forEach(option => {
            option.classList.remove('border-blue-500', 'bg-blue-50');
            option.classList.add('border-gray-300');
        });

        const selectedOption = document.querySelector(`[data-layout="${layoutStyle}"]`);
        selectedOption.classList.remove('border-gray-300');
        selectedOption.classList.add('border-blue-500', 'bg-blue-50');

        this.updatePreview();
    }

    initializeSectionToggles() {
        const sections = [
            { key: 'social', name: 'Social Media Links', icon: 'ri-share-line' },
            { key: 'services', name: 'Services & Pricing', icon: 'ri-service-line' },
            { key: 'offers', name: 'Special Offers', icon: 'ri-gift-line' },
            { key: 'gallery', name: 'Photo Gallery', icon: 'ri-gallery-line' },
            { key: 'contact', name: 'Contact Information', icon: 'ri-phone-line' }
        ];

        this.sectionToggles.innerHTML = '';

        sections.forEach(section => {
            const sectionData = this.businessData.layout.sections[section.key];
            const toggleDiv = document.createElement('div');
            toggleDiv.className = 'section-toggle flex items-center justify-between p-3 bg-gray-50 rounded-lg';
            toggleDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="${section.icon} mr-3 text-gray-600"></i>
                    <span class="text-sm font-medium">${section.name}</span>
                </div>
                <label class="flex items-center">
                    <input type="checkbox" ${sectionData.visible ? 'checked' : ''}
                           onchange="templateEditor.toggleSection('${section.key}', this.checked)"
                           class="mr-2">
                    <span class="text-sm">Show</span>
                </label>
            `;
            this.sectionToggles.appendChild(toggleDiv);
        });
    }

    toggleSection(sectionKey, isVisible) {
        this.businessData.layout.sections[sectionKey].visible = isVisible;
        this.updatePreview();
    }

    initializeSectionOrder() {
        const sections = [
            { key: 'social', name: 'Social Media Links', icon: 'ri-share-line' },
            { key: 'services', name: 'Services & Pricing', icon: 'ri-service-line' },
            { key: 'offers', name: 'Special Offers', icon: 'ri-gift-line' },
            { key: 'gallery', name: 'Photo Gallery', icon: 'ri-gallery-line' },
            { key: 'contact', name: 'Contact Information', icon: 'ri-phone-line' }
        ];

        // Sort sections by order
        const sortedSections = sections.sort((a, b) => {
            return this.businessData.layout.sections[a.key].order - this.businessData.layout.sections[b.key].order;
        });

        this.sectionOrder.innerHTML = '';

        sortedSections.forEach((section, index) => {
            const orderDiv = document.createElement('div');
            orderDiv.className = 'section-order-item flex items-center p-3 bg-gray-50 rounded-lg border';
            orderDiv.draggable = true;
            orderDiv.dataset.section = section.key;
            orderDiv.innerHTML = `
                <div class="drag-handle mr-3 text-gray-400 cursor-move">
                    <i class="ri-drag-move-line"></i>
                </div>
                <i class="${section.icon} mr-3 text-gray-600"></i>
                <span class="text-sm font-medium flex-1">${section.name}</span>
                <span class="text-xs text-gray-500">#${index + 1}</span>
            `;

            // Add drag and drop event listeners
            orderDiv.addEventListener('dragstart', (e) => this.handleDragStart(e));
            orderDiv.addEventListener('dragover', (e) => this.handleDragOver(e));
            orderDiv.addEventListener('drop', (e) => this.handleSectionDrop(e));
            orderDiv.addEventListener('dragend', (e) => this.handleDragEnd(e));

            this.sectionOrder.appendChild(orderDiv);
        });
    }

    handleDragStart(e) {
        e.target.classList.add('dragging');
        e.dataTransfer.setData('text/plain', e.target.dataset.section);
    }

    handleSectionDrop(e) {
        e.preventDefault();
        const draggedSection = e.dataTransfer.getData('text/plain');
        const targetSection = e.target.closest('.section-order-item').dataset.section;

        if (draggedSection !== targetSection) {
            this.reorderSections(draggedSection, targetSection);
        }
    }

    handleDragEnd(e) {
        e.target.classList.remove('dragging');
    }

    reorderSections(draggedSection, targetSection) {
        const sections = this.businessData.layout.sections;
        const draggedOrder = sections[draggedSection].order;
        const targetOrder = sections[targetSection].order;

        // Swap orders
        sections[draggedSection].order = targetOrder;
        sections[targetSection].order = draggedOrder;

        // Re-render section order
        this.initializeSectionOrder();
        this.updatePreview();
    }

    updateResponsiveSettings() {
        this.businessData.layout.responsive = {
            mobileOptimized: this.mobileOptimized.checked,
            showOnDesktop: this.showOnDesktop.checked,
            compactMode: this.compactMode.checked
        };
        this.updatePreview();
    }

    getSortedSections() {
        const sectionKeys = Object.keys(this.businessData.layout.sections);
        return sectionKeys.sort((a, b) => {
            return this.businessData.layout.sections[a].order - this.businessData.layout.sections[b].order;
        });
    }

    generateDynamicSections(data) {
        const sortedSections = this.getSortedSections();
        const layoutStyle = this.businessData.layout.style;
        const isCompact = this.businessData.layout.responsive.compactMode;

        let sectionsHtml = '';

        sortedSections.forEach(sectionKey => {
            const section = this.businessData.layout.sections[sectionKey];
            if (!section.visible) return;

            const containerClass = layoutStyle === 'card' ?
                'px-5 py-6 bg-white mb-4 mx-4 rounded-lg shadow-sm' :
                'px-5 py-6 bg-white';

            const compactClass = isCompact ? 'py-4' : 'py-6';

            switch (sectionKey) {
                case 'social':
                    sectionsHtml += `
                        <div class="${containerClass} ${compactClass}">
                            <h2 class="text-2xl font-semibold text-secondary mb-2 animate-slideInLeft">Connect with us</h2>
                            <p class="text-gray-600 mb-6 animate-fadeInUp">Follow us and get updates delivered to your favorite social media channel.</p>

                            <div class="space-y-4 stagger-animation">
                                <a href="${data.facebookUrl}" target="_blank" class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                                    <div class="w-10 h-10 flex items-center justify-center bg-[#1877F2] rounded-full mr-4">
                                        <i class="ri-facebook-fill ri-lg text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-secondary">Facebook</h3>
                                        <p class="text-sm text-gray-500">Visit our Facebook page</p>
                                    </div>
                                </a>

                                ${data.instagramUrl ? `<a href="${data.instagramUrl}" target="_blank" class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                                    <div class="w-10 h-10 flex items-center justify-center rounded-full mr-4" style="background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);">
                                        <i class="ri-instagram-line ri-lg text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-secondary">Instagram</h3>
                                        <p class="text-sm text-gray-500">Follow our nail art</p>
                                    </div>
                                </a>` : ''}

                                <a href="${data.googleMapsUrl}" target="_blank" class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                                    <div class="w-10 h-10 flex items-center justify-center bg-white rounded-full mr-4 border border-gray-200">
                                        <i class="ri-google-fill ri-lg" style="color: #4285F4;"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-secondary">Google Review</h3>
                                        <p class="text-sm text-gray-500">Review us on Google</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    `;
                    break;

                case 'services':
                    if (this.businessData.services && this.businessData.services.length > 0) {
                        sectionsHtml += `
                            <div class="${containerClass} ${compactClass}">
                                <h2 class="text-2xl font-semibold text-secondary mb-6 animate-slideInLeft">Our Services</h2>
                                <div class="space-y-4 stagger-animation">
                                    ${this.businessData.services.map(service => `
                                        <div class="bg-gray-50 p-4 rounded-lg hover-scale">
                                            <div class="flex justify-between items-start mb-2">
                                                <h3 class="font-medium text-secondary">${service.name}</h3>
                                                <span class="text-primary font-semibold">${service.price}</span>
                                            </div>
                                            <p class="text-sm text-gray-600">${service.description}</p>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }
                    break;

                case 'offers':
                    if (this.businessData.offers && this.businessData.offers.length > 0) {
                        sectionsHtml += `
                            <div class="${containerClass} ${compactClass} bg-gradient-to-r from-purple-50 to-pink-50">
                                <h2 class="text-2xl font-semibold text-secondary mb-6 animate-slideInLeft">
                                    <i class="ri-gift-line mr-2"></i>Special Offers
                                </h2>
                                <div class="space-y-4 stagger-animation">
                                    ${this.businessData.offers.map(offer => `
                                        <div class="bg-white p-4 rounded-lg border border-purple-200 hover-scale">
                                            <h3 class="font-medium text-secondary mb-2">${offer.title}</h3>
                                            <p class="text-sm text-gray-600 mb-2">${offer.description}</p>
                                            <p class="text-xs text-purple-600">Valid until: ${new Date(offer.validUntil).toLocaleDateString()}</p>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }
                    break;

                case 'gallery':
                    if (this.businessData.gallery && this.businessData.gallery.length > 0) {
                        sectionsHtml += `
                            <div class="${containerClass} ${compactClass}">
                                <h2 class="text-2xl font-semibold text-secondary mb-6 animate-slideInLeft">Our Gallery</h2>
                                <div class="grid grid-cols-2 gap-3 stagger-animation">
                                    ${this.businessData.gallery.map(image => `
                                        <div class="relative rounded-lg overflow-hidden hover-scale">
                                            <img src="${image.url}" alt="${image.caption || 'Gallery image'}" class="w-full h-32 object-cover">
                                            ${image.caption ? `
                                                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white p-2">
                                                    <p class="text-xs">${image.caption}</p>
                                                </div>
                                            ` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }
                    break;

                case 'contact':
                    sectionsHtml += `
                        <div class="${containerClass} ${compactClass}">
                            <h2 class="text-2xl font-semibold text-secondary mb-4 animate-slideInLeft">Visit Us</h2>
                            <div class="bg-white p-4 rounded-lg shadow-sm mb-4 animate-fadeInUp hover-scale">
                                <div class="flex items-start mb-3">
                                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                                        <i class="ri-map-pin-line text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-secondary">Address</h3>
                                        <p class="text-gray-600">${data.address.replace(/\n/g, '<br>')}</p>
                                    </div>
                                </div>
                                <div class="flex items-start mb-3">
                                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                                        <i class="ri-time-line text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-secondary">Hours</h3>
                                        ${this.businessData.useSimpleHoursFormat ?
                                            `<p class="text-gray-600">${data.hours.replace(/\n/g, '<br>')}</p>` :
                                            `<div class="text-gray-600 space-y-1">
                                                ${this.generateFormattedHours()}
                                            </div>`
                                        }
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                                        <i class="ri-phone-line text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-secondary">Phone</h3>
                                        <p class="text-gray-600">${data.phone}</p>
                                    </div>
                                </div>
                            </div>

                            <a href="${data.googleMapsUrl}" target="_blank" class="w-full mt-4 py-3 bg-primary text-white font-medium rounded-button flex items-center justify-center cursor-pointer hover-scale">
                                <i class="ri-navigation-line ri-lg mr-2"></i>
                                <span>Get Directions</span>
                            </a>
                        </div>
                    `;
                    break;
            }
        });

        return sectionsHtml;
    }

    updatePreview() {
        if (!this.businessData.preview.autoRefresh) return;

        this.previewStatus.textContent = 'Updating...';

        const html = this.generateHTML();
        const blob = new Blob([html], { type: 'text/html' });
        const url = URL.createObjectURL(blob);

        this.previewFrame.src = url;
        this.businessData.preview.lastUpdate = new Date();

        // Update preview URL
        this.previewUrl.value = url;

        setTimeout(() => {
            this.previewStatus.textContent = 'Ready';
        }, 500);
    }

    // Enhanced Preview Methods
    switchDevice(device) {
        this.businessData.preview.currentDevice = device;

        // Update device frame
        this.deviceFrame.className = `device-frame ${device}-frame`;

        // Update button states
        document.querySelectorAll('.device-btn').forEach(btn => {
            btn.classList.remove('active', 'bg-blue-500', 'text-white');
            btn.classList.add('text-gray-600');
        });

        const activeBtn = document.getElementById(`${device}View`);
        activeBtn.classList.add('active', 'bg-blue-500', 'text-white');
        activeBtn.classList.remove('text-gray-600');

        this.updatePreview();
    }

    showFullscreenPreview() {
        this.fullscreenModal.classList.remove('hidden');
        this.fullscreenModal.classList.add('flex');

        // Copy current preview to fullscreen
        this.fullscreenPreviewFrame.src = this.previewFrame.src;
        this.switchFullscreenDevice(this.businessData.preview.currentDevice);
    }

    hideFullscreenPreview() {
        this.fullscreenModal.classList.add('hidden');
        this.fullscreenModal.classList.remove('flex');
    }

    switchFullscreenDevice(device) {
        // Update fullscreen device frame
        this.fullscreenDeviceFrame.className = `device-frame ${device}-frame`;

        // Update fullscreen button states
        document.querySelectorAll('.fullscreen-device-btn').forEach(btn => {
            btn.classList.remove('active', 'bg-blue-500', 'text-white');
            btn.classList.add('text-gray-300');
        });

        const activeBtn = document.getElementById(`fullscreen${device.charAt(0).toUpperCase() + device.slice(1)}`);
        activeBtn.classList.add('active', 'bg-blue-500', 'text-white');
        activeBtn.classList.remove('text-gray-300');
    }

    showShareModal() {
        this.shareModal.classList.remove('hidden');
        this.shareModal.classList.add('flex');

        if (this.businessData.preview.shareUrl) {
            this.shareUrl.value = this.businessData.preview.shareUrl;
            this.generateShareQR(this.businessData.preview.shareUrl);
        }
    }

    hideShareModal() {
        this.shareModal.classList.add('hidden');
        this.shareModal.classList.remove('flex');
    }

    async generateShareableUrl() {
        try {
            // Generate HTML
            const html = this.generateHTML();

            // Create a temporary URL (in real app, this would upload to server)
            const blob = new Blob([html], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            // Store the URL
            this.businessData.preview.shareUrl = url;
            this.shareUrl.value = url;

            // Generate QR code for the URL
            this.generateShareQR(url);

        } catch (error) {
            console.error('Error generating shareable URL:', error);
            alert('Error generating shareable URL');
        }
    }

    generateShareQR(url) {
        this.shareQrCode.innerHTML = '';

        QRCode.toCanvas(this.shareQrCode, url, {
            width: 200,
            height: 200,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.M
        }, (error) => {
            if (error) {
                console.error('Error generating share QR code:', error);
                this.shareQrCode.innerHTML = '<p class="text-red-500">Error generating QR code</p>';
            }
        });
    }

    forceRefreshPreview() {
        this.previewStatus.textContent = 'Refreshing...';
        this.updatePreview();
    }

    toggleAutoRefresh() {
        this.businessData.preview.autoRefresh = this.autoRefresh.checked;
        if (this.businessData.preview.autoRefresh) {
            this.updatePreview();
        }
    }

    toggleGrid() {
        this.businessData.preview.showGrid = this.showGrid.checked;

        const deviceScreen = this.deviceFrame.querySelector('.device-screen');
        if (this.businessData.preview.showGrid) {
            if (!deviceScreen.querySelector('.grid-overlay')) {
                const gridOverlay = document.createElement('div');
                gridOverlay.className = 'grid-overlay';
                deviceScreen.appendChild(gridOverlay);
            }
        } else {
            const gridOverlay = deviceScreen.querySelector('.grid-overlay');
            if (gridOverlay) {
                gridOverlay.remove();
            }
        }
    }

    async copyToClipboard(text, buttonElement = null) {
        try {
            await navigator.clipboard.writeText(text);
            // Show temporary success message if button element provided
            if (buttonElement) {
                const originalText = buttonElement.innerHTML;
                buttonElement.innerHTML = '<i class="ri-check-line"></i>';
                setTimeout(() => {
                    buttonElement.innerHTML = originalText;
                }, 1000);
            }
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }

    // Enhanced Content Editor Methods
    switchContentView(viewName) {
        // Update business data
        this.businessData.contentEditor.currentView = viewName;

        // Hide all views
        this.basicContentView.classList.remove('active');
        this.basicContentView.classList.add('hidden');
        this.visualContentView.classList.remove('active');
        this.visualContentView.classList.add('hidden');
        this.advancedContentView.classList.remove('active');
        this.advancedContentView.classList.add('hidden');

        // Update button states
        document.querySelectorAll('.content-view-btn').forEach(btn => {
            btn.classList.remove('active', 'bg-white', 'shadow-sm');
            btn.classList.add('text-gray-600', 'hover:bg-gray-50');
        });

        // Show selected view
        switch (viewName) {
            case 'basic':
                this.basicContentView.classList.add('active');
                this.basicContentView.classList.remove('hidden');
                this.basicView.classList.add('active', 'bg-white', 'shadow-sm');
                this.basicView.classList.remove('text-gray-600', 'hover:bg-gray-50');
                break;
            case 'visual':
                this.visualContentView.classList.add('active');
                this.visualContentView.classList.remove('hidden');
                this.visualView.classList.add('active', 'bg-white', 'shadow-sm');
                this.visualView.classList.remove('text-gray-600', 'hover:bg-gray-50');
                this.initializeVisualEditor();
                break;
            case 'advanced':
                this.advancedContentView.classList.add('active');
                this.advancedContentView.classList.remove('hidden');
                this.advancedView.classList.add('active', 'bg-white', 'shadow-sm');
                this.advancedView.classList.remove('text-gray-600', 'hover:bg-gray-50');
                this.initializeAdvancedEditor();
                break;
        }
    }

    initializeVisualEditor() {
        // Initialize drag and drop for content blocks
        const blockItems = document.querySelectorAll('.content-block-item');
        blockItems.forEach(item => {
            item.addEventListener('dragstart', (e) => this.handleBlockDragStart(e));
        });

        // Render existing visual blocks
        this.renderVisualBlocks();
    }

    initializeAdvancedEditor() {
        // Load saved content
        this.richTextEditor.innerHTML = this.businessData.contentEditor.richTextContent;

        // Apply saved styling
        const styling = this.businessData.contentEditor.customStyling;
        this.contentBgColor.value = styling.backgroundColor;
        this.contentTextColor.value = styling.textColor;
        this.contentFontSize.value = styling.fontSize;
        this.contentFontWeight.value = styling.fontWeight;

        this.applyAdvancedStyling();
    }

    handleBlockDragStart(e) {
        e.dataTransfer.setData('text/plain', e.target.dataset.type);
        e.target.classList.add('dragging');
    }

    handleCanvasDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
        this.contentCanvas.classList.add('drag-over');
    }

    handleCanvasDrop(e) {
        e.preventDefault();
        this.contentCanvas.classList.remove('drag-over');

        const blockType = e.dataTransfer.getData('text/plain');
        if (blockType) {
            this.addBlockToCanvas(blockType);
        }

        // Remove dragging class from all items
        document.querySelectorAll('.content-block-item').forEach(item => {
            item.classList.remove('dragging');
        });
    }

    addBlockToCanvas(blockType) {
        const blockId = Date.now() + Math.random();
        const blockData = this.createBlockData(blockType, blockId);

        this.businessData.contentEditor.visualBlocks.push(blockData);
        this.renderVisualBlocks();
    }

    createBlockData(type, id) {
        const baseData = {
            id: id,
            type: type,
            order: this.businessData.contentEditor.visualBlocks.length
        };

        switch (type) {
            case 'header':
                return {
                    ...baseData,
                    content: 'New Header',
                    level: 'h2',
                    alignment: 'left',
                    color: '#000000'
                };
            case 'text':
                return {
                    ...baseData,
                    content: 'Add your text content here...',
                    alignment: 'left',
                    fontSize: '14px',
                    color: '#000000'
                };
            case 'button':
                return {
                    ...baseData,
                    text: 'Click Here',
                    url: '#',
                    style: 'primary',
                    alignment: 'center'
                };
            case 'contact':
                return {
                    ...baseData,
                    phone: this.phone.value,
                    email: '',
                    address: this.address.value
                };
            case 'social':
                return {
                    ...baseData,
                    platforms: [
                        { name: 'Facebook', url: this.facebookUrl.value, icon: 'ri-facebook-fill' },
                        { name: 'Instagram', url: this.instagramUrl.value, icon: 'ri-instagram-line' }
                    ]
                };
            case 'spacer':
                return {
                    ...baseData,
                    height: '20px'
                };
            default:
                return baseData;
        }
    }

    renderVisualBlocks() {
        const canvas = this.contentCanvas;

        if (this.businessData.contentEditor.visualBlocks.length === 0) {
            canvas.innerHTML = `
                <div class="text-center text-gray-500 py-8">
                    <i class="ri-drag-drop-line text-3xl mb-2 block"></i>
                    <p>Drag content blocks here to build your page</p>
                </div>
            `;
            return;
        }

        canvas.innerHTML = '';

        // Sort blocks by order
        const sortedBlocks = [...this.businessData.contentEditor.visualBlocks].sort((a, b) => a.order - b.order);

        sortedBlocks.forEach(block => {
            const blockElement = this.createBlockElement(block);
            canvas.appendChild(blockElement);
        });
    }

    createBlockElement(block) {
        const blockDiv = document.createElement('div');
        blockDiv.className = 'canvas-block';
        blockDiv.dataset.blockId = block.id;

        let content = '';

        switch (block.type) {
            case 'header':
                content = `
                    <${block.level} style="text-align: ${block.alignment}; color: ${block.color}; margin: 0;">
                        ${block.content}
                    </${block.level}>
                `;
                break;
            case 'text':
                content = `
                    <p style="text-align: ${block.alignment}; font-size: ${block.fontSize}; color: ${block.color}; margin: 0;">
                        ${block.content}
                    </p>
                `;
                break;
            case 'button':
                const buttonClass = block.style === 'primary' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800';
                content = `
                    <div style="text-align: ${block.alignment};">
                        <a href="${block.url}" class="inline-block px-6 py-3 rounded-lg ${buttonClass} hover:opacity-80 transition-opacity">
                            ${block.text}
                        </a>
                    </div>
                `;
                break;
            case 'contact':
                content = `
                    <div class="space-y-2">
                        ${block.phone ? `<div><i class="ri-phone-line mr-2"></i>${block.phone}</div>` : ''}
                        ${block.email ? `<div><i class="ri-mail-line mr-2"></i>${block.email}</div>` : ''}
                        ${block.address ? `<div><i class="ri-map-pin-line mr-2"></i>${block.address}</div>` : ''}
                    </div>
                `;
                break;
            case 'social':
                content = `
                    <div class="flex space-x-3 justify-center">
                        ${block.platforms.map(platform =>
                            platform.url ? `<a href="${platform.url}" class="text-2xl text-blue-600 hover:text-blue-800"><i class="${platform.icon}"></i></a>` : ''
                        ).join('')}
                    </div>
                `;
                break;
            case 'spacer':
                content = `<div style="height: ${block.height};"></div>`;
                break;
        }

        blockDiv.innerHTML = `
            ${content}
            <div class="block-controls">
                <button class="block-control-btn" onclick="templateEditor.editBlock('${block.id}')" title="Edit">
                    <i class="ri-edit-line text-sm"></i>
                </button>
                <button class="block-control-btn" onclick="templateEditor.moveBlockUp('${block.id}')" title="Move Up">
                    <i class="ri-arrow-up-line text-sm"></i>
                </button>
                <button class="block-control-btn" onclick="templateEditor.moveBlockDown('${block.id}')" title="Move Down">
                    <i class="ri-arrow-down-line text-sm"></i>
                </button>
                <button class="block-control-btn" onclick="templateEditor.deleteBlock('${block.id}')" title="Delete">
                    <i class="ri-delete-bin-line text-sm text-red-500"></i>
                </button>
            </div>
        `;

        return blockDiv;
    }

    handleCanvasClick(e) {
        // Remove selection from all blocks
        document.querySelectorAll('.canvas-block').forEach(block => {
            block.classList.remove('selected');
        });

        // Add selection to clicked block
        const blockElement = e.target.closest('.canvas-block');
        if (blockElement) {
            blockElement.classList.add('selected');
            const blockId = blockElement.dataset.blockId;
            this.showBlockProperties(blockId);
        } else {
            this.hideBlockProperties();
        }
    }

    editBlock(blockId) {
        const block = this.businessData.contentEditor.visualBlocks.find(b => b.id == blockId);
        if (block) {
            this.businessData.contentEditor.selectedBlock = blockId;
            this.showBlockProperties(blockId);
        }
    }

    moveBlockUp(blockId) {
        const blocks = this.businessData.contentEditor.visualBlocks;
        const blockIndex = blocks.findIndex(b => b.id == blockId);

        if (blockIndex > 0) {
            // Swap orders
            const temp = blocks[blockIndex].order;
            blocks[blockIndex].order = blocks[blockIndex - 1].order;
            blocks[blockIndex - 1].order = temp;

            this.renderVisualBlocks();
        }
    }

    moveBlockDown(blockId) {
        const blocks = this.businessData.contentEditor.visualBlocks;
        const blockIndex = blocks.findIndex(b => b.id == blockId);

        if (blockIndex < blocks.length - 1) {
            // Swap orders
            const temp = blocks[blockIndex].order;
            blocks[blockIndex].order = blocks[blockIndex + 1].order;
            blocks[blockIndex + 1].order = temp;

            this.renderVisualBlocks();
        }
    }

    deleteBlock(blockId) {
        if (confirm('Are you sure you want to delete this block?')) {
            this.businessData.contentEditor.visualBlocks =
                this.businessData.contentEditor.visualBlocks.filter(b => b.id != blockId);
            this.renderVisualBlocks();
            this.hideBlockProperties();
        }
    }

    showBlockProperties(blockId) {
        const block = this.businessData.contentEditor.visualBlocks.find(b => b.id == blockId);
        if (!block) return;

        this.blockPropertiesPanel.classList.remove('hidden');
        this.blockPropertiesContent.innerHTML = this.generateBlockPropertiesForm(block);
    }

    hideBlockProperties() {
        this.blockPropertiesPanel.classList.add('hidden');
        this.businessData.contentEditor.selectedBlock = null;
    }

    generateBlockPropertiesForm(block) {
        let form = `<h4 class="font-medium mb-3 capitalize">${block.type} Properties</h4>`;

        switch (block.type) {
            case 'header':
                form += `
                    <div class="property-group">
                        <label class="property-label">Content</label>
                        <input type="text" class="property-input" value="${block.content}"
                               onchange="templateEditor.updateBlockProperty('${block.id}', 'content', this.value)">
                    </div>
                    <div class="property-group">
                        <label class="property-label">Level</label>
                        <select class="property-input" onchange="templateEditor.updateBlockProperty('${block.id}', 'level', this.value)">
                            <option value="h1" ${block.level === 'h1' ? 'selected' : ''}>H1</option>
                            <option value="h2" ${block.level === 'h2' ? 'selected' : ''}>H2</option>
                            <option value="h3" ${block.level === 'h3' ? 'selected' : ''}>H3</option>
                        </select>
                    </div>
                    <div class="property-group">
                        <label class="property-label">Alignment</label>
                        <select class="property-input" onchange="templateEditor.updateBlockProperty('${block.id}', 'alignment', this.value)">
                            <option value="left" ${block.alignment === 'left' ? 'selected' : ''}>Left</option>
                            <option value="center" ${block.alignment === 'center' ? 'selected' : ''}>Center</option>
                            <option value="right" ${block.alignment === 'right' ? 'selected' : ''}>Right</option>
                        </select>
                    </div>
                    <div class="property-group">
                        <label class="property-label">Color</label>
                        <input type="color" class="property-input" value="${block.color}"
                               onchange="templateEditor.updateBlockProperty('${block.id}', 'color', this.value)">
                    </div>
                `;
                break;
            case 'text':
                form += `
                    <div class="property-group">
                        <label class="property-label">Content</label>
                        <textarea class="property-input" rows="3"
                                  onchange="templateEditor.updateBlockProperty('${block.id}', 'content', this.value)">${block.content}</textarea>
                    </div>
                    <div class="property-group">
                        <label class="property-label">Font Size</label>
                        <select class="property-input" onchange="templateEditor.updateBlockProperty('${block.id}', 'fontSize', this.value)">
                            <option value="12px" ${block.fontSize === '12px' ? 'selected' : ''}>12px</option>
                            <option value="14px" ${block.fontSize === '14px' ? 'selected' : ''}>14px</option>
                            <option value="16px" ${block.fontSize === '16px' ? 'selected' : ''}>16px</option>
                            <option value="18px" ${block.fontSize === '18px' ? 'selected' : ''}>18px</option>
                        </select>
                    </div>
                    <div class="property-group">
                        <label class="property-label">Alignment</label>
                        <select class="property-input" onchange="templateEditor.updateBlockProperty('${block.id}', 'alignment', this.value)">
                            <option value="left" ${block.alignment === 'left' ? 'selected' : ''}>Left</option>
                            <option value="center" ${block.alignment === 'center' ? 'selected' : ''}>Center</option>
                            <option value="right" ${block.alignment === 'right' ? 'selected' : ''}>Right</option>
                        </select>
                    </div>
                `;
                break;
            case 'button':
                form += `
                    <div class="property-group">
                        <label class="property-label">Button Text</label>
                        <input type="text" class="property-input" value="${block.text}"
                               onchange="templateEditor.updateBlockProperty('${block.id}', 'text', this.value)">
                    </div>
                    <div class="property-group">
                        <label class="property-label">URL</label>
                        <input type="url" class="property-input" value="${block.url}"
                               onchange="templateEditor.updateBlockProperty('${block.id}', 'url', this.value)">
                    </div>
                    <div class="property-group">
                        <label class="property-label">Style</label>
                        <select class="property-input" onchange="templateEditor.updateBlockProperty('${block.id}', 'style', this.value)">
                            <option value="primary" ${block.style === 'primary' ? 'selected' : ''}>Primary</option>
                            <option value="secondary" ${block.style === 'secondary' ? 'selected' : ''}>Secondary</option>
                        </select>
                    </div>
                `;
                break;
            case 'spacer':
                form += `
                    <div class="property-group">
                        <label class="property-label">Height</label>
                        <select class="property-input" onchange="templateEditor.updateBlockProperty('${block.id}', 'height', this.value)">
                            <option value="10px" ${block.height === '10px' ? 'selected' : ''}>10px</option>
                            <option value="20px" ${block.height === '20px' ? 'selected' : ''}>20px</option>
                            <option value="30px" ${block.height === '30px' ? 'selected' : ''}>30px</option>
                            <option value="50px" ${block.height === '50px' ? 'selected' : ''}>50px</option>
                        </select>
                    </div>
                `;
                break;
        }

        return form;
    }

    updateBlockProperty(blockId, property, value) {
        const block = this.businessData.contentEditor.visualBlocks.find(b => b.id == blockId);
        if (block) {
            block[property] = value;
            this.renderVisualBlocks();

            // Re-select the block
            setTimeout(() => {
                const blockElement = document.querySelector(`[data-block-id="${blockId}"]`);
                if (blockElement) {
                    blockElement.classList.add('selected');
                }
            }, 100);
        }
    }

    // Advanced Editor Methods
    updateRichTextContent() {
        this.businessData.contentEditor.richTextContent = this.richTextEditor.innerHTML;
    }

    updateCustomStyling() {
        this.businessData.contentEditor.customStyling = {
            backgroundColor: this.contentBgColor.value,
            textColor: this.contentTextColor.value,
            fontSize: this.contentFontSize.value,
            fontWeight: this.contentFontWeight.value
        };
        this.applyAdvancedStyling();
    }

    applyAdvancedStyling() {
        const styling = this.businessData.contentEditor.customStyling;
        this.richTextEditor.style.backgroundColor = styling.backgroundColor;
        this.richTextEditor.style.color = styling.textColor;
        this.richTextEditor.style.fontSize = styling.fontSize;
        this.richTextEditor.style.fontWeight = styling.fontWeight;
    }

    executeEditorCommand(command) {
        document.execCommand(command, false, null);
        this.updateRichTextContent();

        // Update button states
        this.updateEditorButtonStates();
    }

    updateEditorButtonStates() {
        document.querySelectorAll('.editor-btn[data-command]').forEach(btn => {
            const command = btn.dataset.command;
            if (document.queryCommandState(command)) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }

    insertLinkDialog() {
        const url = prompt('Enter URL:');
        if (url) {
            document.execCommand('createLink', false, url);
            this.updateRichTextContent();
        }
    }

    insertImageDialog() {
        const url = prompt('Enter image URL:');
        if (url) {
            document.execCommand('insertImage', false, url);
            this.updateRichTextContent();
        }
    }

    // Content Templates Methods
    showContentTemplatesModal() {
        this.contentTemplatesModal.classList.remove('hidden');
        this.contentTemplatesModal.classList.add('flex');

        // Add click handlers for template cards
        document.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', () => {
                // Remove selection from all cards
                document.querySelectorAll('.template-card').forEach(c => c.classList.remove('border-blue-400', 'bg-blue-50'));
                // Select clicked card
                card.classList.add('border-blue-400', 'bg-blue-50');
                this.selectedContentTemplate = card.dataset.template;
            });
        });
    }

    hideContentTemplatesModal() {
        this.contentTemplatesModal.classList.add('hidden');
        this.contentTemplatesModal.classList.remove('flex');
    }

    applySelectedTemplate() {
        if (!this.selectedContentTemplate) {
            alert('Please select a template first');
            return;
        }

        const templateData = this.getContentTemplateData(this.selectedContentTemplate);

        // Apply to visual editor
        this.businessData.contentEditor.visualBlocks = templateData.blocks;
        this.renderVisualBlocks();

        // Switch to visual view
        this.switchContentView('visual');

        this.hideContentTemplatesModal();
        alert('Template applied successfully!');
    }

    getContentTemplateData(templateName) {
        const templates = {
            'business-card': {
                blocks: [
                    {
                        id: Date.now() + 1,
                        type: 'header',
                        content: this.businessName.value || 'Your Business Name',
                        level: 'h1',
                        alignment: 'center',
                        color: '#000000',
                        order: 0
                    },
                    {
                        id: Date.now() + 2,
                        type: 'contact',
                        phone: this.phone.value,
                        email: '',
                        address: this.address.value,
                        order: 1
                    },
                    {
                        id: Date.now() + 3,
                        type: 'social',
                        platforms: [
                            { name: 'Facebook', url: this.facebookUrl.value, icon: 'ri-facebook-fill' },
                            { name: 'Instagram', url: this.instagramUrl.value, icon: 'ri-instagram-line' }
                        ],
                        order: 2
                    }
                ]
            },
            'service-menu': {
                blocks: [
                    {
                        id: Date.now() + 1,
                        type: 'header',
                        content: 'Our Services',
                        level: 'h2',
                        alignment: 'center',
                        color: '#000000',
                        order: 0
                    },
                    {
                        id: Date.now() + 2,
                        type: 'text',
                        content: 'Professional nail care services with premium quality products.',
                        alignment: 'center',
                        fontSize: '16px',
                        color: '#666666',
                        order: 1
                    },
                    {
                        id: Date.now() + 3,
                        type: 'spacer',
                        height: '20px',
                        order: 2
                    },
                    {
                        id: Date.now() + 4,
                        type: 'button',
                        text: 'Book Appointment',
                        url: this.bookingUrl.value || '#',
                        style: 'primary',
                        alignment: 'center',
                        order: 3
                    }
                ]
            }
            // Add more templates as needed
        };

        return templates[templateName] || templates['business-card'];
    }

    showContentPresets() {
        // Quick presets for common content
        const presets = [
            'Contact Information',
            'Service Menu',
            'Social Media Links',
            'Call to Action',
            'Business Hours'
        ];

        const preset = prompt(`Choose a preset:\n${presets.map((p, i) => `${i + 1}. ${p}`).join('\n')}`);
        const index = parseInt(preset) - 1;

        if (index >= 0 && index < presets.length) {
            this.applyContentPreset(presets[index]);
        }
    }

    applyContentPreset(presetName) {
        let blocks = [];

        switch (presetName) {
            case 'Contact Information':
                blocks = [
                    {
                        id: Date.now() + 1,
                        type: 'header',
                        content: 'Contact Us',
                        level: 'h3',
                        alignment: 'left',
                        color: '#000000',
                        order: this.businessData.contentEditor.visualBlocks.length
                    },
                    {
                        id: Date.now() + 2,
                        type: 'contact',
                        phone: this.phone.value,
                        email: '',
                        address: this.address.value,
                        order: this.businessData.contentEditor.visualBlocks.length + 1
                    }
                ];
                break;
            case 'Social Media Links':
                blocks = [
                    {
                        id: Date.now() + 1,
                        type: 'header',
                        content: 'Follow Us',
                        level: 'h3',
                        alignment: 'center',
                        color: '#000000',
                        order: this.businessData.contentEditor.visualBlocks.length
                    },
                    {
                        id: Date.now() + 2,
                        type: 'social',
                        platforms: [
                            { name: 'Facebook', url: this.facebookUrl.value, icon: 'ri-facebook-fill' },
                            { name: 'Instagram', url: this.instagramUrl.value, icon: 'ri-instagram-line' }
                        ],
                        order: this.businessData.contentEditor.visualBlocks.length + 1
                    }
                ];
                break;
            // Add more presets as needed
        }

        // Add blocks to existing content
        this.businessData.contentEditor.visualBlocks.push(...blocks);
        this.renderVisualBlocks();
        this.switchContentView('visual');
    }

    // Icon Selector Methods
    showIconSelector() {
        this.iconSelectorModal.classList.remove('hidden');
        this.iconSelectorModal.classList.add('flex');
        this.loadIcons();
    }

    hideIconSelector() {
        this.iconSelectorModal.classList.add('hidden');
        this.iconSelectorModal.classList.remove('flex');
    }

    loadIcons() {
        // Popular RemixIcon icons organized by category
        const iconCategories = {
            business: ['ri-briefcase-line', 'ri-building-line', 'ri-store-line', 'ri-service-line', 'ri-customer-service-line'],
            communication: ['ri-phone-line', 'ri-mail-line', 'ri-message-line', 'ri-chat-line', 'ri-notification-line'],
            social: ['ri-facebook-fill', 'ri-instagram-line', 'ri-twitter-fill', 'ri-linkedin-fill', 'ri-youtube-fill'],
            arrows: ['ri-arrow-right-line', 'ri-arrow-left-line', 'ri-arrow-up-line', 'ri-arrow-down-line', 'ri-external-link-line'],
            media: ['ri-image-line', 'ri-video-line', 'ri-music-line', 'ri-camera-line', 'ri-gallery-line']
        };

        this.allIcons = Object.values(iconCategories).flat();
        this.renderIcons(this.allIcons);

        // Add category filter handlers
        document.querySelectorAll('.icon-category-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // Update button states
                document.querySelectorAll('.icon-category-btn').forEach(b => {
                    b.classList.remove('active', 'bg-blue-500', 'text-white');
                    b.classList.add('bg-gray-200', 'text-gray-700');
                });
                btn.classList.add('active', 'bg-blue-500', 'text-white');
                btn.classList.remove('bg-gray-200', 'text-gray-700');

                // Filter icons
                const category = btn.dataset.category;
                const iconsToShow = category === 'all' ? this.allIcons : iconCategories[category] || [];
                this.renderIcons(iconsToShow);
            });
        });
    }

    renderIcons(icons) {
        this.iconGrid.innerHTML = '';

        icons.forEach(iconClass => {
            const iconDiv = document.createElement('div');
            iconDiv.className = 'icon-item p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors text-center';
            iconDiv.innerHTML = `<i class="${iconClass} text-xl"></i>`;
            iconDiv.dataset.icon = iconClass;

            iconDiv.addEventListener('click', () => {
                // Remove selection from all icons
                document.querySelectorAll('.icon-item').forEach(item => {
                    item.classList.remove('border-blue-400', 'bg-blue-50');
                });
                // Select clicked icon
                iconDiv.classList.add('border-blue-400', 'bg-blue-50');
                this.selectedIcon = iconClass;
            });

            this.iconGrid.appendChild(iconDiv);
        });
    }

    filterIcons() {
        const searchTerm = this.iconSearch.value.toLowerCase();
        const filteredIcons = this.allIcons.filter(icon =>
            icon.toLowerCase().includes(searchTerm)
        );
        this.renderIcons(filteredIcons);
    }

    insertSelectedIconToEditor() {
        if (!this.selectedIcon) {
            alert('Please select an icon first');
            return;
        }

        const iconHtml = `<i class="${this.selectedIcon}"></i>`;
        document.execCommand('insertHTML', false, iconHtml);
        this.updateRichTextContent();
        this.hideIconSelector();
    }

    // Enhanced Basic View UX Methods
    initializeEnhancedBasicView() {
        // Initialize tab navigation
        this.initializeBasicTabs();

        // Initialize smart inputs
        this.initializeSmartInputs();

        // Initialize progress tracking
        this.initializeProgressTracking();

        // Initialize quick actions
        this.initializeQuickActions();
    }

    initializeBasicTabs() {
        // Tab click handlers
        document.querySelectorAll('.basic-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.target.dataset.tab;
                this.switchBasicTab(tabName);
            });
        });

        // Next/Previous tab handlers
        document.querySelectorAll('.next-tab-btn').forEach(btn => {
            btn.addEventListener('click', () => this.goToNextTab());
        });

        document.querySelectorAll('.prev-tab-btn').forEach(btn => {
            btn.addEventListener('click', () => this.goToPreviousTab());
        });
    }

    switchBasicTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.basic-tab').forEach(tab => {
            tab.classList.remove('active', 'border-blue-500', 'text-blue-600');
            tab.classList.add('border-transparent', 'text-gray-500');
        });

        // Update tab content
        document.querySelectorAll('.basic-tab-content').forEach(content => {
            content.classList.remove('active');
            content.classList.add('hidden');
        });

        // Activate selected tab
        const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
        const activeContent = document.getElementById(`${tabName}-tab`);

        if (activeTab && activeContent) {
            activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            activeTab.classList.remove('border-transparent', 'text-gray-500');

            activeContent.classList.add('active');
            activeContent.classList.remove('hidden');
        }

        // Update progress
        this.updateTabProgress(tabName);

        // Store current tab
        this.businessData.contentEditor.currentBasicTab = tabName;
    }

    goToNextTab() {
        const tabs = ['basic-info', 'media', 'contact', 'business', 'social', 'design', 'advanced'];
        const currentTab = this.businessData.contentEditor.currentBasicTab || 'basic-info';
        const currentIndex = tabs.indexOf(currentTab);

        if (currentIndex < tabs.length - 1) {
            this.switchBasicTab(tabs[currentIndex + 1]);
        }
    }

    goToPreviousTab() {
        const tabs = ['basic-info', 'media', 'contact', 'business', 'social', 'design', 'advanced'];
        const currentTab = this.businessData.contentEditor.currentBasicTab || 'basic-info';
        const currentIndex = tabs.indexOf(currentTab);

        if (currentIndex > 0) {
            this.switchBasicTab(tabs[currentIndex - 1]);
        }
    }

    initializeSmartInputs() {
        // Smart input validation and feedback
        document.querySelectorAll('.smart-input').forEach(input => {
            // Real-time validation
            input.addEventListener('input', (e) => this.validateSmartInput(e.target));
            input.addEventListener('blur', (e) => this.validateSmartInput(e.target));

            // Enhanced focus effects
            input.addEventListener('focus', (e) => {
                e.target.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', (e) => {
                e.target.parentElement.classList.remove('focused');
            });
        });

        // Auto-format phone numbers
        document.querySelectorAll('.format-phone-btn').forEach(btn => {
            btn.addEventListener('click', () => this.autoFormatPhone());
        });

        // Auto-detect favicon
        document.querySelectorAll('.auto-detect-btn').forEach(btn => {
            btn.addEventListener('click', () => this.autoDetectFavicon());
        });
    }

    validateSmartInput(input) {
        const value = input.value.trim();
        const isRequired = input.dataset.required === 'true';
        const type = input.type;

        let isValid = true;
        let message = '';
        let messageType = 'success';

        // Required field validation
        if (isRequired && !value) {
            isValid = false;
            message = 'This field is required';
            messageType = 'error';
        }
        // Email validation
        else if (type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
            message = 'Please enter a valid email address';
            messageType = 'error';
        }
        // URL validation
        else if (type === 'url' && value && !this.isValidUrl(value)) {
            isValid = false;
            message = 'Please enter a valid URL';
            messageType = 'error';
        }
        // Phone validation
        else if (type === 'tel' && value && !this.isValidPhone(value)) {
            isValid = false;
            message = 'Please enter a valid phone number';
            messageType = 'error';
        }
        // Success messages
        else if (value) {
            message = 'Looks good!';
            messageType = 'success';
        }

        // Update input styling
        input.classList.remove('valid', 'invalid');
        if (value) {
            input.classList.add(isValid ? 'valid' : 'invalid');
        }

        // Update validation icon
        const formGroup = input.closest('.form-group');
        const validationIcon = formGroup?.querySelector('.validation-icon');
        if (validationIcon) {
            validationIcon.classList.remove('show');
            if (value && isValid) {
                validationIcon.classList.add('show');
            }
        }

        // Update feedback message
        const feedback = formGroup?.querySelector('.input-feedback');
        if (feedback && message) {
            feedback.textContent = message;
            feedback.className = `input-feedback show ${messageType}`;
        } else if (feedback) {
            feedback.classList.remove('show');
        }

        return isValid;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
        return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
    }

    autoFormatPhone() {
        const phoneInput = document.getElementById('phone');
        let phone = phoneInput.value.replace(/\D/g, '');

        if (phone.length === 10) {
            phone = `(${phone.slice(0,3)}) ${phone.slice(3,6)}-${phone.slice(6)}`;
        } else if (phone.length === 11 && phone[0] === '1') {
            phone = `+1 (${phone.slice(1,4)}) ${phone.slice(4,7)}-${phone.slice(7)}`;
        }

        phoneInput.value = phone;
        this.validateSmartInput(phoneInput);
    }

    autoDetectFavicon() {
        const websiteUrl = this.websiteUrl.value;
        if (websiteUrl) {
            try {
                const url = new URL(websiteUrl);
                const faviconUrl = `${url.origin}/favicon.ico`;
                this.faviconUrl.value = faviconUrl;
                this.validateSmartInput(this.faviconUrl);
            } catch (error) {
                alert('Please enter a valid website URL first');
            }
        } else {
            alert('Please enter a website URL first');
        }
    }

    initializeProgressTracking() {
        // Track completion of required fields
        this.updateProgress();

        // Update progress when inputs change
        document.querySelectorAll('.smart-input').forEach(input => {
            input.addEventListener('input', () => {
                setTimeout(() => this.updateProgress(), 100);
            });
        });
    }

    updateProgress() {
        const requiredFields = document.querySelectorAll('.smart-input[data-required="true"]');
        const completedFields = Array.from(requiredFields).filter(field =>
            field.value.trim() && this.validateSmartInput(field)
        );

        const progress = Math.round((completedFields.length / requiredFields.length) * 100);
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }

        if (progressText) {
            progressText.textContent = `${completedFields.length}/${requiredFields.length} Complete`;
        }
    }

    updateTabProgress(tabName) {
        // Update progress based on current tab
        const tabProgress = {
            'basic-info': 28,
            'media': 42,
            'contact': 57,
            'business': 71,
            'social': 85,
            'design': 92,
            'advanced': 100
        };

        const progress = tabProgress[tabName] || 0;
        const progressBar = document.getElementById('progressBar');

        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
    }

    initializeQuickActions() {
        // Template suggestions
        document.querySelectorAll('.template-suggestion-btn').forEach(btn => {
            btn.addEventListener('click', () => this.showTemplateSuggestions());
        });

        // Import data
        document.querySelectorAll('.import-data-btn').forEach(btn => {
            btn.addEventListener('click', () => this.showImportDataOptions());
        });

        // Media optimization
        document.querySelectorAll('.optimize-images-btn').forEach(btn => {
            btn.addEventListener('click', () => this.optimizeAllImages());
        });
    }

    showTemplateSuggestions() {
        const suggestions = [
            'Nail Salon Template',
            'Restaurant Template',
            'Beauty Salon Template',
            'Retail Store Template',
            'Service Business Template'
        ];

        const choice = prompt(`Choose a template:\n${suggestions.map((s, i) => `${i + 1}. ${s}`).join('\n')}`);
        const index = parseInt(choice) - 1;

        if (index >= 0 && index < suggestions.length) {
            this.applyBusinessTemplate(suggestions[index]);
        }
    }

    showImportDataOptions() {
        const options = [
            'Import from Google My Business',
            'Import from Facebook Page',
            'Import from CSV File',
            'Import from vCard',
            'Import from Website'
        ];

        const choice = prompt(`Choose import source:\n${options.map((o, i) => `${i + 1}. ${o}`).join('\n')}`);
        const index = parseInt(choice) - 1;

        if (index >= 0 && index < options.length) {
            alert(`${options[index]} import feature coming soon!`);
        }
    }

    applyBusinessTemplate(templateName) {
        // Apply template data based on selection
        switch (templateName) {
            case 'Nail Salon Template':
                this.businessName.value = 'Your Nail Salon';
                this.phone.value = '(*************';
                this.address.value = '123 Beauty Street\nYour City, State 12345';
                this.hours.value = 'Mon-Fri: 9:00 AM - 7:00 PM\nSat: 9:00 AM - 6:00 PM\nSun: 11:00 AM - 5:00 PM';
                break;
            // Add more templates as needed
        }

        // Validate all inputs after applying template
        document.querySelectorAll('.smart-input').forEach(input => {
            this.validateSmartInput(input);
        });

        this.updateProgress();
        alert(`${templateName} applied successfully!`);
    }

    optimizeAllImages() {
        // Placeholder for image optimization
        alert('Image optimization feature coming soon!');
    }

    // Data Management Methods
    saveBusinessProfile() {
        const profileName = this.profileName.value.trim();
        if (!profileName) {
            alert('Please enter a profile name');
            return;
        }

        const profileData = {
            id: Date.now(),
            name: profileName,
            data: this.getFormData(),
            businessData: JSON.parse(JSON.stringify(this.businessData)),
            savedAt: new Date().toISOString()
        };

        // Save to localStorage
        const savedProfiles = JSON.parse(localStorage.getItem('qr-page-profiles') || '[]');
        savedProfiles.push(profileData);
        localStorage.setItem('qr-page-profiles', JSON.stringify(savedProfiles));

        // Update UI
        this.businessData.dataManagement.savedProfiles = savedProfiles;
        this.renderSavedProfiles();
        this.profileName.value = '';

        // Add to recent projects
        this.addToRecentProjects(profileName);
    }

    renderSavedProfiles() {
        const savedProfiles = JSON.parse(localStorage.getItem('qr-page-profiles') || '[]');
        this.savedProfiles.innerHTML = '';

        savedProfiles.forEach(profile => {
            const profileDiv = document.createElement('div');
            profileDiv.className = 'flex items-center justify-between p-2 bg-white border border-gray-200 rounded';
            profileDiv.innerHTML = `
                <div class="flex-1">
                    <div class="font-medium text-sm">${profile.name}</div>
                    <div class="text-xs text-gray-500">${new Date(profile.savedAt).toLocaleDateString()}</div>
                </div>
                <div class="flex space-x-1">
                    <button onclick="templateEditor.loadBusinessProfile(${profile.id})"
                            class="text-blue-600 hover:text-blue-800 p-1" title="Load">
                        <i class="ri-download-line text-sm"></i>
                    </button>
                    <button onclick="templateEditor.deleteBusinessProfile(${profile.id})"
                            class="text-red-600 hover:text-red-800 p-1" title="Delete">
                        <i class="ri-delete-bin-line text-sm"></i>
                    </button>
                </div>
            `;
            this.savedProfiles.appendChild(profileDiv);
        });
    }

    loadBusinessProfile(profileId) {
        const savedProfiles = JSON.parse(localStorage.getItem('qr-page-profiles') || '[]');
        const profile = savedProfiles.find(p => p.id === profileId);

        if (profile) {
            // Load form data
            Object.keys(profile.data).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = profile.data[key];
                    } else {
                        element.value = profile.data[key];
                    }
                }
            });

            // Load business data
            this.businessData = { ...this.businessData, ...profile.businessData };

            // Re-render components
            this.initializeBusinessHours();
            this.renderServices();
            this.renderOffers();
            this.renderGalleryPreview();
            this.initializeSectionToggles();
            this.initializeSectionOrder();

            this.updatePreview();
            this.addToRecentProjects(profile.name);
        }
    }

    deleteBusinessProfile(profileId) {
        if (confirm('Are you sure you want to delete this profile?')) {
            const savedProfiles = JSON.parse(localStorage.getItem('qr-page-profiles') || '[]');
            const filteredProfiles = savedProfiles.filter(p => p.id !== profileId);
            localStorage.setItem('qr-page-profiles', JSON.stringify(filteredProfiles));
            this.renderSavedProfiles();
        }
    }

    addToRecentProjects(projectName) {
        const recentProjects = JSON.parse(localStorage.getItem('qr-page-recent') || '[]');
        const projectData = {
            name: projectName,
            timestamp: new Date().toISOString()
        };

        // Remove if already exists
        const filtered = recentProjects.filter(p => p.name !== projectName);

        // Add to beginning
        filtered.unshift(projectData);

        // Keep only last 10
        const limited = filtered.slice(0, 10);

        localStorage.setItem('qr-page-recent', JSON.stringify(limited));
        this.renderRecentProjects();
    }

    renderRecentProjects() {
        const recentProjects = JSON.parse(localStorage.getItem('qr-page-recent') || '[]');
        this.recentProjects.innerHTML = '';

        recentProjects.forEach(project => {
            const projectDiv = document.createElement('div');
            projectDiv.className = 'flex items-center justify-between p-2 bg-white border border-gray-200 rounded';
            projectDiv.innerHTML = `
                <div class="flex-1">
                    <div class="font-medium text-sm">${project.name}</div>
                    <div class="text-xs text-gray-500">${new Date(project.timestamp).toLocaleString()}</div>
                </div>
                <button onclick="templateEditor.loadRecentProject('${project.name}')"
                        class="text-blue-600 hover:text-blue-800 p-1" title="Load">
                    <i class="ri-download-line text-sm"></i>
                </button>
            `;
            this.recentProjects.appendChild(projectDiv);
        });
    }

    loadRecentProject(projectName) {
        const savedProfiles = JSON.parse(localStorage.getItem('qr-page-profiles') || '[]');
        const profile = savedProfiles.find(p => p.name === projectName);

        if (profile) {
            this.loadBusinessProfile(profile.id);
        }
    }

    clearRecentProjects() {
        if (confirm('Clear all recent projects?')) {
            localStorage.removeItem('qr-page-recent');
            this.renderRecentProjects();
        }
    }

    exportBackupData() {
        const backupData = {
            version: '1.0',
            exportedAt: new Date().toISOString(),
            profiles: JSON.parse(localStorage.getItem('qr-page-profiles') || '[]'),
            recent: JSON.parse(localStorage.getItem('qr-page-recent') || '[]'),
            currentData: {
                formData: this.getFormData(),
                businessData: this.businessData
            }
        };

        const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
        const filename = `qr-page-backup-${new Date().toISOString().split('T')[0]}.json`;
        saveAs(blob, filename);
    }

    importBackupData(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const backupData = JSON.parse(e.target.result);

                if (confirm('This will replace all current data. Continue?')) {
                    // Restore profiles
                    if (backupData.profiles) {
                        localStorage.setItem('qr-page-profiles', JSON.stringify(backupData.profiles));
                    }

                    // Restore recent projects
                    if (backupData.recent) {
                        localStorage.setItem('qr-page-recent', JSON.stringify(backupData.recent));
                    }

                    // Restore current data if available
                    if (backupData.currentData) {
                        // Load form data
                        Object.keys(backupData.currentData.formData).forEach(key => {
                            const element = document.getElementById(key);
                            if (element) {
                                if (element.type === 'checkbox') {
                                    element.checked = backupData.currentData.formData[key];
                                } else {
                                    element.value = backupData.currentData.formData[key];
                                }
                            }
                        });

                        // Load business data
                        this.businessData = { ...this.businessData, ...backupData.currentData.businessData };
                    }

                    // Re-render everything
                    this.renderSavedProfiles();
                    this.renderRecentProjects();
                    this.initializeBusinessHours();
                    this.renderServices();
                    this.renderOffers();
                    this.renderGalleryPreview();
                    this.initializeSectionToggles();
                    this.initializeSectionOrder();
                    this.updatePreview();

                    alert('Backup restored successfully!');
                }
            } catch (error) {
                console.error('Error importing backup:', error);
                alert('Error importing backup file. Please check the file format.');
            }
        };
        reader.readAsText(file);

        // Reset file input
        event.target.value = '';
    }

    resetAllData() {
        if (confirm('This will reset all data including saved profiles. This cannot be undone. Continue?')) {
            // Clear localStorage
            localStorage.removeItem('qr-page-profiles');
            localStorage.removeItem('qr-page-recent');

            // Reset form
            document.querySelectorAll('input, textarea, select').forEach(element => {
                if (element.type === 'checkbox') {
                    element.checked = false;
                } else if (element.type === 'color') {
                    element.value = '#000000';
                } else {
                    element.value = '';
                }
            });

            // Reset business data to defaults
            this.businessData = {
                gallery: [],
                operatingHours: {
                    monday: { open: '09:00', close: '20:00', closed: false },
                    tuesday: { open: '09:00', close: '20:00', closed: false },
                    wednesday: { open: '09:00', close: '20:00', closed: false },
                    thursday: { open: '09:00', close: '20:00', closed: false },
                    friday: { open: '09:00', close: '20:00', closed: false },
                    saturday: { open: '09:00', close: '18:00', closed: false },
                    sunday: { open: '11:00', close: '17:00', closed: false }
                },
                services: [],
                offers: [],
                useSimpleHoursFormat: true,
                layout: {
                    style: 'card',
                    sections: {
                        social: { visible: true, order: 1 },
                        services: { visible: true, order: 2 },
                        offers: { visible: true, order: 3 },
                        gallery: { visible: true, order: 4 },
                        contact: { visible: true, order: 5 }
                    },
                    responsive: {
                        mobileOptimized: true,
                        showOnDesktop: true,
                        compactMode: false
                    }
                },
                preview: {
                    currentDevice: 'mobile',
                    autoRefresh: true,
                    showGrid: false,
                    shareUrl: null,
                    lastUpdate: null
                },
                dataManagement: {
                    savedProfiles: [],
                    recentProjects: [],
                    bulkBusinesses: []
                }
            };

            // Re-render everything
            this.renderSavedProfiles();
            this.renderRecentProjects();
            this.initializeBusinessHours();
            this.renderServices();
            this.renderOffers();
            this.renderGalleryPreview();
            this.initializeSectionToggles();
            this.initializeSectionOrder();
            this.updatePreview();

            alert('All data has been reset.');
        }
    }

    // Bulk Edit Methods
    showBulkEditModal() {
        this.bulkEditModal.classList.remove('hidden');
        this.bulkEditModal.classList.add('flex');
        this.renderBulkBusinessList();
    }

    hideBulkEditModal() {
        this.bulkEditModal.classList.add('hidden');
        this.bulkEditModal.classList.remove('flex');
    }

    renderBulkBusinessList() {
        this.bulkBusinessList.innerHTML = '';

        this.businessData.dataManagement.bulkBusinesses.forEach((business, index) => {
            const businessDiv = document.createElement('div');
            businessDiv.className = 'p-3 bg-gray-50 rounded-lg border';
            businessDiv.innerHTML = `
                <div class="flex items-center justify-between mb-2">
                    <input type="text" value="${business.businessName || ''}"
                           onchange="templateEditor.updateBulkBusiness(${index}, 'businessName', this.value)"
                           placeholder="Business Name"
                           class="font-medium bg-transparent border border-gray-300 rounded px-2 py-1 flex-1 mr-2">
                    <button onclick="templateEditor.removeBulkBusiness(${index})"
                            class="text-red-500 hover:text-red-700 p-1">
                        <i class="ri-delete-bin-line"></i>
                    </button>
                </div>
                <div class="grid grid-cols-2 gap-2 text-sm">
                    <input type="tel" value="${business.phone || ''}"
                           onchange="templateEditor.updateBulkBusiness(${index}, 'phone', this.value)"
                           placeholder="Phone" class="border border-gray-300 rounded px-2 py-1">
                    <input type="url" value="${business.websiteUrl || ''}"
                           onchange="templateEditor.updateBulkBusiness(${index}, 'websiteUrl', this.value)"
                           placeholder="Website" class="border border-gray-300 rounded px-2 py-1">
                    <input type="url" value="${business.facebookUrl || ''}"
                           onchange="templateEditor.updateBulkBusiness(${index}, 'facebookUrl', this.value)"
                           placeholder="Facebook" class="border border-gray-300 rounded px-2 py-1">
                    <input type="url" value="${business.instagramUrl || ''}"
                           onchange="templateEditor.updateBulkBusiness(${index}, 'instagramUrl', this.value)"
                           placeholder="Instagram" class="border border-gray-300 rounded px-2 py-1">
                </div>
                <textarea onchange="templateEditor.updateBulkBusiness(${index}, 'address', this.value)"
                          placeholder="Address" rows="2"
                          class="w-full mt-2 border border-gray-300 rounded px-2 py-1 text-sm resize-none">${business.address || ''}</textarea>
            `;
            this.bulkBusinessList.appendChild(businessDiv);
        });
    }

    addBulkBusiness() {
        const newBusiness = {
            businessName: '',
            phone: '',
            address: '',
            websiteUrl: '',
            facebookUrl: '',
            instagramUrl: ''
        };
        this.businessData.dataManagement.bulkBusinesses.push(newBusiness);
        this.renderBulkBusinessList();
    }

    removeBulkBusiness(index) {
        this.businessData.dataManagement.bulkBusinesses.splice(index, 1);
        this.renderBulkBusinessList();
    }

    updateBulkBusiness(index, field, value) {
        if (this.businessData.dataManagement.bulkBusinesses[index]) {
            this.businessData.dataManagement.bulkBusinesses[index][field] = value;
        }
    }

    loadBusinessesFromProfiles() {
        const savedProfiles = JSON.parse(localStorage.getItem('qr-page-profiles') || '[]');

        savedProfiles.forEach(profile => {
            const businessData = {
                businessName: profile.data.businessName || profile.name,
                phone: profile.data.phone || '',
                address: profile.data.address || '',
                websiteUrl: profile.data.websiteUrl || '',
                facebookUrl: profile.data.facebookUrl || '',
                instagramUrl: profile.data.instagramUrl || ''
            };
            this.businessData.dataManagement.bulkBusinesses.push(businessData);
        });

        this.renderBulkBusinessList();
    }

    applyBulkChangesToAll() {
        const template = this.bulkTemplate.value;
        const primaryColor = this.bulkPrimaryColor.value;
        const logoUrl = this.bulkLogoUrl.value;
        const websiteDomain = this.bulkWebsiteDomain.value;

        this.businessData.dataManagement.bulkBusinesses.forEach(business => {
            if (template) {
                business.template = template;
            }
            if (primaryColor) {
                business.primaryColor = primaryColor;
            }
            if (logoUrl) {
                business.logoUrl = logoUrl;
            }
            if (websiteDomain && !business.websiteUrl) {
                business.websiteUrl = `https://${websiteDomain}`;
            }
        });

        this.renderBulkBusinessList();
        alert('Bulk changes applied to all businesses!');
    }

    async exportBulkBusinesses() {
        try {
            if (this.businessData.dataManagement.bulkBusinesses.length === 0) {
                alert('No businesses to export');
                return;
            }

            const JSZip = window.JSZip || await this.loadJSZip();
            const zip = new JSZip();

            // Generate HTML for each business
            this.businessData.dataManagement.bulkBusinesses.forEach((business) => {
                const mergedData = {
                    ...this.getFormData(),
                    ...business,
                    pageTitle: business.pageTitle || `${business.businessName} - QR Page`,
                    logoUrl: business.logoUrl || this.logoUrl.value,
                    faviconUrl: business.faviconUrl || this.faviconUrl.value
                };

                const html = this.generateHTML(mergedData);
                const filename = `${business.businessName.toLowerCase().replace(/\s+/g, '-')}-qr-page.html`;
                zip.file(filename, html);
            });

            // Generate and download zip
            const content = await zip.generateAsync({ type: 'blob' });
            saveAs(content, 'bulk-qr-pages.zip');

        } catch (error) {
            console.error('Bulk export error:', error);
            alert('Error in bulk export.');
        }
    }

    generateHTML(customData = null) {
        const data = customData || this.getFormData();
        const templateClass = this.getTemplateClass(data.template || this.selectedTemplate);

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${data.pageTitle}</title>
    <link rel="icon" type="image/webp" href="${data.faviconUrl}">
    <link rel="shortcut icon" type="image/webp" href="${data.faviconUrl}">
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config = { theme: { extend: { colors: { primary: '${data.primaryColor}', secondary: '${data.secondaryColor}' }, borderRadius: { 'none': '0px', 'sm': '4px', DEFAULT: '8px', 'md': '12px', 'lg': '16px', 'xl': '20px', '2xl': '24px', '3xl': '32px', 'full': '9999px', 'button': '8px' } } } }</script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before {
            content: "\\f3c2";
        }

        body {
            font-family: 'Playfair Display', serif;
            background-color: #f9f5eb;
        }

        .gold-gradient {
            background: linear-gradient(135deg, ${data.primaryColor} 0%, #f9e076 50%, ${data.primaryColor} 100%);
        }

        /* Modern Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .animate-slideInLeft {
            animation: slideInLeft 0.6s ease-out forwards;
        }

        .animate-pulse-slow {
            animation: pulse 2s ease-in-out infinite;
        }

        .shimmer {
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            background-size: 200px 100%;
            animation: shimmer 2s infinite;
        }

        .hover-scale {
            transition: all 0.3s ease;
        }

        .hover-scale:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stagger-animation > * {
            opacity: 0;
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
        .stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
        .stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
        .stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }

        /* Template Specific Styles */
        ${this.getTemplateStyles(data.template || this.selectedTemplate)}

        /* Custom CSS */
        ${data.enableCustomCSS && data.customCSS ? data.customCSS : ''}
    </style>
</head>

<body class="bg-white">
    <!-- Main Content -->
    <main>
        <!-- Banner -->
        <div class="relative w-full h-48 bg-secondary overflow-hidden">
            <div class="w-full h-full gold-gradient shimmer"></div>
            <div class="absolute inset-0 bg-black bg-opacity-40 flex flex-col items-center justify-center">
                <img src="${data.logoUrl}" alt="${data.businessName} Logo" class="h-16 mb-1 animate-pulse-slow">
                <h1 class="text-white text-xl font-semibold tracking-wider animate-fadeInUp">${data.businessName}</h1>
            </div>
        </div>

        <!-- Dynamic Sections Based on Layout Settings -->
        ${this.generateDynamicSections(data)}



        <!-- Action Buttons -->
        <div class="px-5 py-6 bg-gray-50">
            <h2 class="text-2xl font-semibold text-secondary mb-6 animate-slideInLeft">Quick Actions</h2>
            <div class="stagger-animation">
                <!-- Visit Website -->
                <a href="${data.websiteUrl}" target="_blank" class="flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4">
                        <i class="ri-global-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Visit our website</h3>
                        <p class="text-sm text-gray-500">Learn more about us</p>
                    </div>
                </a>

                <!-- Book Appointment -->
                <a href="${data.bookingUrl}" target="_blank" class="flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4">
                        <i class="ri-calendar-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Book an appointment</h3>
                        <p class="text-sm text-gray-500">Online booking available</p>
                    </div>
                </a>

                <!-- Call Now -->
                <a href="tel:${data.phone.replace(/[^\d+]/g, '')}" class="flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4">
                        <i class="ri-phone-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Call us now</h3>
                        <p class="text-sm text-gray-500">${data.phone}</p>
                    </div>
                </a>
            </div>
        </div>
    </main>
</body>
</html>`;
    }

    getFormData() {
        return {
            pageTitle: this.pageTitle.value,
            businessName: this.businessName.value,
            logoUrl: this.logoUrl.value,
            faviconUrl: this.faviconUrl.value,
            phone: this.phone.value,
            address: this.address.value,
            hours: this.hours.value,
            websiteUrl: this.websiteUrl.value,
            bookingUrl: this.bookingUrl.value,
            facebookUrl: this.facebookUrl.value,
            instagramUrl: this.instagramUrl.value,
            googleMapsUrl: this.googleMapsUrl.value,
            primaryColor: this.primaryColor.value,
            secondaryColor: this.secondaryColor.value,
            customCSS: this.customCSS.value,
            enableCustomCSS: this.enableCustomCSS.checked,
            template: this.selectedTemplate
        };
    }

    getTemplateClass(template) {
        const classes = {
            modern: 'template-modern',
            luxury: 'template-luxury',
            minimal: 'template-minimal',
            cute: 'template-cute'
        };
        return classes[template] || 'template-modern';
    }

    getTemplateStyles(template) {
        const styles = {
            modern: `
                .template-modern .gold-gradient {
                    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 50%, #3B82F6 100%);
                }
                .template-modern .hover-scale:hover {
                    transform: scale(1.05) rotate(1deg);
                }
            `,
            luxury: `
                .template-luxury .gold-gradient {
                    background: linear-gradient(135deg, #D4AF37 0%, #F7E98E 50%, #D4AF37 100%);
                }
                .template-luxury body {
                    background: linear-gradient(45deg, #FFF8DC, #F5F5DC);
                }
            `,
            minimal: `
                .template-minimal .gold-gradient {
                    background: linear-gradient(135deg, #6B7280 0%, #9CA3AF 50%, #6B7280 100%);
                }
                .template-minimal .hover-scale:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                }
            `,
            cute: `
                .template-cute .gold-gradient {
                    background: linear-gradient(135deg, #EC4899 0%, #F472B6 50%, #EC4899 100%);
                }
                .template-cute .hover-scale:hover {
                    transform: scale(1.1);
                    border-radius: 20px;
                }
            `
        };
        return styles[template] || styles.modern;
    }

    exportHTML() {
        const html = this.generateHTML();
        const blob = new Blob([html], { type: 'text/html' });
        const filename = `${this.businessName.value.toLowerCase().replace(/\s+/g, '-')}-qr-page.html`;
        saveAs(blob, filename);
    }

    async batchExport() {
        try {
            const businessData = JSON.parse(this.businessList.value);

            if (!Array.isArray(businessData) || businessData.length === 0) {
                alert('Please enter valid JSON array of businesses');
                return;
            }

            // Create a zip file using JSZip
            const JSZip = window.JSZip || await this.loadJSZip();
            const zip = new JSZip();

            // Generate HTML for each business
            businessData.forEach((business, index) => {
                const mergedData = {
                    ...this.getFormData(),
                    ...business,
                    pageTitle: business.pageTitle || `${business.businessName} - QR Page`,
                    logoUrl: business.logoUrl || this.logoUrl.value,
                    faviconUrl: business.faviconUrl || this.faviconUrl.value
                };

                const html = this.generateHTML(mergedData);
                const filename = `${business.businessName.toLowerCase().replace(/\s+/g, '-')}-qr-page.html`;
                zip.file(filename, html);
            });

            // Generate and download zip
            const content = await zip.generateAsync({ type: 'blob' });
            saveAs(content, 'qr-pages-batch.zip');

        } catch (error) {
            console.error('Batch export error:', error);
            alert('Error in batch export. Please check your JSON format.');
        }
    }

    async loadJSZip() {
        // Load JSZip dynamically if not available
        if (!window.JSZip) {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            document.head.appendChild(script);

            return new Promise((resolve) => {
                script.onload = () => resolve(window.JSZip);
            });
        }
        return window.JSZip;
    }

    showQRModal() {
        this.qrModal.classList.remove('hidden');
        this.qrModal.classList.add('flex');
        this.qrUrl.value = this.websiteUrl.value;

        // Reset QR preview
        document.getElementById('qrcode').innerHTML = '<p class="text-gray-500">Click "Generate QR" to see preview</p>';
        this.downloadQr.classList.add('hidden');
        this.downloadQrSvg.classList.add('hidden');
    }

    hideQRModal() {
        this.qrModal.classList.add('hidden');
        this.qrModal.classList.remove('flex');
        document.getElementById('qrcode').innerHTML = '<p class="text-gray-500">Click "Generate QR" to see preview</p>';
        this.downloadQr.classList.add('hidden');
        this.downloadQrSvg.classList.add('hidden');
    }

    async generateQRCode() {
        const url = this.qrUrl.value;
        if (!url) {
            alert('Please enter a URL');
            return;
        }

        const qrContainer = document.getElementById('qrcode');
        qrContainer.innerHTML = '<p class="text-gray-500">Generating QR code...</p>';

        try {
            // Get customization options
            const size = parseInt(this.qrSize.value);
            const errorLevel = this.qrErrorLevel.value;
            const foreground = this.qrForeground.value;
            const background = this.qrBackground.value;
            const includeLogo = this.qrLogo.checked;

            // QR Code options
            const qrOptions = {
                width: size,
                height: size,
                colorDark: foreground,
                colorLight: background,
                correctLevel: QRCode.CorrectLevel[errorLevel]
            };

            // Generate QR code
            const canvas = document.createElement('canvas');
            await new Promise((resolve, reject) => {
                QRCode.toCanvas(canvas, url, qrOptions, (error) => {
                    if (error) reject(error);
                    else resolve();
                });
            });

            // If logo is enabled and available, overlay it
            if (includeLogo && this.logoUrl.value) {
                await this.addLogoToQR(canvas, size);
            }

            // Display the QR code
            qrContainer.innerHTML = '';
            qrContainer.appendChild(canvas);

            // Store canvas for download
            this.currentQRCanvas = canvas;

            // Show download buttons
            this.downloadQr.classList.remove('hidden');
            this.downloadQrSvg.classList.remove('hidden');

        } catch (error) {
            console.error('Error generating QR code:', error);
            qrContainer.innerHTML = '<p class="text-red-500">Error generating QR code</p>';
        }
    }

    async addLogoToQR(canvas, qrSize) {
        return new Promise((resolve, reject) => {
            const ctx = canvas.getContext('2d');
            const logo = new Image();

            logo.onload = () => {
                // Calculate logo size (about 20% of QR code)
                const logoSize = qrSize * 0.2;
                const x = (qrSize - logoSize) / 2;
                const y = (qrSize - logoSize) / 2;

                // Add white background circle for logo
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(qrSize / 2, qrSize / 2, logoSize / 2 + 5, 0, 2 * Math.PI);
                ctx.fill();

                // Draw logo
                ctx.drawImage(logo, x, y, logoSize, logoSize);
                resolve();
            };

            logo.onerror = () => {
                console.warn('Could not load logo for QR code');
                resolve(); // Continue without logo
            };

            logo.crossOrigin = 'anonymous';
            logo.src = this.logoUrl.value;
        });
    }

    downloadQRCode() {
        if (this.currentQRCanvas) {
            this.currentQRCanvas.toBlob((blob) => {
                const filename = `${this.businessName.value.toLowerCase().replace(/\s+/g, '-')}-qr-code.png`;
                saveAs(blob, filename);
            });
        }
    }

    downloadQRCodeSVG() {
        const url = this.qrUrl.value;
        if (!url) {
            alert('Please enter a URL');
            return;
        }

        try {
            // Get customization options
            const size = parseInt(this.qrSize.value);
            const errorLevel = this.qrErrorLevel.value;
            const foreground = this.qrForeground.value;
            const background = this.qrBackground.value;

            // Generate SVG QR code
            QRCode.toString(url, {
                type: 'svg',
                width: size,
                height: size,
                color: {
                    dark: foreground,
                    light: background
                },
                errorCorrectionLevel: errorLevel
            }, (error, svgString) => {
                if (error) {
                    console.error('Error generating SVG QR code:', error);
                    alert('Error generating SVG QR code');
                } else {
                    const blob = new Blob([svgString], { type: 'image/svg+xml' });
                    const filename = `${this.businessName.value.toLowerCase().replace(/\s+/g, '-')}-qr-code.svg`;
                    saveAs(blob, filename);
                }
            });
        } catch (error) {
            console.error('Error generating SVG QR code:', error);
            alert('Error generating SVG QR code');
        }
    }
}

// Initialize the editor when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.templateEditor = new TemplateEditor();
});
