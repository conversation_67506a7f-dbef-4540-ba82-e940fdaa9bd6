/**
 * Template Management Service
 */

import type { 
  Template, 
  TemplateLibrary, 
  TemplateSearchParams, 
  TemplateSearchResult,
  TemplateUploadData,
  BusinessInstance,
  TemplateCustomizations
} from '../types/template';
import { templateValidator } from '../utils/templateValidator';

const API_BASE_URL = 'http://localhost:3001';

export class TemplateService {
  
  // ===== TEMPLATE MANAGEMENT =====
  
  /**
   * Get all templates with optional filtering
   */
  async getTemplates(params?: TemplateSearchParams): Promise<TemplateSearchResult> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params?.query) queryParams.append('q', params.query);
      if (params?.category) queryParams.append('category', params.category);
      if (params?.sortBy) queryParams.append('_sort', params.sortBy);
      if (params?.sortOrder) queryParams.append('_order', params.sortOrder);
      if (params?.page) queryParams.append('_page', params.page.toString());
      if (params?.limit) queryParams.append('_limit', params.limit.toString());
      
      const response = await fetch(`${API_BASE_URL}/templates?${queryParams}`);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      
      const templates = await response.json();
      const totalCount = parseInt(response.headers.get('X-Total-Count') || '0');
      
      return {
        templates,
        totalCount,
        page: params?.page || 1,
        totalPages: Math.ceil(totalCount / (params?.limit || 10)),
        hasMore: (params?.page || 1) * (params?.limit || 10) < totalCount
      };
    } catch (error) {
      console.error('Failed to fetch templates:', error);
      throw error;
    }
  }

  /**
   * Get template by ID
   */
  async getTemplate(id: string): Promise<Template> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates/${id}`);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch template:', error);
      throw error;
    }
  }

  /**
   * Create new template
   */
  async createTemplate(templateData: Omit<Template, 'id' | 'createdAt' | 'updatedAt'>): Promise<Template> {
    try {
      // Validate template before creating
      const validation = templateValidator.validateTemplate(templateData.htmlContent, templateData.metadata);
      
      const template: Template = {
        ...templateData,
        id: this.generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        validationResult: validation,
        downloadCount: 0,
        status: validation.isValid ? 'published' : 'draft'
      };

      const response = await fetch(`${API_BASE_URL}/templates`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(template)
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to create template:', error);
      throw error;
    }
  }

  /**
   * Upload template from files
   */
  async uploadTemplate(uploadData: TemplateUploadData): Promise<Template> {
    try {
      // Read HTML file
      const htmlContent = await this.readFileAsText(uploadData.htmlFile);
      
      // Read CSS file if provided
      let cssContent = '';
      if (uploadData.cssFile) {
        cssContent = await this.readFileAsText(uploadData.cssFile);
      }

      // Read JS file if provided
      let jsContent = '';
      if (uploadData.jsFile) {
        jsContent = await this.readFileAsText(uploadData.jsFile);
      }

      // Process preview image if provided
      let previewImage = '';
      if (uploadData.previewImage) {
        previewImage = await this.fileToBase64(uploadData.previewImage);
      }

      // Generate metadata
      const metadata = templateValidator.generateMetadata(
        htmlContent, 
        this.generateId(), 
        uploadData.name
      );

      // Create template
      const template = await this.createTemplate({
        name: uploadData.name,
        description: uploadData.description,
        category: uploadData.category,
        version: '1.0.0',
        htmlContent,
        cssContent,
        jsContent,
        metadata: {
          ...metadata,
          tags: uploadData.tags
        },
        previewImage,
        status: 'draft',
        isPublic: uploadData.isPublic,
        downloadCount: 0
      });

      return template;
    } catch (error) {
      console.error('Failed to upload template:', error);
      throw error;
    }
  }

  /**
   * Update template
   */
  async updateTemplate(id: string, updates: Partial<Template>): Promise<Template> {
    try {
      const updatedTemplate = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      // Re-validate if HTML content changed
      if (updates.htmlContent) {
        const validation = templateValidator.validateTemplate(updates.htmlContent);
        updatedTemplate.validationResult = validation;
      }

      const response = await fetch(`${API_BASE_URL}/templates/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedTemplate)
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to update template:', error);
      throw error;
    }
  }

  /**
   * Delete template
   */
  async deleteTemplate(id: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/templates/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    } catch (error) {
      console.error('Failed to delete template:', error);
      throw error;
    }
  }

  // ===== BUSINESS INSTANCE MANAGEMENT =====

  /**
   * Get all business instances
   */
  async getBusinessInstances(): Promise<BusinessInstance[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/businesses`);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch business instances:', error);
      throw error;
    }
  }

  /**
   * Get business instance by ID
   */
  async getBusinessInstance(id: string): Promise<BusinessInstance> {
    try {
      const response = await fetch(`${API_BASE_URL}/businesses/${id}`);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch business instance:', error);
      throw error;
    }
  }

  /**
   * Create business instance from template
   */
  async createBusinessInstance(
    templateId: string, 
    businessName: string, 
    initialData?: any
  ): Promise<BusinessInstance> {
    try {
      const template = await this.getTemplate(templateId);
      
      const businessInstance: BusinessInstance = {
        id: this.generateId(),
        businessName,
        templateId,
        templateVersion: template.version,
        businessData: initialData || this.getDefaultBusinessData(businessName),
        customizations: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        viewCount: 0,
        isActive: true,
        isPublic: false,
        exportHistory: []
      };

      const response = await fetch(`${API_BASE_URL}/businesses`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(businessInstance)
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to create business instance:', error);
      throw error;
    }
  }

  /**
   * Update business instance
   */
  async updateBusinessInstance(id: string, updates: Partial<BusinessInstance>): Promise<BusinessInstance> {
    try {
      const updatedInstance = {
        ...updates,
        updatedAt: new Date().toISOString()
      };

      const response = await fetch(`${API_BASE_URL}/businesses/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedInstance)
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to update business instance:', error);
      throw error;
    }
  }

  /**
   * Delete business instance
   */
  async deleteBusinessInstance(id: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/businesses/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    } catch (error) {
      console.error('Failed to delete business instance:', error);
      throw error;
    }
  }

  // ===== UTILITY METHODS =====

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private async readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }

  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  private getDefaultBusinessData(businessName: string): any {
    return {
      businessName,
      slogan: '',
      description: '',
      phone: '',
      email: '',
      address: '',
      hours: '',
      website: '',
      logoUrl: '',
      faviconUrl: '',
      galleryImages: [],
      services: [],
      offers: [],
      facebookUrl: '',
      instagramUrl: '',
      yelpUrl: '',
      customSocialLinks: [],
      primaryColor: '#1976d2',
      secondaryColor: '#dc004e'
    };
  }
}

export const templateService = new TemplateService();
