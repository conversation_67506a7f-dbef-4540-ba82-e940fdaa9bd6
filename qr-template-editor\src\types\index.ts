// Business Data Types
export interface BusinessData {
  // Basic Info
  pageTitle: string;
  businessName: string;
  slogan?: string;
  logoUrl: string;
  logoFile?: File;
  faviconUrl: string;
  faviconFile?: File;

  // Header Customization
  headerBackgroundType: 'color' | 'gradient' | 'image';
  headerBackgroundValue: string; // Color hex, gradient CSS, or image URL
  headerTextColor: string; // Text color for contrast
  
  // Contact Info
  phone: string;
  email?: string;
  address: string;
  hours: string;
  
  // Links & Social
  websiteUrl: string;
  bookingUrl?: string;
  facebookUrl?: string;
  instagramUrl?: string;
  yelpUrl?: string;
  googleMapsUrl?: string;
  customSocialLinks: CustomSocialLink[];
  
  // Template & Styling
  template: TemplateType;
  primaryColor: string;
  secondaryColor: string;
  customCSS?: string;
  
  // Media
  galleryImages: string[];
  galleryFiles?: File[];
  
  // Services & Offers
  services: Service[];
  offers: Offer[];
}

export interface Service {
  id: string;
  name: string;
  description: string;
  price: string;
  duration?: string;
}

export interface Offer {
  id: string;
  title: string;
  description: string;
  validUntil?: string;
  discount?: string;
}

export interface CustomSocialLink {
  id: string;
  name: string;
  url: string;
  icon: string;
  color?: string;
}

// Template Types
export type TemplateType = 'modern' | 'luxury' | 'minimal' | 'cute';

export interface Template {
  id: TemplateType;
  name: string;
  description: string;
  preview: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

// Editor Types
export type EditorView = 'basic' | 'visual' | 'advanced';
export type BasicTab = 'basic-info' | 'media' | 'contact' | 'business' | 'social' | 'design' | 'advanced';

export interface EditorState {
  currentView: EditorView;
  currentBasicTab: BasicTab;
  isPreviewOpen: boolean;
  previewDevice: 'mobile' | 'tablet' | 'desktop';
}

// Validation Types
export interface ValidationResult {
  isValid: boolean;
  message: string;
  type: 'success' | 'error' | 'warning';
}

export interface FormValidation {
  [key: string]: ValidationResult;
}

// QR Code Types
export interface QRCodeOptions {
  size: number;
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
  foregroundColor: string;
  backgroundColor: string;
  includeLogo: boolean;
}

// Export Types
export interface ExportOptions {
  format: 'html' | 'zip';
  includeAssets: boolean;
  minify: boolean;
}

// Template Management Types
export interface TemplateRecord {
  id: number;
  name: string;
  description: string;
  category: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  // All BusinessData fields
  pageTitle: string;
  businessName: string;
  logoUrl?: string;
  faviconUrl?: string;
  phone?: string;
  email?: string;
  address: string;
  hours: string;
  websiteUrl: string;
  bookingUrl?: string;
  facebookUrl?: string;
  instagramUrl?: string;
  yelpUrl?: string;
  googleMapsUrl?: string;
  customSocialLinks: CustomSocialLink[];
  template: TemplateType;
  primaryColor: string;
  secondaryColor: string;
  customCSS?: string;
  galleryImages: string[];
  services: Service[];
  offers: Offer[];
}

export interface TemplateCategory {
  id: number;
  name: string;
  slug: string;
  description: string;
  icon: string;
}

export interface AppSettings {
  defaultTemplate: TemplateType;
  maxTemplatesPerUser: number;
  autoSaveInterval: number;
}

// Business Profile Types (legacy - for backward compatibility)
export interface BusinessProfile {
  id: string;
  name: string;
  data: BusinessData;
  createdAt: string;
  updatedAt: string;
}

// Bulk Operations
export interface BulkEditData {
  template?: TemplateType;
  primaryColor?: string;
  logoUrl?: string;
  websiteDomain?: string;
}
