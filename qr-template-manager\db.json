{"templates": [{"id": "modern-business-001", "name": "Modern Business Template", "description": "Clean, modern design perfect for professional businesses", "category": "business", "version": "1.0.0", "author": "QR Template Manager", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "htmlContent": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{BUSINESS_NAME}}</title>\n    {{#HAS_FAVICON}}<link rel=\"icon\" href=\"{{FAVICON_URL}}\">{{/HAS_FAVICON}}\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }\n        .header { background: {{PRIMARY_COLOR}}; color: white; padding: 2rem 0; text-align: center; }\n        {{#HAS_LOGO}}.logo { max-width: 150px; margin-bottom: 1rem; }{{/HAS_LOGO}}\n        .business-name { font-size: 2.5rem; margin-bottom: 0.5rem; }\n        .slogan { font-size: 1.2rem; opacity: 0.9; }\n        .content { padding: 3rem 0; }\n        .section { margin-bottom: 3rem; }\n        .section h2 { color: {{PRIMARY_COLOR}}; margin-bottom: 1rem; }\n        .contact-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; }\n        .contact-item { padding: 1rem; background: #f8f9fa; border-radius: 8px; }\n        {{#HAS_SERVICES}}.services { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; }{{/HAS_SERVICES}}\n        {{#HAS_SERVICES}}.service { padding: 1.5rem; border: 1px solid #e0e0e0; border-radius: 8px; }{{/HAS_SERVICES}}\n        {{#HAS_GALLERY}}.gallery { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; }{{/HAS_GALLERY}}\n        {{#HAS_GALLERY}}.gallery img { width: 100%; height: 200px; object-fit: cover; border-radius: 8px; }{{/HAS_GALLERY}}\n        .social-links { display: flex; gap: 1rem; justify-content: center; margin-top: 2rem; }\n        .social-link { padding: 0.5rem 1rem; background: {{SECONDARY_COLOR}}; color: white; text-decoration: none; border-radius: 5px; }\n        @media (max-width: 768px) {\n            .business-name { font-size: 2rem; }\n            .contact-info { grid-template-columns: 1fr; }\n        }\n    </style>\n</head>\n<body>\n    <header class=\"header\">\n        <div class=\"container\">\n            {{#HAS_LOGO}}<img src=\"{{LOGO_URL}}\" alt=\"{{BUSINESS_NAME}} Logo\" class=\"logo\">{{/HAS_LOGO}}\n            <h1 class=\"business-name\">{{BUSINESS_NAME}}</h1>\n            {{#SLOGAN}}<p class=\"slogan\">{{SLOGAN}}</p>{{/SLOGAN}}\n        </div>\n    </header>\n\n    <main class=\"content\">\n        <div class=\"container\">\n            {{#DESCRIPTION}}\n            <section class=\"section\">\n                <h2>About Us</h2>\n                <p>{{DESCRIPTION}}</p>\n            </section>\n            {{/DESCRIPTION}}\n\n            {{#HAS_SERVICES}}\n            <section class=\"section\">\n                <h2>Our Services</h2>\n                <div class=\"services\">\n                    {{#SERVICES}}\n                    <div class=\"service\">\n                        <h3>{{name}}</h3>\n                        {{#description}}<p>{{description}}</p>{{/description}}\n                        {{#price}}<p><strong>Price: {{price}}</strong></p>{{/price}}\n                    </div>\n                    {{/SERVICES}}\n                </div>\n            </section>\n            {{/HAS_SERVICES}}\n\n            {{#HAS_GALLERY}}\n            <section class=\"section\">\n                <h2>Gallery</h2>\n                <div class=\"gallery\">\n                    {{#GALLERY_IMAGES}}\n                    <img src=\"{{url}}\" alt=\"{{alt}}\">\n                    {{/GALLERY_IMAGES}}\n                </div>\n            </section>\n            {{/HAS_GALLERY}}\n\n            <section class=\"section\">\n                <h2>Contact Information</h2>\n                <div class=\"contact-info\">\n                    <div class=\"contact-item\">\n                        <h3>Phone</h3>\n                        <p>{{PHONE}}</p>\n                    </div>\n                    <div class=\"contact-item\">\n                        <h3>Email</h3>\n                        <p>{{EMAIL}}</p>\n                    </div>\n                    <div class=\"contact-item\">\n                        <h3>Address</h3>\n                        <p>{{ADDRESS}}</p>\n                    </div>\n                    {{#HOURS}}\n                    <div class=\"contact-item\">\n                        <h3>Hours</h3>\n                        <p>{{HOURS}}</p>\n                    </div>\n                    {{/HOURS}}\n                </div>\n            </section>\n\n            {{#HAS_SOCIAL_LINKS}}\n            <section class=\"section\">\n                <h2>Follow Us</h2>\n                <div class=\"social-links\">\n                    {{#FACEBOOK_URL}}<a href=\"{{FACEBOOK_URL}}\" class=\"social-link\">Facebook</a>{{/FACEBOOK_URL}}\n                    {{#INSTAGRAM_URL}}<a href=\"{{INSTAGRAM_URL}}\" class=\"social-link\">Instagram</a>{{/INSTAGRAM_URL}}\n                    {{#YELP_URL}}<a href=\"{{YELP_URL}}\" class=\"social-link\">Yelp</a>{{/YELP_URL}}\n                    {{#CUSTOM_SOCIAL_LINKS}}\n                    <a href=\"{{url}}\" class=\"social-link\">{{platform}}</a>\n                    {{/CUSTOM_SOCIAL_LINKS}}\n                </div>\n            </section>\n            {{/HAS_SOCIAL_LINKS}}\n        </div>\n    </main>\n</body>\n</html>", "cssContent": "", "jsContent": "", "metadata": {"features": ["responsive", "services", "gallery", "social", "contact"], "customizable": {"colors": true, "fonts": true, "layout": false, "header": true, "footer": false, "sidebar": false}, "requiredVariables": ["BUSINESS_NAME", "PHONE", "EMAIL", "ADDRESS"], "optionalVariables": ["SLOGAN", "DESCRIPTION", "LOGO_URL", "FAVICON_URL", "HOURS", "FACEBOOK_URL", "INSTAGRAM_URL", "YELP_URL", "PRIMARY_COLOR", "SECONDARY_COLOR"], "supportedDevices": ["mobile", "tablet", "desktop"], "dependencies": [], "tags": ["modern", "business", "responsive", "clean"]}, "previewImage": "", "thumbnailImage": "", "status": "published", "isPublic": true, "downloadCount": 0, "rating": 5, "validationResult": {"isValid": true, "errors": [], "warnings": [], "score": 95, "lastValidated": "2024-01-01T00:00:00.000Z"}}, {"id": "luxury-beauty-001", "name": "Luxury Beauty Template", "description": "Elegant design perfect for beauty salons and spas", "category": "beauty", "version": "1.0.0", "author": "QR Template Manager", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "htmlContent": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{BUSINESS_NAME}}</title>\n    {{#HAS_FAVICON}}<link rel=\"icon\" href=\"{{FAVICON_URL}}\">{{/HAS_FAVICON}}\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { font-family: 'Georgia', serif; line-height: 1.6; color: #2c2c2c; background: #faf8f5; }\n        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }\n        .header { background: linear-gradient(135deg, {{PRIMARY_COLOR}}, {{SECONDARY_COLOR}}); color: white; padding: 4rem 0; text-align: center; position: relative; }\n        .header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"2\" fill=\"white\" opacity=\"0.1\"/></svg>') repeat; }\n        {{#HAS_LOGO}}.logo { max-width: 120px; margin-bottom: 1.5rem; border-radius: 50%; }{{/HAS_LOGO}}\n        .business-name { font-size: 3rem; margin-bottom: 0.5rem; font-weight: 300; letter-spacing: 2px; }\n        .slogan { font-size: 1.3rem; opacity: 0.9; font-style: italic; }\n        .content { padding: 4rem 0; }\n        .section { margin-bottom: 4rem; text-align: center; }\n        .section h2 { color: {{PRIMARY_COLOR}}; margin-bottom: 2rem; font-size: 2.5rem; font-weight: 300; }\n        .services { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 2rem; }\n        .service { padding: 2rem; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .service:hover { transform: translateY(-5px); }\n        .service h3 { color: {{SECONDARY_COLOR}}; margin-bottom: 1rem; font-size: 1.5rem; }\n        .contact-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-top: 2rem; }\n        .contact-card { padding: 2rem; background: white; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); }\n        .contact-card h3 { color: {{PRIMARY_COLOR}}; margin-bottom: 1rem; }\n        .social-links { display: flex; gap: 1rem; justify-content: center; margin-top: 3rem; }\n        .social-link { padding: 1rem 2rem; background: {{SECONDARY_COLOR}}; color: white; text-decoration: none; border-radius: 25px; transition: all 0.3s ease; }\n        .social-link:hover { background: {{PRIMARY_COLOR}}; transform: scale(1.05); }\n        @media (max-width: 768px) {\n            .business-name { font-size: 2.5rem; }\n            .services { grid-template-columns: 1fr; }\n            .contact-grid { grid-template-columns: 1fr; }\n        }\n    </style>\n</head>\n<body>\n    <header class=\"header\">\n        <div class=\"container\">\n            {{#HAS_LOGO}}<img src=\"{{LOGO_URL}}\" alt=\"{{BUSINESS_NAME}} Logo\" class=\"logo\">{{/HAS_LOGO}}\n            <h1 class=\"business-name\">{{BUSINESS_NAME}}</h1>\n            {{#SLOGAN}}<p class=\"slogan\">{{SLOGAN}}</p>{{/SLOGAN}}\n        </div>\n    </header>\n\n    <main class=\"content\">\n        <div class=\"container\">\n            {{#DESCRIPTION}}\n            <section class=\"section\">\n                <h2>About Our Salon</h2>\n                <p style=\"font-size: 1.2rem; max-width: 800px; margin: 0 auto; line-height: 1.8;\">{{DESCRIPTION}}</p>\n            </section>\n            {{/DESCRIPTION}}\n\n            {{#HAS_SERVICES}}\n            <section class=\"section\">\n                <h2>Our Services</h2>\n                <div class=\"services\">\n                    {{#SERVICES}}\n                    <div class=\"service\">\n                        <h3>{{name}}</h3>\n                        {{#description}}<p>{{description}}</p>{{/description}}\n                        {{#price}}<p style=\"font-size: 1.2rem; color: {{SECONDARY_COLOR}}; font-weight: bold; margin-top: 1rem;\">{{price}}</p>{{/price}}\n                    </div>\n                    {{/SERVICES}}\n                </div>\n            </section>\n            {{/HAS_SERVICES}}\n\n            <section class=\"section\">\n                <h2>Contact Us</h2>\n                <div class=\"contact-grid\">\n                    <div class=\"contact-card\">\n                        <h3>📞 Phone</h3>\n                        <p>{{PHONE}}</p>\n                    </div>\n                    <div class=\"contact-card\">\n                        <h3>✉️ Email</h3>\n                        <p>{{EMAIL}}</p>\n                    </div>\n                    <div class=\"contact-card\">\n                        <h3>📍 Address</h3>\n                        <p>{{ADDRESS}}</p>\n                    </div>\n                    {{#HOURS}}\n                    <div class=\"contact-card\">\n                        <h3>🕒 Hours</h3>\n                        <p>{{HOURS}}</p>\n                    </div>\n                    {{/HOURS}}\n                </div>\n            </section>\n\n            {{#HAS_SOCIAL_LINKS}}\n            <section class=\"section\">\n                <h2>Follow Us</h2>\n                <div class=\"social-links\">\n                    {{#FACEBOOK_URL}}<a href=\"{{FACEBOOK_URL}}\" class=\"social-link\">Facebook</a>{{/FACEBOOK_URL}}\n                    {{#INSTAGRAM_URL}}<a href=\"{{INSTAGRAM_URL}}\" class=\"social-link\">Instagram</a>{{/INSTAGRAM_URL}}\n                    {{#YELP_URL}}<a href=\"{{YELP_URL}}\" class=\"social-link\">Yelp</a>{{/YELP_URL}}\n                </div>\n            </section>\n            {{/HAS_SOCIAL_LINKS}}\n        </div>\n    </main>\n</body>\n</html>", "cssContent": "", "jsContent": "", "metadata": {"features": ["responsive", "services", "social", "contact", "luxury"], "customizable": {"colors": true, "fonts": true, "layout": false, "header": true, "footer": false, "sidebar": false}, "requiredVariables": ["BUSINESS_NAME", "PHONE", "EMAIL", "ADDRESS"], "optionalVariables": ["SLOGAN", "DESCRIPTION", "LOGO_URL", "FAVICON_URL", "HOURS", "FACEBOOK_URL", "INSTAGRAM_URL", "YELP_URL", "PRIMARY_COLOR", "SECONDARY_COLOR"], "supportedDevices": ["mobile", "tablet", "desktop"], "dependencies": [], "tags": ["luxury", "beauty", "salon", "spa", "elegant"]}, "previewImage": "", "thumbnailImage": "", "status": "published", "isPublic": true, "downloadCount": 0, "rating": 4.8, "validationResult": {"isValid": true, "errors": [], "warnings": [], "score": 92, "lastValidated": "2024-01-01T00:00:00.000Z"}}], "businesses": [{"id": "unique-nails-001", "businessName": "Unique Nails Lounge", "templateId": "luxury-beauty-001", "templateVersion": "1.0.0", "businessData": {"businessName": "Unique Nails Lounge", "slogan": "Where Beauty Meets Perfection", "description": "Experience luxury nail care in our modern salon. We offer premium manicures, pedicures, and nail art services.", "phone": "(*************", "email": "<EMAIL>", "address": "123 Main Street, Watertown, MA 02472", "hours": "Mon-Sat: 9AM-7PM, Sun: 10AM-6PM", "website": "https://uniquenailslounge.com", "logoUrl": "", "faviconUrl": "", "galleryImages": [], "services": [{"id": "service-001", "name": "Classic Manicure", "description": "Professional nail care with polish", "price": "$25", "duration": "30 min", "category": "manicure", "isActive": true, "order": 1}, {"id": "service-002", "name": "Gel Manicure", "description": "Long-lasting gel polish manicure", "price": "$35", "duration": "45 min", "category": "manicure", "isActive": true, "order": 2}], "offers": [], "facebookUrl": "https://www.facebook.com/uniquenailslounge", "instagramUrl": "https://www.instagram.com/maidennailsspa/", "yelpUrl": "https://www.yelp.com/biz/luxx-nails-watertown", "customSocialLinks": [], "primaryColor": "#d4af37", "secondaryColor": "#8b4513"}, "customizations": {"primaryColor": "#d4af37", "secondaryColor": "#8b4513", "enabledFeatures": ["services", "social", "contact"], "disabledFeatures": []}, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "viewCount": 0, "isActive": true, "isPublic": false, "exportHistory": []}, {"id": "1749473777036", "businessName": "<PERSON>", "templateId": "modern-business-001", "templateVersion": "1.0.0", "businessData": {"businessName": "<PERSON>", "phone": "", "email": "", "address": "", "services": [], "offers": [], "galleryImages": [], "customSocialLinks": [], "primaryColor": "#1976d2", "secondaryColor": "#dc004e"}, "customizations": {}, "createdAt": "2025-06-09T12:56:17.036Z", "updatedAt": "2025-06-09T12:56:17.036Z", "viewCount": 0, "isActive": true, "isPublic": false, "exportHistory": []}]}