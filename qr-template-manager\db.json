{"templates": [{"id": "modern-business-001", "name": "Modern Business Template", "description": "Clean, modern design perfect for professional businesses", "category": "business", "version": "1.0.0", "author": "QR Template Manager", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "htmlContent": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{BUSINESS_NAME}}</title>\n    {{#HAS_FAVICON}}<link rel=\"icon\" href=\"{{FAVICON_URL}}\">{{/HAS_FAVICON}}\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }\n        .header { background: {{PRIMARY_COLOR}}; color: white; padding: 2rem 0; text-align: center; }\n        {{#HAS_LOGO}}.logo { max-width: 150px; margin-bottom: 1rem; }{{/HAS_LOGO}}\n        .business-name { font-size: 2.5rem; margin-bottom: 0.5rem; }\n        .slogan { font-size: 1.2rem; opacity: 0.9; }\n        .content { padding: 3rem 0; }\n        .section { margin-bottom: 3rem; }\n        .section h2 { color: {{PRIMARY_COLOR}}; margin-bottom: 1rem; }\n        .contact-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; }\n        .contact-item { padding: 1rem; background: #f8f9fa; border-radius: 8px; }\n        {{#HAS_SERVICES}}.services { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; }{{/HAS_SERVICES}}\n        {{#HAS_SERVICES}}.service { padding: 1.5rem; border: 1px solid #e0e0e0; border-radius: 8px; }{{/HAS_SERVICES}}\n        {{#HAS_GALLERY}}.gallery { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; }{{/HAS_GALLERY}}\n        {{#HAS_GALLERY}}.gallery img { width: 100%; height: 200px; object-fit: cover; border-radius: 8px; }{{/HAS_GALLERY}}\n        .social-links { display: flex; gap: 1rem; justify-content: center; margin-top: 2rem; }\n        .social-link { padding: 0.5rem 1rem; background: {{SECONDARY_COLOR}}; color: white; text-decoration: none; border-radius: 5px; }\n        @media (max-width: 768px) {\n            .business-name { font-size: 2rem; }\n            .contact-info { grid-template-columns: 1fr; }\n        }\n    </style>\n</head>\n<body>\n    <header class=\"header\">\n        <div class=\"container\">\n            {{#HAS_LOGO}}<img src=\"{{LOGO_URL}}\" alt=\"{{BUSINESS_NAME}} Logo\" class=\"logo\">{{/HAS_LOGO}}\n            <h1 class=\"business-name\">{{BUSINESS_NAME}}</h1>\n            {{#SLOGAN}}<p class=\"slogan\">{{SLOGAN}}</p>{{/SLOGAN}}\n        </div>\n    </header>\n\n    <main class=\"content\">\n        <div class=\"container\">\n            {{#DESCRIPTION}}\n            <section class=\"section\">\n                <h2>About Us</h2>\n                <p>{{DESCRIPTION}}</p>\n            </section>\n            {{/DESCRIPTION}}\n\n            {{#HAS_SERVICES}}\n            <section class=\"section\">\n                <h2>Our Services</h2>\n                <div class=\"services\">\n                    {{#SERVICES}}\n                    <div class=\"service\">\n                        <h3>{{name}}</h3>\n                        {{#description}}<p>{{description}}</p>{{/description}}\n                        {{#price}}<p><strong>Price: {{price}}</strong></p>{{/price}}\n                    </div>\n                    {{/SERVICES}}\n                </div>\n            </section>\n            {{/HAS_SERVICES}}\n\n            {{#HAS_GALLERY}}\n            <section class=\"section\">\n                <h2>Gallery</h2>\n                <div class=\"gallery\">\n                    {{#GALLERY_IMAGES}}\n                    <img src=\"{{url}}\" alt=\"{{alt}}\">\n                    {{/GALLERY_IMAGES}}\n                </div>\n            </section>\n            {{/HAS_GALLERY}}\n\n            <section class=\"section\">\n                <h2>Contact Information</h2>\n                <div class=\"contact-info\">\n                    <div class=\"contact-item\">\n                        <h3>Phone</h3>\n                        <p>{{PHONE}}</p>\n                    </div>\n                    <div class=\"contact-item\">\n                        <h3>Email</h3>\n                        <p>{{EMAIL}}</p>\n                    </div>\n                    <div class=\"contact-item\">\n                        <h3>Address</h3>\n                        <p>{{ADDRESS}}</p>\n                    </div>\n                    {{#HOURS}}\n                    <div class=\"contact-item\">\n                        <h3>Hours</h3>\n                        <p>{{HOURS}}</p>\n                    </div>\n                    {{/HOURS}}\n                </div>\n            </section>\n\n            {{#HAS_SOCIAL_LINKS}}\n            <section class=\"section\">\n                <h2>Follow Us</h2>\n                <div class=\"social-links\">\n                    {{#FACEBOOK_URL}}<a href=\"{{FACEBOOK_URL}}\" class=\"social-link\">Facebook</a>{{/FACEBOOK_URL}}\n                    {{#INSTAGRAM_URL}}<a href=\"{{INSTAGRAM_URL}}\" class=\"social-link\">Instagram</a>{{/INSTAGRAM_URL}}\n                    {{#YELP_URL}}<a href=\"{{YELP_URL}}\" class=\"social-link\">Yelp</a>{{/YELP_URL}}\n                    {{#CUSTOM_SOCIAL_LINKS}}\n                    <a href=\"{{url}}\" class=\"social-link\">{{platform}}</a>\n                    {{/CUSTOM_SOCIAL_LINKS}}\n                </div>\n            </section>\n            {{/HAS_SOCIAL_LINKS}}\n        </div>\n    </main>\n</body>\n</html>", "cssContent": "", "jsContent": "", "metadata": {"features": ["responsive", "services", "gallery", "social", "contact"], "customizable": {"colors": true, "fonts": true, "layout": false, "header": true, "footer": false, "sidebar": false}, "requiredVariables": ["BUSINESS_NAME", "PHONE", "EMAIL", "ADDRESS"], "optionalVariables": ["SLOGAN", "DESCRIPTION", "LOGO_URL", "FAVICON_URL", "HOURS", "FACEBOOK_URL", "INSTAGRAM_URL", "YELP_URL", "PRIMARY_COLOR", "SECONDARY_COLOR"], "supportedDevices": ["mobile", "tablet", "desktop"], "dependencies": [], "tags": ["modern", "business", "responsive", "clean"]}, "previewImage": "", "thumbnailImage": "", "status": "published", "isPublic": true, "downloadCount": 0, "rating": 5, "validationResult": {"isValid": true, "errors": [], "warnings": [], "score": 95, "lastValidated": "2024-01-01T00:00:00.000Z"}}, {"id": "luxury-beauty-001", "name": "Luxury Beauty Template", "description": "Elegant design perfect for beauty salons and spas", "category": "beauty", "version": "1.0.0", "author": "QR Template Manager", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "htmlContent": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{BUSINESS_NAME}}</title>\n    {{#HAS_FAVICON}}<link rel=\"icon\" href=\"{{FAVICON_URL}}\">{{/HAS_FAVICON}}\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { font-family: 'Georgia', serif; line-height: 1.6; color: #2c2c2c; background: #faf8f5; }\n        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }\n        .header { background: linear-gradient(135deg, {{PRIMARY_COLOR}}, {{SECONDARY_COLOR}}); color: white; padding: 4rem 0; text-align: center; position: relative; }\n        .header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"2\" fill=\"white\" opacity=\"0.1\"/></svg>') repeat; }\n        {{#HAS_LOGO}}.logo { max-width: 120px; margin-bottom: 1.5rem; border-radius: 50%; }{{/HAS_LOGO}}\n        .business-name { font-size: 3rem; margin-bottom: 0.5rem; font-weight: 300; letter-spacing: 2px; }\n        .slogan { font-size: 1.3rem; opacity: 0.9; font-style: italic; }\n        .content { padding: 4rem 0; }\n        .section { margin-bottom: 4rem; text-align: center; }\n        .section h2 { color: {{PRIMARY_COLOR}}; margin-bottom: 2rem; font-size: 2.5rem; font-weight: 300; }\n        .services { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 2rem; }\n        .service { padding: 2rem; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .service:hover { transform: translateY(-5px); }\n        .service h3 { color: {{SECONDARY_COLOR}}; margin-bottom: 1rem; font-size: 1.5rem; }\n        .contact-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-top: 2rem; }\n        .contact-card { padding: 2rem; background: white; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); }\n        .contact-card h3 { color: {{PRIMARY_COLOR}}; margin-bottom: 1rem; }\n        .social-links { display: flex; gap: 1rem; justify-content: center; margin-top: 3rem; }\n        .social-link { padding: 1rem 2rem; background: {{SECONDARY_COLOR}}; color: white; text-decoration: none; border-radius: 25px; transition: all 0.3s ease; }\n        .social-link:hover { background: {{PRIMARY_COLOR}}; transform: scale(1.05); }\n        @media (max-width: 768px) {\n            .business-name { font-size: 2.5rem; }\n            .services { grid-template-columns: 1fr; }\n            .contact-grid { grid-template-columns: 1fr; }\n        }\n    </style>\n</head>\n<body>\n    <header class=\"header\">\n        <div class=\"container\">\n            {{#HAS_LOGO}}<img src=\"{{LOGO_URL}}\" alt=\"{{BUSINESS_NAME}} Logo\" class=\"logo\">{{/HAS_LOGO}}\n            <h1 class=\"business-name\">{{BUSINESS_NAME}}</h1>\n            {{#SLOGAN}}<p class=\"slogan\">{{SLOGAN}}</p>{{/SLOGAN}}\n        </div>\n    </header>\n\n    <main class=\"content\">\n        <div class=\"container\">\n            {{#DESCRIPTION}}\n            <section class=\"section\">\n                <h2>About Our Salon</h2>\n                <p style=\"font-size: 1.2rem; max-width: 800px; margin: 0 auto; line-height: 1.8;\">{{DESCRIPTION}}</p>\n            </section>\n            {{/DESCRIPTION}}\n\n            {{#HAS_SERVICES}}\n            <section class=\"section\">\n                <h2>Our Services</h2>\n                <div class=\"services\">\n                    {{#SERVICES}}\n                    <div class=\"service\">\n                        <h3>{{name}}</h3>\n                        {{#description}}<p>{{description}}</p>{{/description}}\n                        {{#price}}<p style=\"font-size: 1.2rem; color: {{SECONDARY_COLOR}}; font-weight: bold; margin-top: 1rem;\">{{price}}</p>{{/price}}\n                    </div>\n                    {{/SERVICES}}\n                </div>\n            </section>\n            {{/HAS_SERVICES}}\n\n            <section class=\"section\">\n                <h2>Contact Us</h2>\n                <div class=\"contact-grid\">\n                    <div class=\"contact-card\">\n                        <h3>📞 Phone</h3>\n                        <p>{{PHONE}}</p>\n                    </div>\n                    <div class=\"contact-card\">\n                        <h3>✉️ Email</h3>\n                        <p>{{EMAIL}}</p>\n                    </div>\n                    <div class=\"contact-card\">\n                        <h3>📍 Address</h3>\n                        <p>{{ADDRESS}}</p>\n                    </div>\n                    {{#HOURS}}\n                    <div class=\"contact-card\">\n                        <h3>🕒 Hours</h3>\n                        <p>{{HOURS}}</p>\n                    </div>\n                    {{/HOURS}}\n                </div>\n            </section>\n\n            {{#HAS_SOCIAL_LINKS}}\n            <section class=\"section\">\n                <h2>Follow Us</h2>\n                <div class=\"social-links\">\n                    {{#FACEBOOK_URL}}<a href=\"{{FACEBOOK_URL}}\" class=\"social-link\">Facebook</a>{{/FACEBOOK_URL}}\n                    {{#INSTAGRAM_URL}}<a href=\"{{INSTAGRAM_URL}}\" class=\"social-link\">Instagram</a>{{/INSTAGRAM_URL}}\n                    {{#YELP_URL}}<a href=\"{{YELP_URL}}\" class=\"social-link\">Yelp</a>{{/YELP_URL}}\n                </div>\n            </section>\n            {{/HAS_SOCIAL_LINKS}}\n        </div>\n    </main>\n</body>\n</html>", "cssContent": "", "jsContent": "", "metadata": {"features": ["responsive", "services", "social", "contact", "luxury"], "customizable": {"colors": true, "fonts": true, "layout": false, "header": true, "footer": false, "sidebar": false}, "requiredVariables": ["BUSINESS_NAME", "PHONE", "EMAIL", "ADDRESS"], "optionalVariables": ["SLOGAN", "DESCRIPTION", "LOGO_URL", "FAVICON_URL", "HOURS", "FACEBOOK_URL", "INSTAGRAM_URL", "YELP_URL", "PRIMARY_COLOR", "SECONDARY_COLOR"], "supportedDevices": ["mobile", "tablet", "desktop"], "dependencies": [], "tags": ["luxury", "beauty", "salon", "spa", "elegant"]}, "previewImage": "", "thumbnailImage": "", "status": "published", "isPublic": true, "downloadCount": 0, "rating": 4.8, "validationResult": {"isValid": true, "errors": [], "warnings": [], "score": 92, "lastValidated": "2024-01-01T00:00:00.000Z"}}, {"id": "1749475461482", "name": "Công ty TNHH ABC", "description": "", "category": "beauty", "version": "1.0.0", "author": "User", "createdAt": "2025-06-09T13:24:21.482Z", "updatedAt": "2025-06-09T13:24:21.482Z", "htmlContent": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>{{BUSINESS_NAME}}</title>\r\n</head>\r\n<body>\r\n    <h1>{{BUSINESS_NAME}}</h1>\r\n    <div class=\"contact\">\r\n        <p>Phone: {{PHONE}}</p>\r\n        <p>Email: {{EMAIL}}</p>\r\n        <p>Address: {{ADDRESS}}</p>\r\n    </div>\r\n    \r\n    {{#HAS_SERVICES}}\r\n    <div class=\"services\">\r\n        {{#SERVICES}}\r\n        <div>\r\n            <h3>{{name}}</h3>\r\n            <p>{{description}} - {{price}}</p>\r\n        </div>\r\n        {{/SERVICES}}\r\n    </div>\r\n    {{/HAS_SERVICES}}\r\n</body>\r\n</html>", "cssContent": "", "jsContent": "", "metadata": {"features": ["conditional-has_services", "array-services", "responsive", "services", "contact"], "customizable": {"colors": false, "fonts": false, "layout": false, "header": false, "footer": false, "sidebar": false}, "requiredVariables": ["BUSINESS_NAME", "PHONE", "EMAIL", "ADDRESS"], "optionalVariables": ["SLOGAN", "DESCRIPTION", "LOGO_URL", "FAVICON_URL"], "supportedDevices": ["mobile", "tablet", "desktop"], "dependencies": [], "tags": []}, "previewImage": "", "status": "draft", "isPublic": false, "downloadCount": 0, "validationResult": {"isValid": true, "errors": [], "warnings": [], "features": ["conditional-has_services", "array-services", "responsive", "services", "contact"], "score": 90, "lastValidated": "2025-06-09T13:24:21.482Z"}}, {"id": "1749475630735", "name": "Ngân hàng BIDV", "description": "", "category": "restaurant", "version": "1.0.0", "author": "User", "createdAt": "2025-06-09T13:27:10.735Z", "updatedAt": "2025-06-09T13:27:10.735Z", "htmlContent": "<!DOCTYPE html>\r\n<html>\r\n\r\n<head>\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>{{BUSINESS_NAME}}</title>\r\n</head>\r\n\r\n<body>\r\n    <h1>{{BUSINESS_NAME}}</h1>\r\n    <p>{{SLOGAN}}</p>\r\n\r\n    <!-- Conditional Sections -->\r\n    {{#HAS_LOGO}}\r\n    <img src=\"{{LOGO_URL}}\" alt=\"{{BUSINESS_NAME}} Logo\">\r\n    {{/HAS_LOGO}}\r\n\r\n    <!-- Loops for Arrays -->\r\n    {{#SERVICES}}\r\n    <div class=\"service\">\r\n        <h3>{{name}}</h3>\r\n        <p>{{description}}</p>\r\n        <span class=\"price\">${{price}}</span>\r\n    </div>\r\n    {{/SERVICES}}\r\n\r\n    <!-- Conditional Content -->\r\n    {{#HAS_SERVICES}}\r\n    <section class=\"services-section\">\r\n        <h2>Our Services</h2>\r\n        {{#SERVICES}}\r\n        <div class=\"service-item\">\r\n            <h3>{{name}}</h3>\r\n            <p>{{description}}</p>\r\n            <span>${{price}}</span>\r\n        </div>\r\n        {{/SERVICES}}\r\n    </section>\r\n    {{/HAS_SERVICES}}\r\n</body>\r\n\r\n</html>", "cssContent": "", "jsContent": "", "metadata": {"features": ["conditional-has_logo", "conditional-has_services", "array-services", "responsive", "services"], "customizable": {"colors": false, "fonts": false, "layout": false, "header": false, "footer": false, "sidebar": false}, "requiredVariables": ["BUSINESS_NAME", "PHONE", "EMAIL", "ADDRESS"], "optionalVariables": ["SLOGAN", "DESCRIPTION", "LOGO_URL", "FAVICON_URL"], "supportedDevices": ["mobile", "tablet", "desktop"], "dependencies": [], "tags": []}, "previewImage": "", "status": "draft", "isPublic": false, "downloadCount": 0, "validationResult": {"isValid": true, "errors": [], "warnings": ["Recommended variable missing: {{PHONE}}", "Recommended variable missing: {{EMAIL}}", "Recommended variable missing: {{ADDRESS}}", "Unknown conditional section: {{#SERVICES}}"], "features": ["conditional-has_logo", "conditional-has_services", "array-services", "responsive", "services"], "score": 90, "lastValidated": "2025-06-09T13:27:10.735Z"}}, {"id": "1749475801702", "name": "Template 02", "description": "", "category": "beauty", "version": "1.0.0", "author": "User", "createdAt": "2025-06-09T13:30:01.702Z", "updatedAt": "2025-06-09T13:30:01.702Z", "htmlContent": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{{PAGE_TITLE}}</title>\n    {{#if FAVICON_URL}}\n    <link rel=\"icon\" type=\"image/webp\" href=\"{{FAVICON_URL}}\">\n    <link rel=\"shortcut icon\" type=\"image/webp\" href=\"{{FAVICON_URL}}\">\n    {{/if}}\n    <script src=\"https://cdn.tailwindcss.com/3.4.16\"></script>\n    <script>tailwind.config = { theme: { extend: { colors: { primary: '#d4af37', secondary: '#222222' }, borderRadius: { 'none': '0px', 'sm': '4px', DEFAULT: '8px', 'md': '12px', 'lg': '16px', 'xl': '20px', '2xl': '24px', '3xl': '32px', 'full': '9999px', 'button': '8px' } } } }</script>\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Pacifico&display=swap\" rel=\"stylesheet\">\n    <link href=\"https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap\" rel=\"stylesheet\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css\">\n    <style>\n        :where([class^=\"ri-\"])::before {\n            content: \"\\f3c2\";\n        }\n\n        body {\n            font-family: 'Playfair Display', serif;\n            background-color: #f9f5eb;\n        }\n\n        .gold-gradient {\n            background: linear-gradient(135deg, #d4af37 0%, #f9e076 50%, #d4af37 100%);\n        }\n\n        /* Modern Animations */\n        @keyframes fadeInUp {\n            from {\n                opacity: 0;\n                transform: translateY(30px);\n            }\n            to {\n                opacity: 1;\n                transform: translateY(0);\n            }\n        }\n\n        @keyframes slideInLeft {\n            from {\n                opacity: 0;\n                transform: translateX(-30px);\n            }\n            to {\n                opacity: 1;\n                transform: translateX(0);\n            }\n        }\n\n        @keyframes pulse {\n            0%, 100% {\n                transform: scale(1);\n            }\n            50% {\n                transform: scale(1.05);\n            }\n        }\n\n        @keyframes shimmer {\n            0% {\n                background-position: -200px 0;\n            }\n            100% {\n                background-position: calc(200px + 100%) 0;\n            }\n        }\n\n        .animate-fadeInUp {\n            animation: fadeInUp 0.8s ease-out forwards;\n        }\n\n        .animate-slideInLeft {\n            animation: slideInLeft 0.6s ease-out forwards;\n        }\n\n        .animate-pulse-slow {\n            animation: pulse 2s ease-in-out infinite;\n        }\n\n        .shimmer {\n            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);\n            background-size: 200px 100%;\n            animation: shimmer 2s infinite;\n        }\n\n        .hover-scale {\n            transition: all 0.3s ease;\n        }\n\n        .hover-scale:hover {\n            transform: scale(1.02);\n            box-shadow: 0 8px 25px rgba(0,0,0,0.15);\n        }\n\n        .stagger-animation > * {\n            opacity: 0;\n            animation: fadeInUp 0.8s ease-out forwards;\n        }\n\n        .stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }\n        .stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }\n        .stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }\n        .stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }\n\n        /* Template Specific Styles */\n        \n                .template-modern .gold-gradient {\n                    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 50%, #3B82F6 100%);\n                }\n                .template-modern .hover-scale:hover {\n                    transform: scale(1.05) rotate(1deg);\n                }\n            \n\n        /* Custom CSS */\n\n        /* Custom Social Icons */\n        .custom-social-icon {\n            background-color: #1976d2; /* Default color */\n        }\n\n    </style>\n</head>\n\n<body class=\"bg-white\">\n    <!-- Main Content -->\n    <main>\n        <!-- Enhanced Header Banner -->\n        <div class=\"relative w-full h-48 overflow-hidden header-background\">\n            <!-- Overlay for better text readability on images -->\n            <div class=\"absolute inset-0 bg-black bg-opacity-30 header-overlay\"></div>\n\n            <div class=\"absolute inset-0 flex flex-col items-center justify-center text-center px-4 header-content\">\n                {{#if HAS_LOGO}}\n                <!-- Logo Display -->\n                <img src=\"{{LOGO_URL}}\" alt=\"{{BUSINESS_NAME}} Logo\" class=\"h-24 md:h-28 mb-2 animate-pulse-slow drop-shadow-lg\">\n                {{/if}}\n\n                {{#if HAS_NO_LOGO}}\n                <!-- Text Logo when no image -->\n                <h1 class=\"text-3xl font-bold mb-2 tracking-wider animate-fadeInUp drop-shadow-lg header-text\">{{BUSINESS_NAME}}</h1>\n                {{/if}}\n\n                {{#if SLOGAN}}\n                <!-- Slogan -->\n                <p class=\"text-lg opacity-90 animate-fadeInUp drop-shadow-md header-text\">{{SLOGAN}}</p>\n                {{/if}}\n            </div>\n        </div>\n\n        <!-- Connect Section -->\n        <div class=\"px-5 py-6 bg-white\">\n            <h2 class=\"text-2xl font-semibold text-secondary mb-2 animate-slideInLeft\">Connect with us</h2>\n            <p class=\"text-gray-600 mb-6 animate-fadeInUp\">Follow us and get updates delivered to your favorite social media channel. Stay informed about our latest promotions and nail art trends.</p>\n            \n            <!-- Social Media Links -->\n            <div class=\"space-y-4 stagger-animation\">\n                {{#if FACEBOOK_URL}}\n                <!-- Facebook -->\n                <a href=\"{{FACEBOOK_URL}}\" target=\"_blank\" class=\"flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale\">\n                    <div class=\"w-10 h-10 flex items-center justify-center bg-[#1877F2] rounded-full mr-4\">\n                        <i class=\"ri-facebook-fill ri-lg text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Facebook</h3>\n                        <p class=\"text-sm text-gray-500\">Visit our Facebook page</p>\n                    </div>\n                </a>\n                {{/if}}\n\n                {{#if INSTAGRAM_URL}}\n                <!-- Instagram -->\n                <a href=\"{{INSTAGRAM_URL}}\" target=\"_blank\" class=\"flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale\">\n                    <div class=\"w-10 h-10 flex items-center justify-center bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mr-4\">\n                        <i class=\"ri-instagram-line ri-lg text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Instagram</h3>\n                        <p class=\"text-sm text-gray-500\">Follow us on Instagram</p>\n                    </div>\n                </a>\n                {{/if}}\n\n                {{#if YELP_URL}}\n                <!-- Yelp Review -->\n                <a href=\"{{YELP_URL}}\" target=\"_blank\" class=\"flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale\">\n                    <div class=\"w-10 h-10 flex items-center justify-center bg-[#D32323] rounded-full mr-4\">\n                        <i class=\"ri-yelp-fill ri-lg text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Yelp Review</h3>\n                        <p class=\"text-sm text-gray-500\">Review us on Yelp</p>\n                    </div>\n                </a>\n                {{/if}}\n\n                {{#if GOOGLE_MAPS_URL}}\n                <!-- Google Review -->\n                <a href=\"{{GOOGLE_MAPS_URL}}\" target=\"_blank\" class=\"flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale\">\n                    <div class=\"w-10 h-10 flex items-center justify-center bg-white rounded-full mr-4 border border-gray-200\">\n                        <i class=\"ri-google-fill ri-lg\" style=\"color: #4285F4;\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Google Review</h3>\n                        <p class=\"text-sm text-gray-500\">Review us on Google</p>\n                    </div>\n                </a>\n                {{/if}}\n\n                <!-- Custom Social Links -->\n                {{#each CUSTOM_SOCIAL_LINKS}}\n                <a href=\"{{this.url}}\" target=\"_blank\" class=\"flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale\">\n                    <div class=\"w-10 h-10 flex items-center justify-center rounded-full mr-4 border border-gray-200 custom-social-icon\" data-color=\"{{this.color}}\">\n                        <i class=\"ri-{{this.icon}}-line ri-lg text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">{{this.name}}</h3>\n                        <p class=\"text-sm text-gray-500\">Visit our {{this.name}}</p>\n                    </div>\n                </a>\n                {{/each}}\n            </div>\n        </div>\n\n        <!-- Services Section -->\n        {{#if HAS_SERVICES}}\n        <div class=\"px-5 py-6 bg-white\">\n            <h2 class=\"text-2xl font-semibold text-secondary mb-6 animate-slideInLeft\">Our Services</h2>\n            <div class=\"space-y-4 stagger-animation\">\n                {{#each SERVICES}}\n                <div class=\"bg-gray-50 p-4 rounded-lg hover-scale\">\n                    <div class=\"flex justify-between items-start mb-2\">\n                        <h3 class=\"font-medium text-secondary\">{{this.name}}</h3>\n                        <span class=\"text-primary font-semibold\">{{this.price}}</span>\n                    </div>\n                    <p class=\"text-sm text-gray-600\">{{this.description}}</p>\n                    {{#if this.duration}}\n                    <p class=\"text-xs text-gray-500 mt-1\">Duration: {{this.duration}}</p>\n                    {{/if}}\n                </div>\n                {{/each}}\n            </div>\n        </div>\n        {{/if}}\n        \n\n        <!-- Special Offers Section -->\n        {{#if HAS_OFFERS}}\n        <div class=\"px-5 py-6 bg-gradient-to-r from-purple-50 to-pink-50\">\n            <h2 class=\"text-2xl font-semibold text-secondary mb-6 animate-slideInLeft\">\n                <i class=\"ri-gift-line mr-2\"></i>Special Offers\n            </h2>\n            <div class=\"space-y-4 stagger-animation\">\n                {{#each OFFERS}}\n                <div class=\"bg-white p-4 rounded-lg border border-purple-200 hover-scale\">\n                    <h3 class=\"font-medium text-secondary mb-2\">{{this.title}}</h3>\n                    {{#if this.discount}}\n                    <p class=\"text-sm text-purple-600 font-semibold\">{{this.discount}}</p>\n                    {{/if}}\n                    {{#if this.validUntil}}\n                    <p class=\"text-xs text-purple-600\">Valid until: {{this.validUntil}}</p>\n                    {{/if}}\n                    <p class=\"text-sm text-gray-600 mb-2\">{{this.description}}</p>\n                </div>\n                {{/each}}\n            </div>\n        </div>\n        {{/if}}\n        \n\n        <!-- Gallery Section -->\n        {{#if HAS_GALLERY}}\n        <div class=\"px-5 py-6 bg-white\">\n            <h2 class=\"text-2xl font-semibold text-secondary mb-6 animate-slideInLeft\">\n                <i class=\"ri-image-line mr-2\"></i>Gallery\n            </h2>\n            <div class=\"grid grid-cols-2 md:grid-cols-3 gap-4 stagger-animation\">\n                {{#each GALLERY_IMAGES}}\n                <div class=\"overflow-hidden rounded-lg shadow-md\">\n                    <img\n                        src=\"{{this.url}}\"\n                        alt=\"Gallery image {{this.index}}\"\n                        class=\"w-full h-32 md:h-40 object-cover\"\n                        loading=\"lazy\"\n                    />\n                </div>\n                {{/each}}\n            </div>\n        </div>\n        {{/if}}\n\n        <!-- Action Buttons -->\n        <div class=\"px-5 py-6 bg-gray-50\">\n            <h2 class=\"text-2xl font-semibold text-secondary mb-6 animate-slideInLeft\">Quick Actions</h2>\n            <div class=\"stagger-animation\">\n                {{#if WEBSITE_URL}}\n                <!-- Visit Website -->\n                <a href=\"{{WEBSITE_URL}}\" target=\"_blank\" class=\"flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale\">\n                    <div class=\"w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4\">\n                        <i class=\"ri-global-line ri-lg text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Visit our website</h3>\n                        <p class=\"text-sm text-gray-500\">Learn more about us</p>\n                    </div>\n                </a>\n                {{/if}}\n\n                {{#if BOOKING_URL}}\n                <!-- Book Appointment -->\n                <a href=\"{{BOOKING_URL}}\" target=\"_blank\" class=\"flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale\">\n                    <div class=\"w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4\">\n                        <i class=\"ri-calendar-line ri-lg text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Book an appointment</h3>\n                        <p class=\"text-sm text-gray-500\">Online booking available</p>\n                    </div>\n                </a>\n                {{/if}}\n\n                <!-- Call Now -->\n                <a href=\"tel:{{PHONE_CLEAN}}\" class=\"flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale\">\n                    <div class=\"w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4\">\n                        <i class=\"ri-phone-line ri-lg text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Call us now</h3>\n                        <p class=\"text-sm text-gray-500\">{{PHONE}}</p>\n                    </div>\n                </a>\n            </div>\n        </div>\n\n        <!-- Contact Info -->\n        <div class=\"px-5 py-6 bg-gray-50\">\n            <h2 class=\"text-2xl font-semibold text-secondary mb-4 animate-slideInLeft\">Visit Us</h2>\n            <div class=\"bg-white p-4 rounded-lg shadow-sm mb-4 animate-fadeInUp hover-scale\">\n                <div class=\"flex items-start mb-3\">\n                    <div class=\"w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1\">\n                        <i class=\"ri-map-pin-line text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Address</h3>\n                        <p class=\"text-gray-600\">{{ADDRESS_HTML}}</p>\n                    </div>\n                </div>\n                <div class=\"flex items-start mb-3\">\n                    <div class=\"w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1\">\n                        <i class=\"ri-time-line text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Hours</h3>\n                        <p class=\"text-gray-600\">{{HOURS_HTML}}</p>\n                    </div>\n                </div>\n                <div class=\"flex items-start mb-3\">\n                    <div class=\"w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1\">\n                        <i class=\"ri-phone-line text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Phone</h3>\n                        <p class=\"text-gray-600\">{{PHONE}}</p>\n                    </div>\n                </div>\n                {{#if EMAIL}}\n                <div class=\"flex items-start\">\n                    <div class=\"w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1\">\n                        <i class=\"ri-mail-line text-white\"></i>\n                    </div>\n                    <div>\n                        <h3 class=\"font-medium text-secondary\">Email</h3>\n                        <p class=\"text-gray-600\">\n                            <a href=\"mailto:{{EMAIL}}\" class=\"text-primary hover:underline\">{{EMAIL}}</a>\n                        </p>\n                    </div>\n                </div>\n                {{/if}}\n            </div>\n\n            {{#if GOOGLE_MAPS_URL}}\n            <a href=\"{{GOOGLE_MAPS_URL}}\" target=\"_blank\" class=\"w-full mt-4 py-3 bg-primary text-white font-medium rounded-button flex items-center justify-center cursor-pointer hover-scale\">\n                <i class=\"ri-navigation-line ri-lg mr-2\"></i>\n                <span>Get Directions</span>\n            </a>\n            {{/if}}\n        </div>\n    </main>\n\n\n\n    <!-- Custom Social Colors JavaScript -->\n    <script>\n        // Initialize custom social colors\n        function initializeCustomSocialColors() {\n            const customSocialIcons = document.querySelectorAll('.custom-social-icon');\n            customSocialIcons.forEach(icon => {\n                const color = icon.dataset.color;\n                if (color && color !== 'undefined') {\n                    icon.style.backgroundColor = color;\n                }\n            });\n        }\n\n        // Initialize header styles\n        function initializeHeaderStyles() {\n            const headerBackground = document.querySelector('.header-background');\n            const headerText = document.querySelectorAll('.header-text');\n            const headerContent = document.querySelector('.header-content');\n            const headerOverlay = document.querySelector('.header-overlay');\n\n            // Header style data (will be replaced by template engine)\n            const headerBackgroundStyle = `{{HEADER_BACKGROUND_STYLE}}`;\n            const headerTextColor = `{{HEADER_TEXT_COLOR}}`;\n\n            // Track if styles were actually applied to reduce console spam\n            let stylesApplied = false;\n\n            if (headerBackground && headerBackgroundStyle) {\n                // Check if style is different before applying\n                const currentStyle = headerBackground.style.cssText;\n                if (!currentStyle.includes(headerBackgroundStyle)) {\n                    headerBackground.style.cssText += headerBackgroundStyle;\n                    console.log('🎨 Applied header background:', headerBackgroundStyle.substring(0, 50) + '...');\n                    stylesApplied = true;\n                }\n            }\n\n            if (headerContent && headerTextColor) {\n                // Apply header text color\n                headerContent.style.color = headerTextColor;\n            }\n\n            headerText.forEach(element => {\n                if (headerTextColor) {\n                    element.style.color = headerTextColor;\n                }\n            });\n\n            // Show/hide overlay based on background type (only log if styles were applied)\n            if (headerOverlay && stylesApplied) {\n                if (headerBackgroundStyle && headerBackgroundStyle.includes('url(')) {\n                    headerOverlay.style.display = 'block';\n                    console.log('🖼️ Showing overlay for background image');\n                } else {\n                    headerOverlay.style.display = 'none';\n                    console.log('🎨 Hiding overlay for solid/gradient background');\n                }\n            }\n        }\n\n        // Initialize when page loads\n        document.addEventListener('DOMContentLoaded', function() {\n            console.log('🔄 Initializing styles...');\n            initializeCustomSocialColors();\n            initializeHeaderStyles();\n            console.log('✅ Styles initialized');\n        });\n    </script>\n</body>\n</html>", "cssContent": "", "jsContent": "", "metadata": {"features": ["responsive", "gallery", "services", "offers", "social", "contact"], "customizable": {"colors": false, "fonts": true, "layout": false, "header": true, "footer": false, "sidebar": false}, "requiredVariables": ["BUSINESS_NAME", "PHONE", "EMAIL", "ADDRESS"], "optionalVariables": ["SLOGAN", "DESCRIPTION", "LOGO_URL", "FAVICON_URL"], "supportedDevices": ["mobile", "tablet", "desktop"], "dependencies": [], "tags": []}, "previewImage": "", "status": "draft", "isPublic": false, "downloadCount": 0, "validationResult": {"isValid": true, "errors": [], "warnings": ["Recommended variable missing: {{ADDRESS}}", "Unknown variable found: {{PAGE_TITLE}}", "Unknown variable found: {{GOOGLE_MAPS_URL}}", "Unknown variable found: {{WEBSITE_URL}}", "Unknown variable found: {{BOOKING_URL}}", "Unknown variable found: {{PHONE_CLEAN}}", "Unknown variable found: {{ADDRESS_HTML}}", "Unknown variable found: {{HOURS_HTML}}", "Unknown variable found: {{HEADER_BACKGROUND_STYLE}}"], "features": ["responsive", "gallery", "services", "offers", "social", "contact"], "score": 90, "lastValidated": "2025-06-09T13:30:01.702Z"}}], "businesses": [{"id": "unique-nails-001", "businessName": "Unique Nails Lounge", "templateId": "luxury-beauty-001", "templateVersion": "1.0.0", "businessData": {"businessName": "Unique Nails Lounge", "slogan": "Where Beauty Meets Perfection", "description": "Experience luxury nail care in our modern salon. We offer premium manicures, pedicures, and nail art services.", "phone": "(*************", "email": "<EMAIL>", "address": "123 Main Street, Watertown, MA 02472", "hours": "Mon-Sat: 9AM-7PM, Sun: 10AM-6PM", "website": "https://uniquenailslounge.com", "logoUrl": "", "faviconUrl": "", "galleryImages": [], "services": [{"id": "service-001", "name": "Classic Manicure", "description": "Professional nail care with polish", "price": "$25", "duration": "30 min", "category": "manicure", "isActive": true, "order": 1}, {"id": "service-002", "name": "Gel Manicure", "description": "Long-lasting gel polish manicure", "price": "$35", "duration": "45 min", "category": "manicure", "isActive": true, "order": 2}], "offers": [], "facebookUrl": "https://www.facebook.com/uniquenailslounge", "instagramUrl": "https://www.instagram.com/maidennailsspa/", "yelpUrl": "https://www.yelp.com/biz/luxx-nails-watertown", "customSocialLinks": [], "primaryColor": "#d4af37", "secondaryColor": "#8b4513"}, "customizations": {"primaryColor": "#d4af37", "secondaryColor": "#8b4513", "enabledFeatures": ["services", "social", "contact"], "disabledFeatures": []}, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "viewCount": 0, "isActive": true, "isPublic": false, "exportHistory": []}, {"id": "1749475852474", "businessName": "Business 2", "templateId": "luxury-beauty-001", "templateVersion": "1.0.0", "businessData": {"businessName": "Business 2", "phone": "", "email": "", "address": "", "services": [], "offers": [], "galleryImages": [], "customSocialLinks": [], "primaryColor": "#1976d2", "secondaryColor": "#dc004e"}, "customizations": {}, "createdAt": "2025-06-09T13:30:52.474Z", "updatedAt": "2025-06-09T13:30:52.474Z", "viewCount": 0, "isActive": true, "isPublic": false, "exportHistory": []}]}