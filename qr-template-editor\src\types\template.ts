/**
 * Template Management Types
 */

export interface Template {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  version: string;
  author?: string;
  createdAt: string;
  updatedAt: string;
  
  // Template content
  htmlContent: string;
  cssContent?: string;
  jsContent?: string;
  
  // Metadata
  metadata: TemplateMetadata;
  
  // Preview
  previewImage?: string;
  thumbnailImage?: string;
  
  // Status
  status: TemplateStatus;
  isPublic: boolean;
  downloadCount: number;
  rating?: number;
  
  // Validation
  validationResult?: TemplateValidationResult;
}

export interface TemplateMetadata {
  features: string[];
  customizable: {
    colors: boolean;
    fonts: boolean;
    layout: boolean;
    header: boolean;
    footer: boolean;
    sidebar: boolean;
  };
  requiredVariables: string[];
  optionalVariables: string[];
  supportedDevices: ('mobile' | 'tablet' | 'desktop')[];
  minScreenWidth?: number;
  maxScreenWidth?: number;
  dependencies?: string[];
  tags: string[];
}

export interface TemplateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number; // 0-100
  lastValidated: string;
}

export type TemplateCategory = 
  | 'business'
  | 'restaurant'
  | 'beauty'
  | 'healthcare'
  | 'retail'
  | 'service'
  | 'event'
  | 'portfolio'
  | 'custom';

export type TemplateStatus = 
  | 'draft'
  | 'published'
  | 'archived'
  | 'deprecated';

// Business Instance using a template
export interface BusinessInstance {
  id: string;
  businessName: string;
  templateId: string;
  templateVersion: string;
  
  // Business data
  businessData: BusinessData;
  
  // Customizations
  customizations: TemplateCustomizations;
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  lastExported?: string;
  
  // QR Code
  qrCodeUrl?: string;
  qrCodeData?: string;
  
  // Analytics
  viewCount: number;
  lastViewed?: string;
  
  // Status
  isActive: boolean;
  isPublic: boolean;
  
  // Export history
  exportHistory: ExportRecord[];
}

export interface TemplateCustomizations {
  // Colors
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  backgroundColor?: string;
  textColor?: string;
  
  // Typography
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: string;
  
  // Layout
  layoutVariant?: string;
  headerStyle?: string;
  footerStyle?: string;
  
  // Header customizations
  headerBackgroundType?: 'color' | 'gradient' | 'image' | 'pattern';
  headerBackgroundValue?: string;
  headerTextColor?: string;
  
  // Custom CSS
  customCSS?: string;
  
  // Feature toggles
  enabledFeatures: string[];
  disabledFeatures: string[];
}

export interface ExportRecord {
  id: string;
  exportedAt: string;
  format: 'html' | 'zip' | 'pdf';
  fileSize: number;
  downloadUrl?: string;
  expiresAt?: string;
}

// Template Library
export interface TemplateLibrary {
  templates: Template[];
  categories: TemplateCategory[];
  totalCount: number;
  featuredTemplates: string[]; // template IDs
  popularTemplates: string[]; // template IDs
  recentTemplates: string[]; // template IDs
}

// Template Search & Filter
export interface TemplateSearchParams {
  query?: string;
  category?: TemplateCategory;
  features?: string[];
  tags?: string[];
  author?: string;
  minRating?: number;
  sortBy?: 'name' | 'created' | 'updated' | 'rating' | 'downloads';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface TemplateSearchResult {
  templates: Template[];
  totalCount: number;
  page: number;
  totalPages: number;
  hasMore: boolean;
}

// Template Upload
export interface TemplateUploadData {
  name: string;
  description: string;
  category: TemplateCategory;
  htmlFile: File;
  cssFile?: File;
  jsFile?: File;
  previewImage?: File;
  tags: string[];
  isPublic: boolean;
}

// Business Data (enhanced from existing)
export interface BusinessData {
  // Basic Info
  businessName: string;
  slogan?: string;
  description?: string;
  
  // Contact
  phone: string;
  email: string;
  address: string;
  hours?: string;
  website?: string;
  
  // Media
  logoUrl?: string;
  logoFile?: File;
  faviconUrl?: string;
  faviconFile?: File;
  
  // Gallery
  galleryImages: GalleryImage[];
  
  // Services & Offers
  services: Service[];
  offers: Offer[];
  
  // Social Media
  facebookUrl?: string;
  instagramUrl?: string;
  yelpUrl?: string;
  customSocialLinks: SocialLink[];
  
  // Styling (for template customization)
  primaryColor?: string;
  secondaryColor?: string;
  headerBackgroundType?: 'color' | 'gradient' | 'image' | 'pattern';
  headerBackgroundValue?: string;
  headerTextColor?: string;
}

export interface GalleryImage {
  id: string;
  url: string;
  file?: File;
  caption?: string;
  alt?: string;
  order: number;
}

export interface Service {
  id: string;
  name: string;
  description?: string;
  price?: string;
  duration?: string;
  category?: string;
  isActive: boolean;
  order: number;
}

export interface Offer {
  id: string;
  title: string;
  description?: string;
  originalPrice?: string;
  discountPrice?: string;
  discountPercent?: number;
  validUntil?: string;
  isActive: boolean;
  order: number;
}

export interface SocialLink {
  id: string;
  platform: string;
  url: string;
  icon?: string;
  order: number;
}

// Template Engine Data
export interface TemplateRenderData {
  // All business data flattened for template variables
  [key: string]: any;
  
  // Computed properties
  HAS_LOGO: boolean;
  HAS_SERVICES: boolean;
  HAS_OFFERS: boolean;
  HAS_GALLERY: boolean;
  HAS_SOCIAL_LINKS: boolean;
  
  // Arrays for loops
  SERVICES: Service[];
  OFFERS: Offer[];
  GALLERY_IMAGES: GalleryImage[];
  CUSTOM_SOCIAL_LINKS: SocialLink[];
}
