import React, { useState } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Switch,
  FormControlLabel,
  Divider,
  Chip,
  Alert,
  IconButton
} from '@mui/material';
import { Palette, Upload, Refresh<PERSON><PERSON>, Eye } from 'lucide-react';
import type { Template } from '../../types/template';

interface ThemeCustomizationTabProps {
  template: Template;
  businessData: any;
  onUpdateTemplate: (field: string, value: any) => void;
  onUpdateBusinessData: (field: string, value: any) => void;
}

const ThemeCustomizationTab: React.FC<ThemeCustomizationTabProps> = ({
  template,
  businessData,
  onUpdateTemplate,
  onUpdateBusinessData
}) => {
  const [customCss, setCustomCss] = useState(template.cssContent || '');
  const [headerBackgroundType, setHeaderBackgroundType] = useState('solid');
  const [headerBackgroundImage, setHeaderBackgroundImage] = useState('');

  const colorPresets = [
    { name: 'Blue', primary: '#1976d2', secondary: '#dc004e' },
    { name: 'Green', primary: '#388e3c', secondary: '#f57c00' },
    { name: 'Purple', primary: '#7b1fa2', secondary: '#c2185b' },
    { name: 'Orange', primary: '#f57c00', secondary: '#1976d2' },
    { name: 'Teal', primary: '#00796b', secondary: '#e91e63' },
    { name: 'Gold', primary: '#d4af37', secondary: '#8b4513' }
  ];

  const templateStyles = [
    { name: 'Modern', value: 'modern' },
    { name: 'Luxury', value: 'luxury' },
    { name: 'Minimal', value: 'minimal' },
    { name: 'Cute', value: 'cute' }
  ];

  const handleColorPreset = (preset: any) => {
    onUpdateBusinessData('primaryColor', preset.primary);
    onUpdateBusinessData('secondaryColor', preset.secondary);
  };

  const handleHeaderBackgroundUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setHeaderBackgroundImage(result);
        setHeaderBackgroundType('image');
      };
      reader.readAsDataURL(file);
    }
  };

  const generateHeaderBackgroundStyle = () => {
    switch (headerBackgroundType) {
      case 'solid':
        return `background-color: ${businessData.primaryColor};`;
      case 'gradient':
        return `background: linear-gradient(135deg, ${businessData.primaryColor} 0%, ${businessData.secondaryColor} 100%);`;
      case 'image':
        return headerBackgroundImage 
          ? `background-image: url(${headerBackgroundImage}); background-size: cover; background-position: center;`
          : `background-color: ${businessData.primaryColor};`;
      default:
        return `background-color: ${businessData.primaryColor};`;
    }
  };

  const applyCssChanges = () => {
    onUpdateTemplate('cssContent', customCss);
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Theme Customization
      </Typography>

      <Grid container spacing={3}>
        {/* Color Customization */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              <Palette size={20} style={{ marginRight: 8, verticalAlign: 'middle' }} />
              Colors
            </Typography>

            <Grid container spacing={2}>
              <Grid size={{ xs: 6 }}>
                <TextField
                  fullWidth
                  label="Primary Color"
                  value={businessData.primaryColor}
                  onChange={(e) => onUpdateBusinessData('primaryColor', e.target.value)}
                  type="color"
                  margin="normal"
                />
              </Grid>
              <Grid size={{ xs: 6 }}>
                <TextField
                  fullWidth
                  label="Secondary Color"
                  value={businessData.secondaryColor}
                  onChange={(e) => onUpdateBusinessData('secondaryColor', e.target.value)}
                  type="color"
                  margin="normal"
                />
              </Grid>
            </Grid>

            <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
              Color Presets:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {colorPresets.map((preset) => (
                <Chip
                  key={preset.name}
                  label={preset.name}
                  onClick={() => handleColorPreset(preset)}
                  sx={{
                    bgcolor: preset.primary,
                    color: 'white',
                    '&:hover': { bgcolor: preset.secondary }
                  }}
                />
              ))}
            </Box>
          </Paper>
        </Grid>

        {/* Header Background */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Header Background
            </Typography>

            <FormControl fullWidth margin="normal">
              <InputLabel>Background Type</InputLabel>
              <Select
                value={headerBackgroundType}
                label="Background Type"
                onChange={(e) => setHeaderBackgroundType(e.target.value)}
              >
                <MenuItem value="solid">Solid Color</MenuItem>
                <MenuItem value="gradient">Gradient</MenuItem>
                <MenuItem value="image">Background Image</MenuItem>
              </Select>
            </FormControl>

            {headerBackgroundType === 'image' && (
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<Upload size={16} />}
                  fullWidth
                >
                  Upload Background Image
                  <input
                    type="file"
                    hidden
                    accept="image/*"
                    onChange={handleHeaderBackgroundUpload}
                  />
                </Button>
                {headerBackgroundImage && (
                  <Box sx={{ mt: 2, textAlign: 'center' }}>
                    <img
                      src={headerBackgroundImage}
                      alt="Header background"
                      style={{ maxWidth: '100%', maxHeight: '100px', objectFit: 'cover' }}
                    />
                  </Box>
                )}
              </Box>
            )}

            <Alert severity="info" sx={{ mt: 2 }}>
              Preview: {generateHeaderBackgroundStyle()}
            </Alert>
          </Paper>
        </Grid>

        {/* Template Style */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Template Style
            </Typography>

            <FormControl fullWidth margin="normal">
              <InputLabel>Style</InputLabel>
              <Select
                value={template.metadata?.templateStyle || 'modern'}
                label="Style"
                onChange={(e) => onUpdateTemplate('metadata', {
                  ...template.metadata,
                  templateStyle: e.target.value
                })}
              >
                {templateStyles.map((style) => (
                  <MenuItem key={style.value} value={style.value}>
                    {style.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Different styles will apply unique animations and layouts to your template.
            </Typography>
          </Paper>
        </Grid>

        {/* Custom CSS */}
        <Grid size={{ xs: 12 }}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Custom CSS
              </Typography>
              <Button
                variant="contained"
                startIcon={<RefreshCw size={16} />}
                onClick={applyCssChanges}
              >
                Apply Changes
              </Button>
            </Box>

            <TextField
              fullWidth
              multiline
              rows={12}
              value={customCss}
              onChange={(e) => setCustomCss(e.target.value)}
              placeholder="/* Add your custom CSS here */
.custom-header {
  background: linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%);
}

.custom-button {
  border-radius: 20px;
  padding: 10px 20px;
}

/* Your custom styles... */"
              sx={{
                '& .MuiInputBase-input': {
                  fontFamily: 'monospace',
                  fontSize: '14px'
                }
              }}
            />

            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Tips:</strong>
                <br />• Use CSS classes like .custom-header, .custom-button to style specific elements
                <br />• Variables like {businessData.primaryColor} will be replaced automatically
                <br />• Changes will be applied when you click "Apply Changes"
              </Typography>
            </Alert>
          </Paper>
        </Grid>

        {/* Advanced Settings */}
        <Grid size={{ xs: 12 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Advanced Settings
            </Typography>

            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={template.metadata?.features?.includes('animations') || false}
                      onChange={(e) => {
                        const features = template.metadata?.features || [];
                        const newFeatures = e.target.checked
                          ? [...features, 'animations']
                          : features.filter(f => f !== 'animations');
                        onUpdateTemplate('metadata', {
                          ...template.metadata,
                          features: newFeatures
                        });
                      }}
                    />
                  }
                  label="Enable Animations"
                />
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={template.metadata?.features?.includes('dark-mode') || false}
                      onChange={(e) => {
                        const features = template.metadata?.features || [];
                        const newFeatures = e.target.checked
                          ? [...features, 'dark-mode']
                          : features.filter(f => f !== 'dark-mode');
                        onUpdateTemplate('metadata', {
                          ...template.metadata,
                          features: newFeatures
                        });
                      }}
                    />
                  }
                  label="Dark Mode Support"
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 2 }} />

            <Typography variant="subtitle2" gutterBottom>
              Current Features:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {(template.metadata?.features || []).map((feature: string) => (
                <Chip key={feature} label={feature} size="small" />
              ))}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ThemeCustomizationTab;
