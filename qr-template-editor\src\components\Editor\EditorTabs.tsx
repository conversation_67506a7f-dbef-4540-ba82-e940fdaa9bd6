import React from 'react';
import { Tabs, Tab, Box } from '@mui/material';
import { 
  Info, 
  Image, 
  Phone, 
  Briefcase, 
  Share2, 
  Palette, 
  Settings 
} from 'lucide-react';
import type { BasicTab } from '../../types/index';

interface EditorTabsProps {
  currentTab: BasicTab;
  onTabChange: (tab: BasicTab) => void;
}

const tabConfig = [
  { id: 'basic-info', label: 'Basic Info', icon: Info },
  { id: 'media', label: 'Media', icon: Image },
  { id: 'contact', label: 'Contact', icon: Phone },
  { id: 'business', label: 'Business', icon: Briefcase },
  { id: 'social', label: 'Social', icon: Share2 },
  { id: 'design', label: 'Design', icon: Palette },
  { id: 'advanced', label: 'Advanced', icon: Settings },
] as const;

const EditorTabs: React.FC<EditorTabsProps> = ({ currentTab, onTabChange }) => {
  const handleChange = (_event: React.SyntheticEvent, newValue: BasicTab) => {
    onTabChange(newValue);
  };

  return (
    <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
      <Tabs
        value={currentTab}
        onChange={handleChange}
        variant="scrollable"
        scrollButtons="auto"
        sx={{
          '& .MuiTab-root': {
            minWidth: 'auto',
            px: 2,
            py: 1.5,
            fontSize: '0.875rem',
            fontWeight: 500,
          },
          '& .MuiTabs-indicator': {
            height: 3,
            borderRadius: '3px 3px 0 0',
          },
        }}
      >
        {tabConfig.map(({ id, label, icon: Icon }) => (
          <Tab
            key={id}
            value={id}
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Icon size={16} />
                <span>{label}</span>
              </Box>
            }
          />
        ))}
      </Tabs>
    </Box>
  );
};

export default EditorTabs;
