{"version": 3, "file": "skipLast.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/skipLast.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAC5C,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AA4ChE,MAAM,UAAU,QAAQ,CAAI,SAAiB;IAC3C,OAAO,SAAS,IAAI,CAAC;QACnB,CAAC;YACC,QAAQ;QACV,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;YAI7B,IAAI,IAAI,GAAQ,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;YAGrC,IAAI,IAAI,GAAG,CAAC,CAAC;YACb,MAAM,CAAC,SAAS,CACd,wBAAwB,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE;gBAK7C,MAAM,UAAU,GAAG,IAAI,EAAE,CAAC;gBAC1B,IAAI,UAAU,GAAG,SAAS,EAAE;oBAI1B,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;iBAC1B;qBAAM;oBAIL,MAAM,KAAK,GAAG,UAAU,GAAG,SAAS,CAAC;oBAGrC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;oBAKpB,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC3B;YACH,CAAC,CAAC,CACH,CAAC;YAEF,OAAO,GAAG,EAAE;gBAEV,IAAI,GAAG,IAAK,CAAC;YACf,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;AACT,CAAC"}