import React from 'react';
import {
  Card,
  CardContent,
  TextField,
  Typography,
  Box,
  Alert,
  Button,
  InputAdornment,
  IconButton,
  Paper
} from '@mui/material';
import { Share2, Facebook, Instagram, Globe, MapPin, CheckCircle, AlertCircle, Star, Plus, X } from 'lucide-react';
import type { BusinessData, ValidationResult, CustomSocialLink } from '../../types/index';
import { validateUrl } from '../../utils/validation';

interface SocialTabProps {
  businessData: BusinessData;
  onUpdate: (field: keyof BusinessData, value: string | CustomSocialLink[]) => void;
}

const SocialTab: React.FC<SocialTabProps> = ({ businessData, onUpdate }) => {
  const [validations, setValidations] = React.useState<{ [key: string]: ValidationResult }>({});

  const validateField = (field: keyof BusinessData, value: string) => {
    let validation: ValidationResult;
    
    switch (field) {
      case 'websiteUrl':
        validation = validateUrl(value, 'Website');
        break;
      case 'bookingUrl':
        validation = validateUrl(value, 'Booking');
        break;
      case 'facebookUrl':
        validation = validateUrl(value, 'Facebook');
        break;
      case 'instagramUrl':
        validation = validateUrl(value, 'Instagram');
        break;
      case 'yelpUrl':
        validation = validateUrl(value, 'Yelp');
        break;
      case 'googleMapsUrl':
        validation = validateUrl(value, 'Google Maps');
        break;
      default:
        validation = { isValid: true, message: '', type: 'success' };
    }
    
    setValidations(prev => ({ ...prev, [field]: validation }));
    return validation;
  };

  const handleFieldChange = (field: keyof BusinessData, value: string) => {
    onUpdate(field, value);
    validateField(field, value);
  };

  const getValidationProps = (field: keyof BusinessData) => {
    const validation = validations[field];
    if (!validation) return {};

    return {
      error: !validation.isValid,
      helperText: validation.message,
      InputProps: {
        endAdornment: validation.message && (
          <InputAdornment position="end">
            {validation.isValid ? (
              <CheckCircle size={16} color="green" />
            ) : (
              <AlertCircle size={16} color="red" />
            )}
          </InputAdornment>
        ),
      },
    };
  };

  const handleAddCustomSocialLink = () => {
    const newLink: CustomSocialLink = {
      id: Date.now().toString(),
      name: '',
      url: '',
      icon: 'globe',
      color: '#1976d2'
    };

    const updatedLinks = [...businessData.customSocialLinks, newLink];
    onUpdate('customSocialLinks', updatedLinks);
  };

  const handleUpdateCustomSocialLink = (id: string, field: keyof CustomSocialLink, value: string) => {
    const updatedLinks = businessData.customSocialLinks.map(link =>
      link.id === id ? { ...link, [field]: value } : link
    );
    onUpdate('customSocialLinks', updatedLinks);
  };

  const handleRemoveCustomSocialLink = (id: string) => {
    const updatedLinks = businessData.customSocialLinks.filter(link => link.id !== id);
    onUpdate('customSocialLinks', updatedLinks);
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              bgcolor: 'info.main',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            <Share2 size={20} color="white" />
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Social Media & Links
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Connect your online presence
            </Typography>
          </Box>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          Add your website and social media links to help customers find you online.
        </Alert>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          <TextField
            label="Website URL"
            value={businessData.websiteUrl}
            onChange={(e) => handleFieldChange('websiteUrl', e.target.value)}
            fullWidth
            placeholder="https://uniquenaillounge.com"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Globe size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('websiteUrl').InputProps,
            }}
            {...getValidationProps('websiteUrl')}
          />

          <TextField
            label="Booking URL"
            value={businessData.bookingUrl}
            onChange={(e) => handleFieldChange('bookingUrl', e.target.value)}
            fullWidth
            placeholder="https://booking.example.com"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Globe size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('bookingUrl').InputProps,
            }}
            {...getValidationProps('bookingUrl')}
          />

          <TextField
            label="Facebook URL"
            value={businessData.facebookUrl}
            onChange={(e) => handleFieldChange('facebookUrl', e.target.value)}
            fullWidth
            placeholder="https://facebook.com/yourpage"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Facebook size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('facebookUrl').InputProps,
            }}
            {...getValidationProps('facebookUrl')}
          />

          <TextField
            label="Instagram URL"
            value={businessData.instagramUrl}
            onChange={(e) => handleFieldChange('instagramUrl', e.target.value)}
            fullWidth
            placeholder="https://instagram.com/youraccount"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Instagram size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('instagramUrl').InputProps,
            }}
            {...getValidationProps('instagramUrl')}
          />

          <TextField
            label="Yelp URL"
            value={businessData.yelpUrl}
            onChange={(e) => handleFieldChange('yelpUrl', e.target.value)}
            fullWidth
            placeholder="https://yelp.com/biz/your-business"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Star size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('yelpUrl').InputProps,
            }}
            {...getValidationProps('yelpUrl')}
          />

          <TextField
            label="Google Maps URL"
            value={businessData.googleMapsUrl}
            onChange={(e) => handleFieldChange('googleMapsUrl', e.target.value)}
            fullWidth
            placeholder="https://maps.app.goo.gl/..."
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <MapPin size={16} />
                </InputAdornment>
              ),
              ...getValidationProps('googleMapsUrl').InputProps,
            }}
            {...getValidationProps('googleMapsUrl')}
          />

          {/* Custom Social Links Section */}
          <Box sx={{ mt: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" fontWeight="bold">
                Custom Social Links
              </Typography>
              <Button
                variant="outlined"
                startIcon={<Plus size={16} />}
                onClick={handleAddCustomSocialLink}
                size="small"
              >
                Add Link
              </Button>
            </Box>

            {businessData.customSocialLinks.length === 0 ? (
              <Alert severity="info">
                No custom social links added yet. Click "Add Link" to add more social media platforms.
              </Alert>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {businessData.customSocialLinks.map((link) => (
                  <Paper key={link.id} sx={{ p: 2, border: '1px solid', borderColor: 'grey.300' }}>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
                      <TextField
                        label="Platform Name"
                        value={link.name}
                        onChange={(e) => handleUpdateCustomSocialLink(link.id, 'name', e.target.value)}
                        placeholder="e.g., TikTok, LinkedIn"
                        size="small"
                        sx={{ flex: 1 }}
                      />
                      <TextField
                        label="URL"
                        value={link.url}
                        onChange={(e) => handleUpdateCustomSocialLink(link.id, 'url', e.target.value)}
                        placeholder="https://..."
                        size="small"
                        sx={{ flex: 2 }}
                      />
                      <TextField
                        label="Icon"
                        value={link.icon}
                        onChange={(e) => handleUpdateCustomSocialLink(link.id, 'icon', e.target.value)}
                        placeholder="globe"
                        size="small"
                        sx={{ flex: 1 }}
                      />
                      <TextField
                        label="Color"
                        value={link.color || '#1976d2'}
                        onChange={(e) => handleUpdateCustomSocialLink(link.id, 'color', e.target.value)}
                        placeholder="#1976d2"
                        size="small"
                        type="color"
                        sx={{ flex: 0.5 }}
                      />
                      <IconButton
                        onClick={() => handleRemoveCustomSocialLink(link.id)}
                        color="error"
                        size="small"
                      >
                        <X size={16} />
                      </IconButton>
                    </Box>
                  </Paper>
                ))}
              </Box>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default SocialTab;
