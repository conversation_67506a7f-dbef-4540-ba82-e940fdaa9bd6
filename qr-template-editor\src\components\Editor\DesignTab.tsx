import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON>po<PERSON>,
  Box,
  Button,
  Grid,
  TextField,
  Alert,
  Chip,
  Paper
} from '@mui/material';
import { Palette, Wand2, Eye } from 'lucide-react';
import type { BusinessData, TemplateType } from '../../types/index';
import HeaderCustomization from './HeaderCustomization';

interface DesignTabProps {
  businessData: BusinessData;
  onUpdate: (field: keyof BusinessData, value: any) => void;
  onApplyTemplate: (template: TemplateType) => void;
}

const templates = [
  {
    id: 'modern' as TemplateType,
    name: 'Modern',
    description: 'Clean and contemporary design',
    colors: { primary: '#3B82F6', secondary: '#1E40AF' },
    preview: '🔵'
  },
  {
    id: 'luxury' as TemplateType,
    name: 'Luxury',
    description: 'Elegant and sophisticated',
    colors: { primary: '#D4AF37', secondary: '#B8860B' },
    preview: '🟡'
  },
  {
    id: 'minimal' as TemplateType,
    name: 'Minimal',
    description: 'Simple and clean',
    colors: { primary: '#6B7280', secondary: '#374151' },
    preview: '⚫'
  },
  {
    id: 'cute' as TemplateType,
    name: 'Cute',
    description: 'Fun and playful',
    colors: { primary: '#EC4899', secondary: '#BE185D' },
    preview: '🩷'
  }
];

const colorPresets = [
  { name: 'Blue', primary: '#3B82F6', secondary: '#1E40AF' },
  { name: 'Gold', primary: '#D4AF37', secondary: '#B8860B' },
  { name: 'Green', primary: '#10B981', secondary: '#047857' },
  { name: 'Purple', primary: '#8B5CF6', secondary: '#7C3AED' },
  { name: 'Pink', primary: '#EC4899', secondary: '#BE185D' },
  { name: 'Red', primary: '#EF4444', secondary: '#DC2626' },
];

const DesignTab: React.FC<DesignTabProps> = ({ businessData, onUpdate, onApplyTemplate }) => {
  const [showCustomCSS, setShowCustomCSS] = React.useState(false);

  const handleTemplateSelect = (template: TemplateType) => {
    onApplyTemplate(template);
  };

  const handleColorPresetSelect = (preset: typeof colorPresets[0]) => {
    onUpdate('primaryColor', preset.primary);
    onUpdate('secondaryColor', preset.secondary);
  };

  return (
    <>
      {/* Header Customization */}
      <HeaderCustomization businessData={businessData} onUpdate={onUpdate} />

      <Card sx={{ mb: 3 }}>
        <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              bgcolor: 'secondary.main',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            <Palette size={20} color="white" />
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Design & Styling
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Customize the look and feel of your page
            </Typography>
          </Box>
        </Box>

        {/* Template Selection */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Choose Template
          </Typography>
          <Grid container spacing={2}>
            {templates.map((template) => (
              <Grid size={{ xs: 12, sm: 6, md: 3 }} key={template.id}>
                <Paper
                  sx={{
                    p: 2,
                    cursor: 'pointer',
                    border: businessData.template === template.id ? 2 : 1,
                    borderColor: businessData.template === template.id ? 'primary.main' : 'divider',
                    '&:hover': { borderColor: 'primary.main' },
                    transition: 'border-color 0.2s',
                  }}
                  onClick={() => handleTemplateSelect(template.id)}
                >
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ mb: 1 }}>
                      {template.preview}
                    </Typography>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {template.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {template.description}
                    </Typography>
                    {businessData.template === template.id && (
                      <Chip
                        label="Selected"
                        size="small"
                        color="primary"
                        sx={{ mt: 1 }}
                      />
                    )}
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Color Customization */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Colors
          </Typography>
          
          {/* Color Presets */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Quick Presets
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {colorPresets.map((preset) => (
                <Button
                  key={preset.name}
                  variant="outlined"
                  size="small"
                  onClick={() => handleColorPresetSelect(preset)}
                  sx={{
                    minWidth: 'auto',
                    px: 2,
                    py: 1,
                    borderColor: preset.primary,
                    color: preset.primary,
                    '&:hover': {
                      borderColor: preset.primary,
                      bgcolor: `${preset.primary}10`,
                    },
                  }}
                >
                  <Box
                    sx={{
                      width: 16,
                      height: 16,
                      bgcolor: preset.primary,
                      borderRadius: '50%',
                      mr: 1,
                    }}
                  />
                  {preset.name}
                </Button>
              ))}
            </Box>
          </Box>

          {/* Custom Colors */}
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                label="Primary Color"
                type="color"
                value={businessData.primaryColor}
                onChange={(e) => onUpdate('primaryColor', e.target.value)}
                fullWidth
                InputProps={{
                  startAdornment: (
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        bgcolor: businessData.primaryColor,
                        borderRadius: 1,
                        mr: 1,
                        border: '1px solid #ccc',
                      }}
                    />
                  ),
                }}
              />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                label="Secondary Color"
                type="color"
                value={businessData.secondaryColor}
                onChange={(e) => onUpdate('secondaryColor', e.target.value)}
                fullWidth
                InputProps={{
                  startAdornment: (
                    <Box
                      sx={{
                        width: 24,
                        height: 24,
                        bgcolor: businessData.secondaryColor,
                        borderRadius: 1,
                        mr: 1,
                        border: '1px solid #ccc',
                      }}
                    />
                  ),
                }}
              />
            </Grid>
          </Grid>
        </Box>

        {/* Custom CSS */}
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              Custom CSS
            </Typography>
            <Button
              variant="outlined"
              size="small"
              startIcon={<Eye size={16} />}
              onClick={() => setShowCustomCSS(!showCustomCSS)}
            >
              {showCustomCSS ? 'Hide' : 'Show'} CSS Editor
            </Button>
          </Box>

          {showCustomCSS && (
            <>
              <Alert severity="warning" sx={{ mb: 2 }}>
                Advanced feature: Add custom CSS to override default styles. Use with caution.
              </Alert>
              <TextField
                label="Custom CSS"
                value={businessData.customCSS || ''}
                onChange={(e) => onUpdate('customCSS', e.target.value)}
                fullWidth
                multiline
                rows={8}
                placeholder="/* Add your custom CSS here */&#10;.business-name {&#10;  font-family: 'Arial', sans-serif;&#10;  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);&#10;}"
                sx={{
                  '& .MuiInputBase-input': {
                    fontFamily: 'monospace',
                    fontSize: '0.875rem',
                  },
                }}
              />
            </>
          )}
        </Box>
      </CardContent>
    </Card>
    </>
  );
};

export default DesignTab;
