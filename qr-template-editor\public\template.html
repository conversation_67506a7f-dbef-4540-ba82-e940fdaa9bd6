<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{PAGE_TITLE}}</title>
    {{#if FAVICON_URL}}
    <link rel="icon" type="image/webp" href="{{FAVICON_URL}}">
    <link rel="shortcut icon" type="image/webp" href="{{FAVICON_URL}}">
    {{/if}}
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config = { theme: { extend: { colors: { primary: '#d4af37', secondary: '#222222' }, borderRadius: { 'none': '0px', 'sm': '4px', DEFAULT: '8px', 'md': '12px', 'lg': '16px', 'xl': '20px', '2xl': '24px', '3xl': '32px', 'full': '9999px', 'button': '8px' } } } }</script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        body {
            font-family: 'Playfair Display', serif;
            background-color: #f9f5eb;
        }

        .gold-gradient {
            background: linear-gradient(135deg, #d4af37 0%, #f9e076 50%, #d4af37 100%);
        }

        /* Modern Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .animate-slideInLeft {
            animation: slideInLeft 0.6s ease-out forwards;
        }

        .animate-pulse-slow {
            animation: pulse 2s ease-in-out infinite;
        }

        .shimmer {
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            background-size: 200px 100%;
            animation: shimmer 2s infinite;
        }

        .hover-scale {
            transition: all 0.3s ease;
        }

        .hover-scale:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stagger-animation > * {
            opacity: 0;
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
        .stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
        .stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
        .stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }

        /* Template Specific Styles */
        
                .template-modern .gold-gradient {
                    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 50%, #3B82F6 100%);
                }
                .template-modern .hover-scale:hover {
                    transform: scale(1.05) rotate(1deg);
                }
            

        /* Custom CSS */

        /* Custom Social Icons */
        .custom-social-icon {
            background-color: #1976d2; /* Default color */
        }

    </style>
</head>

<body class="bg-white">
    <!-- Main Content -->
    <main>
        <!-- Enhanced Header Banner -->
        <div class="relative w-full h-48 overflow-hidden header-background">
            <!-- Overlay for better text readability on images -->
            <div class="absolute inset-0 bg-black bg-opacity-30 header-overlay"></div>

            <div class="absolute inset-0 flex flex-col items-center justify-center text-center px-4 header-content">
                {{#if HAS_LOGO}}
                <!-- Logo Display -->
                <img src="{{LOGO_URL}}" alt="{{BUSINESS_NAME}} Logo" class="h-24 md:h-28 mb-2 animate-pulse-slow drop-shadow-lg">
                {{/if}}

                {{#if HAS_NO_LOGO}}
                <!-- Text Logo when no image -->
                <h1 class="text-3xl font-bold mb-2 tracking-wider animate-fadeInUp drop-shadow-lg header-text">{{BUSINESS_NAME}}</h1>
                {{/if}}

                {{#if SLOGAN}}
                <!-- Slogan -->
                <p class="text-lg opacity-90 animate-fadeInUp drop-shadow-md header-text">{{SLOGAN}}</p>
                {{/if}}
            </div>
        </div>

        <!-- Connect Section -->
        <div class="px-5 py-6 bg-white">
            <h2 class="text-2xl font-semibold text-secondary mb-2 animate-slideInLeft">Connect with us</h2>
            <p class="text-gray-600 mb-6 animate-fadeInUp">Follow us and get updates delivered to your favorite social media channel. Stay informed about our latest promotions and nail art trends.</p>
            
            <!-- Social Media Links -->
            <div class="space-y-4 stagger-animation">
                {{#if FACEBOOK_URL}}
                <!-- Facebook -->
                <a href="{{FACEBOOK_URL}}" target="_blank" class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-[#1877F2] rounded-full mr-4">
                        <i class="ri-facebook-fill ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Facebook</h3>
                        <p class="text-sm text-gray-500">Visit our Facebook page</p>
                    </div>
                </a>
                {{/if}}

                {{#if INSTAGRAM_URL}}
                <!-- Instagram -->
                <a href="{{INSTAGRAM_URL}}" target="_blank" class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mr-4">
                        <i class="ri-instagram-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Instagram</h3>
                        <p class="text-sm text-gray-500">Follow us on Instagram</p>
                    </div>
                </a>
                {{/if}}

                {{#if YELP_URL}}
                <!-- Yelp Review -->
                <a href="{{YELP_URL}}" target="_blank" class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-[#D32323] rounded-full mr-4">
                        <i class="ri-yelp-fill ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Yelp Review</h3>
                        <p class="text-sm text-gray-500">Review us on Yelp</p>
                    </div>
                </a>
                {{/if}}

                {{#if GOOGLE_MAPS_URL}}
                <!-- Google Review -->
                <a href="{{GOOGLE_MAPS_URL}}" target="_blank" class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-white rounded-full mr-4 border border-gray-200">
                        <i class="ri-google-fill ri-lg" style="color: #4285F4;"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Google Review</h3>
                        <p class="text-sm text-gray-500">Review us on Google</p>
                    </div>
                </a>
                {{/if}}

                <!-- Custom Social Links -->
                {{#each CUSTOM_SOCIAL_LINKS}}
                <a href="{{this.url}}" target="_blank" class="flex items-center p-3 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center rounded-full mr-4 border border-gray-200 custom-social-icon" data-color="{{this.color}}">
                        <i class="ri-{{this.icon}}-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">{{this.name}}</h3>
                        <p class="text-sm text-gray-500">Visit our {{this.name}}</p>
                    </div>
                </a>
                {{/each}}
            </div>
        </div>

        <!-- Services Section -->
        {{#if HAS_SERVICES}}
        <div class="px-5 py-6 bg-white">
            <h2 class="text-2xl font-semibold text-secondary mb-6 animate-slideInLeft">Our Services</h2>
            <div class="space-y-4 stagger-animation">
                {{#each SERVICES}}
                <div class="bg-gray-50 p-4 rounded-lg hover-scale">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-medium text-secondary">{{this.name}}</h3>
                        <span class="text-primary font-semibold">{{this.price}}</span>
                    </div>
                    <p class="text-sm text-gray-600">{{this.description}}</p>
                    {{#if this.duration}}
                    <p class="text-xs text-gray-500 mt-1">Duration: {{this.duration}}</p>
                    {{/if}}
                </div>
                {{/each}}
            </div>
        </div>
        {{/if}}
        

        <!-- Special Offers Section -->
        {{#if HAS_OFFERS}}
        <div class="px-5 py-6 bg-gradient-to-r from-purple-50 to-pink-50">
            <h2 class="text-2xl font-semibold text-secondary mb-6 animate-slideInLeft">
                <i class="ri-gift-line mr-2"></i>Special Offers
            </h2>
            <div class="space-y-4 stagger-animation">
                {{#each OFFERS}}
                <div class="bg-white p-4 rounded-lg border border-purple-200 hover-scale">
                    <h3 class="font-medium text-secondary mb-2">{{this.title}}</h3>
                    {{#if this.discount}}
                    <p class="text-sm text-purple-600 font-semibold">{{this.discount}}</p>
                    {{/if}}
                    {{#if this.validUntil}}
                    <p class="text-xs text-purple-600">Valid until: {{this.validUntil}}</p>
                    {{/if}}
                    <p class="text-sm text-gray-600 mb-2">{{this.description}}</p>
                </div>
                {{/each}}
            </div>
        </div>
        {{/if}}
        

        <!-- Gallery Section -->
        {{#if HAS_GALLERY}}
        <div class="px-5 py-6 bg-white">
            <h2 class="text-2xl font-semibold text-secondary mb-6 animate-slideInLeft">
                <i class="ri-image-line mr-2"></i>Gallery
            </h2>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 stagger-animation">
                {{#each GALLERY_IMAGES}}
                <div class="overflow-hidden rounded-lg shadow-md">
                    <img
                        src="{{this.url}}"
                        alt="Gallery image {{this.index}}"
                        class="w-full h-32 md:h-40 object-cover"
                        loading="lazy"
                    />
                </div>
                {{/each}}
            </div>
        </div>
        {{/if}}

        <!-- Action Buttons -->
        <div class="px-5 py-6 bg-gray-50">
            <h2 class="text-2xl font-semibold text-secondary mb-6 animate-slideInLeft">Quick Actions</h2>
            <div class="stagger-animation">
                {{#if WEBSITE_URL}}
                <!-- Visit Website -->
                <a href="{{WEBSITE_URL}}" target="_blank" class="flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4">
                        <i class="ri-global-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Visit our website</h3>
                        <p class="text-sm text-gray-500">Learn more about us</p>
                    </div>
                </a>
                {{/if}}

                {{#if BOOKING_URL}}
                <!-- Book Appointment -->
                <a href="{{BOOKING_URL}}" target="_blank" class="flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4">
                        <i class="ri-calendar-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Book an appointment</h3>
                        <p class="text-sm text-gray-500">Online booking available</p>
                    </div>
                </a>
                {{/if}}

                <!-- Call Now -->
                <a href="tel:{{PHONE_CLEAN}}" class="flex items-center p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer hover-scale">
                    <div class="w-10 h-10 flex items-center justify-center bg-primary rounded-full mr-4">
                        <i class="ri-phone-line ri-lg text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Call us now</h3>
                        <p class="text-sm text-gray-500">{{PHONE}}</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Contact Info -->
        <div class="px-5 py-6 bg-gray-50">
            <h2 class="text-2xl font-semibold text-secondary mb-4 animate-slideInLeft">Visit Us</h2>
            <div class="bg-white p-4 rounded-lg shadow-sm mb-4 animate-fadeInUp hover-scale">
                <div class="flex items-start mb-3">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                        <i class="ri-map-pin-line text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Address</h3>
                        <p class="text-gray-600">{{ADDRESS_HTML}}</p>
                    </div>
                </div>
                <div class="flex items-start mb-3">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                        <i class="ri-time-line text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Hours</h3>
                        <p class="text-gray-600">{{HOURS_HTML}}</p>
                    </div>
                </div>
                <div class="flex items-start mb-3">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                        <i class="ri-phone-line text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Phone</h3>
                        <p class="text-gray-600">{{PHONE}}</p>
                    </div>
                </div>
                {{#if EMAIL}}
                <div class="flex items-start">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary rounded-full mr-3 mt-1">
                        <i class="ri-mail-line text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-secondary">Email</h3>
                        <p class="text-gray-600">
                            <a href="mailto:{{EMAIL}}" class="text-primary hover:underline">{{EMAIL}}</a>
                        </p>
                    </div>
                </div>
                {{/if}}
            </div>

            {{#if GOOGLE_MAPS_URL}}
            <a href="{{GOOGLE_MAPS_URL}}" target="_blank" class="w-full mt-4 py-3 bg-primary text-white font-medium rounded-button flex items-center justify-center cursor-pointer hover-scale">
                <i class="ri-navigation-line ri-lg mr-2"></i>
                <span>Get Directions</span>
            </a>
            {{/if}}
        </div>
    </main>



    <!-- Custom Social Colors JavaScript -->
    <script>
        // Initialize custom social colors
        function initializeCustomSocialColors() {
            const customSocialIcons = document.querySelectorAll('.custom-social-icon');
            customSocialIcons.forEach(icon => {
                const color = icon.dataset.color;
                if (color && color !== 'undefined') {
                    icon.style.backgroundColor = color;
                }
            });
        }

        // Initialize header styles
        function initializeHeaderStyles() {
            const headerBackground = document.querySelector('.header-background');
            const headerText = document.querySelectorAll('.header-text');
            const headerContent = document.querySelector('.header-content');
            const headerOverlay = document.querySelector('.header-overlay');

            // Header style data (will be replaced by template engine)
            const headerBackgroundStyle = `{{HEADER_BACKGROUND_STYLE}}`;
            const headerTextColor = `{{HEADER_TEXT_COLOR}}`;

            // Track if styles were actually applied to reduce console spam
            let stylesApplied = false;

            if (headerBackground && headerBackgroundStyle) {
                // Check if style is different before applying
                const currentStyle = headerBackground.style.cssText;
                if (!currentStyle.includes(headerBackgroundStyle)) {
                    headerBackground.style.cssText += headerBackgroundStyle;
                    console.log('🎨 Applied header background:', headerBackgroundStyle.substring(0, 50) + '...');
                    stylesApplied = true;
                }
            }

            if (headerContent && headerTextColor) {
                // Apply header text color
                headerContent.style.color = headerTextColor;
            }

            headerText.forEach(element => {
                if (headerTextColor) {
                    element.style.color = headerTextColor;
                }
            });

            // Show/hide overlay based on background type (only log if styles were applied)
            if (headerOverlay && stylesApplied) {
                if (headerBackgroundStyle && headerBackgroundStyle.includes('url(')) {
                    headerOverlay.style.display = 'block';
                    console.log('🖼️ Showing overlay for background image');
                } else {
                    headerOverlay.style.display = 'none';
                    console.log('🎨 Hiding overlay for solid/gradient background');
                }
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔄 Initializing styles...');
            initializeCustomSocialColors();
            initializeHeaderStyles();
            console.log('✅ Styles initialized');
        });
    </script>
</body>
</html>