/**
 * Utility functions for handling base64 images
 */

export interface Base64ImageInfo {
  isValid: boolean;
  mimeType?: string;
  size?: number;
  width?: number;
  height?: number;
}

/**
 * Validate if a string is a valid base64 data URL
 */
export const isValidBase64Image = (dataUrl: string): boolean => {
  if (!dataUrl || typeof dataUrl !== 'string') {
    return false;
  }

  // Check if it's a data URL
  const dataUrlPattern = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/i;
  return dataUrlPattern.test(dataUrl);
};

/**
 * Get information about a base64 image
 */
export const getBase64ImageInfo = (dataUrl: string): Promise<Base64ImageInfo> => {
  return new Promise((resolve) => {
    if (!isValidBase64Image(dataUrl)) {
      resolve({ isValid: false });
      return;
    }

    const img = new Image();
    img.onload = () => {
      // Calculate approximate size (base64 is ~33% larger than binary)
      const base64Data = dataUrl.split(',')[1];
      const binarySize = (base64Data.length * 3) / 4;
      
      resolve({
        isValid: true,
        mimeType: dataUrl.match(/data:image\/([^;]+)/)?.[1],
        size: binarySize,
        width: img.width,
        height: img.height
      });
    };
    
    img.onerror = () => {
      resolve({ isValid: false });
    };
    
    img.src = dataUrl;
  });
};

/**
 * Compress base64 image if it's too large
 */
export const compressBase64Image = (
  dataUrl: string, 
  maxSizeKB: number = 500,
  quality: number = 0.8
): Promise<string> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions to reduce file size
      let { width, height } = img;
      const maxDimension = 1200; // Max width or height

      if (width > maxDimension || height > maxDimension) {
        if (width > height) {
          height = (height * maxDimension) / width;
          width = maxDimension;
        } else {
          width = (width * maxDimension) / height;
          height = maxDimension;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      // Try different quality levels to meet size requirement
      let compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
      
      // Check size and reduce quality if needed
      const sizeKB = (compressedDataUrl.length * 3) / 4 / 1024;
      if (sizeKB > maxSizeKB && quality > 0.1) {
        // Recursively compress with lower quality
        compressBase64Image(dataUrl, maxSizeKB, quality - 0.1).then(resolve);
      } else {
        resolve(compressedDataUrl);
      }
    };

    img.src = dataUrl;
  });
};

/**
 * Convert file to base64 with compression
 */
export const fileToBase64 = (
  file: File, 
  maxSizeKB: number = 500
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = async (e) => {
      const dataUrl = e.target?.result as string;
      
      try {
        // Check if compression is needed
        const info = await getBase64ImageInfo(dataUrl);
        const sizeKB = (info.size || 0) / 1024;
        
        if (sizeKB > maxSizeKB) {
          // Compress the image
          const compressed = await compressBase64Image(dataUrl, maxSizeKB);
          resolve(compressed);
        } else {
          resolve(dataUrl);
        }
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
