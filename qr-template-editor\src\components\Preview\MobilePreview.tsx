import React from 'react';
import { Box, Paper, Typography } from '@mui/material';
import type { BusinessData } from '../../types/index';
import { TemplateEngine, convertBusinessDataToTemplateData } from '../../utils/templateEngine';

interface MobilePreviewProps {
  businessData: BusinessData;
  device: 'mobile' | 'tablet' | 'desktop';
}

const MobilePreview: React.FC<MobilePreviewProps> = ({ businessData, device }) => {
  const [htmlContent, setHtmlContent] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(true);
  const templateEngine = React.useRef(new TemplateEngine());

  React.useEffect(() => {
    const generatePreview = async () => {
      try {
        console.log('🔄 Preview: Regenerating preview for businessData change');
        console.log('📊 Preview: Logo URL:', businessData.logoUrl ? 'Present' : 'None');
        console.log('📊 Preview: Favicon URL:', businessData.faviconUrl ? 'Present' : 'None');
        setIsLoading(true);

        // Load template if not already loaded
        if (!templateEngine.current.template) {
          await templateEngine.current.loadTemplate();
        }

        // Convert business data to template data
        const templateData = convertBusinessDataToTemplateData(businessData);
        console.log('📊 Preview: Template data HAS_LOGO:', templateData.HAS_LOGO);

        // Render HTML
        const html = templateEngine.current.render(templateData);
        setHtmlContent(html);
        console.log('✅ Preview: HTML updated');
      } catch (error) {
        console.error('Failed to generate preview:', error);
        setHtmlContent('<div style="padding: 20px; text-align: center; color: red;">Failed to load preview</div>');
      } finally {
        setIsLoading(false);
      }
    };

    generatePreview();
  }, [businessData]);

  const getDeviceStyles = () => {
    switch (device) {
      case 'mobile':
        return {
          width: 375,
          height: 667,
          maxHeight: '80vh',
        };
      case 'tablet':
        return {
          width: 768,
          height: 1024,
          maxHeight: '80vh',
        };
      case 'desktop':
        return {
          width: '100%',
          height: '80vh',
          maxWidth: 1200,
        };
      default:
        return {
          width: 375,
          height: 667,
          maxHeight: '80vh',
        };
    }
  };

  const deviceStyles = getDeviceStyles();

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 2 }}>
      <Typography variant="h6" gutterBottom sx={{ textTransform: 'capitalize' }}>
        {device} Preview {isLoading && '(Loading...)'}
      </Typography>

      <Paper
        elevation={3}
        sx={{
          ...deviceStyles,
          overflow: 'hidden',
          border: '8px solid #333',
          borderRadius: device === 'mobile' ? '25px' : '8px',
          position: 'relative',
          '&::before': device === 'mobile' ? {
            content: '""',
            position: 'absolute',
            top: 10,
            left: '50%',
            transform: 'translateX(-50%)',
            width: 60,
            height: 4,
            bgcolor: '#666',
            borderRadius: 2,
            zIndex: 10,
          } : {},
        }}
      >
        {isLoading ? (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: '#f5f5f5'
            }}
          >
            <Typography variant="body2" color="text.secondary">
              Loading template...
            </Typography>
          </Box>
        ) : (
          <iframe
            key={`preview-${businessData.logoUrl}-${businessData.faviconUrl}-${Date.now()}`}
            srcDoc={htmlContent}
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              borderRadius: device === 'mobile' ? '17px' : '0px',
            }}
            title="Preview"
          />
        )}
      </Paper>

      <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
        {device === 'mobile' && '375 × 667px'}
        {device === 'tablet' && '768 × 1024px'}
        {device === 'desktop' && 'Responsive'}
      </Typography>
    </Box>
  );
};

export default MobilePreview;
