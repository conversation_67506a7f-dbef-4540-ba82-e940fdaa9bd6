import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Grid,
  Alert,
  Chip
} from '@mui/material';
import { Palette, Image, Zap } from 'lucide-react';
import type { BusinessData } from '../../types/index';

interface HeaderCustomizationProps {
  businessData: BusinessData;
  onUpdate: (field: keyof BusinessData, value: string) => void;
}

const HeaderCustomization: React.FC<HeaderCustomizationProps> = ({ businessData, onUpdate }) => {
  const gradientPresets = [
    { name: 'Ocean Blue', value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
    { name: 'Sunset', value: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' },
    { name: 'Forest', value: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' },
    { name: 'Purple Rain', value: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' },
    { name: 'Golden Hour', value: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' },
    { name: 'Night Sky', value: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)' },
    { name: 'Rose Gold', value: 'linear-gradient(135deg, #f7971e 0%, #ffd200 100%)' },
    { name: 'Mint Fresh', value: 'linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%)' }
  ];

  const colorPresets = [
    { name: 'Primary Blue', value: '#1976d2' },
    { name: 'Purple', value: '#9c27b0' },
    { name: 'Teal', value: '#009688' },
    { name: 'Orange', value: '#ff9800' },
    { name: 'Red', value: '#f44336' },
    { name: 'Green', value: '#4caf50' },
    { name: 'Pink', value: '#e91e63' },
    { name: 'Indigo', value: '#3f51b5' }
  ];

  const handleBackgroundTypeChange = (type: 'color' | 'gradient' | 'image') => {
    onUpdate('headerBackgroundType', type);
    
    // Set default values based on type
    if (type === 'color') {
      onUpdate('headerBackgroundValue', '#1976d2');
    } else if (type === 'gradient') {
      onUpdate('headerBackgroundValue', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
    } else if (type === 'image') {
      onUpdate('headerBackgroundValue', '');
    }
  };

  const handlePresetSelect = (value: string) => {
    onUpdate('headerBackgroundValue', value);
  };

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              bgcolor: 'primary.main',
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            <Palette size={20} color="white" />
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Header Customization
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Customize your header background and text colors
            </Typography>
          </Box>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          Customize your header with solid colors, gradients, or background images. Make sure text remains readable!
        </Alert>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Background Type Selection */}
          <FormControl fullWidth>
            <InputLabel>Background Type</InputLabel>
            <Select
              value={businessData.headerBackgroundType}
              label="Background Type"
              onChange={(e) => handleBackgroundTypeChange(e.target.value as 'color' | 'gradient' | 'image')}
            >
              <MenuItem value="color">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Palette size={16} style={{ marginRight: 8 }} />
                  Solid Color
                </Box>
              </MenuItem>
              <MenuItem value="gradient">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Zap size={16} style={{ marginRight: 8 }} />
                  Gradient
                </Box>
              </MenuItem>
              <MenuItem value="image">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Image size={16} style={{ marginRight: 8 }} />
                  Background Image
                </Box>
              </MenuItem>
            </Select>
          </FormControl>

          {/* Color Presets */}
          {businessData.headerBackgroundType === 'color' && (
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>Color Presets:</Typography>
              <Grid container spacing={1}>
                {colorPresets.map((preset) => (
                  <Grid item key={preset.name}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handlePresetSelect(preset.value)}
                      sx={{
                        minWidth: 'auto',
                        width: 40,
                        height: 40,
                        bgcolor: preset.value,
                        border: businessData.headerBackgroundValue === preset.value ? '2px solid #000' : '1px solid #ccc',
                        '&:hover': {
                          bgcolor: preset.value,
                          opacity: 0.8
                        }
                      }}
                      title={preset.name}
                    />
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Gradient Presets */}
          {businessData.headerBackgroundType === 'gradient' && (
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>Gradient Presets:</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {gradientPresets.map((preset) => (
                  <Chip
                    key={preset.name}
                    label={preset.name}
                    onClick={() => handlePresetSelect(preset.value)}
                    variant={businessData.headerBackgroundValue === preset.value ? 'filled' : 'outlined'}
                    sx={{
                      background: businessData.headerBackgroundValue === preset.value ? preset.value : 'transparent',
                      color: businessData.headerBackgroundValue === preset.value ? 'white' : 'inherit',
                      cursor: 'pointer'
                    }}
                  />
                ))}
              </Box>
            </Box>
          )}

          {/* Background Value Input */}
          <TextField
            label={
              businessData.headerBackgroundType === 'color' ? 'Color (Hex)' :
              businessData.headerBackgroundType === 'gradient' ? 'Gradient CSS' :
              'Image URL'
            }
            value={businessData.headerBackgroundValue}
            onChange={(e) => onUpdate('headerBackgroundValue', e.target.value)}
            fullWidth
            placeholder={
              businessData.headerBackgroundType === 'color' ? '#1976d2' :
              businessData.headerBackgroundType === 'gradient' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' :
              'https://example.com/header-bg.jpg'
            }
            helperText={
              businessData.headerBackgroundType === 'color' ? 'Enter a hex color code' :
              businessData.headerBackgroundType === 'gradient' ? 'Enter CSS gradient syntax' :
              'Enter a URL to a background image'
            }
            type={businessData.headerBackgroundType === 'color' ? 'color' : 'text'}
          />

          {/* Text Color */}
          <TextField
            label="Text Color"
            value={businessData.headerTextColor}
            onChange={(e) => onUpdate('headerTextColor', e.target.value)}
            fullWidth
            type="color"
            placeholder="#ffffff"
            helperText="Choose a color that contrasts well with your background"
          />

          {/* Preview */}
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>Preview:</Typography>
            <Box
              sx={{
                height: 120,
                borderRadius: 2,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                overflow: 'hidden',
                background: businessData.headerBackgroundType === 'color' 
                  ? businessData.headerBackgroundValue
                  : businessData.headerBackgroundType === 'gradient'
                  ? businessData.headerBackgroundValue
                  : `url('${businessData.headerBackgroundValue}') center/cover`,
                color: businessData.headerTextColor
              }}
            >
              {businessData.headerBackgroundType === 'image' && (
                <Box sx={{ position: 'absolute', inset: 0, bgcolor: 'black', opacity: 0.3 }} />
              )}
              <Typography variant="h6" sx={{ position: 'relative', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>
                {businessData.businessName}
              </Typography>
              {businessData.slogan && (
                <Typography variant="body2" sx={{ position: 'relative', opacity: 0.9, textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}>
                  {businessData.slogan}
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default HeaderCustomization;
