import React from 'react';
import {
  Box,
  Button,
  Typography,
  Avatar,
  IconButton,
  Alert
} from '@mui/material';
import { Upload, X } from 'lucide-react';
import { uploadImage, IMAGE_PRESETS, type ImageUploadResult } from '../../utils/imageUtils';

interface SimpleImageUploadProps {
  label: string;
  description?: string;
  currentImage?: string;
  onImageUpload: (result: ImageUploadResult) => void;
  onImageRemove: () => void;
  size?: number;
}

const SimpleImageUpload: React.FC<SimpleImageUploadProps> = ({
  label,
  description,
  currentImage,
  onImageUpload,
  onImageRemove,
  size = 80
}) => {
  // 🔄 Force re-render state
  const [imageKey, setImageKey] = React.useState(0);

  // Debug: Track currentImage changes
  React.useEffect(() => {
    console.log(`🖼️ ${label}: currentImage changed to:`, currentImage ? 'Present' : 'None');
    // Force Avatar re-render when currentImage changes
    setImageKey(prev => prev + 1);
  }, [currentImage, label]);
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        // Use the same uploadImage function as the original ImageUpload component
        const options = label.toLowerCase().includes('favicon')
          ? IMAGE_PRESETS.favicon
          : IMAGE_PRESETS.logo;

        console.log(`📸 ${label}: Processing image with options:`, options);
        const result = await uploadImage(file, options);
        console.log(`📸 ${label}: Processed - ${result.width}x${result.height}, ${result.sizeKB}KB`);

        onImageUpload(result);
      } catch (error) {
        console.error(`Failed to process ${label.toLowerCase()}:`, error);
        alert(`Failed to process ${label.toLowerCase()}. Please try a different file.`);
      }
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {label}
      </Typography>
      
      {description && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {description}
        </Typography>
      )}

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        {/* Image Preview */}
        <Box sx={{ position: 'relative' }}>
          <Avatar
            key={`${label}-${currentImage || 'empty'}-${imageKey}`}
            src={currentImage}
            sx={{
              width: size,
              height: size,
              bgcolor: 'grey.100',
              border: '2px dashed',
              borderColor: currentImage ? 'transparent' : 'grey.300'
            }}
          >
            {!currentImage && <Upload size={size / 3} color="#666" />}
          </Avatar>
          
          {/* Remove Button */}
          {currentImage && (
            <IconButton
              size="small"
              onClick={onImageRemove}
              sx={{
                position: 'absolute',
                top: -8,
                right: -8,
                bgcolor: 'error.main',
                color: 'white',
                width: 24,
                height: 24,
                '&:hover': {
                  bgcolor: 'error.dark'
                }
              }}
            >
              <X size={14} />
            </IconButton>
          )}
        </Box>

        {/* Upload Button */}
        <Box>
          <input
            accept="image/*"
            style={{ display: 'none' }}
            id={`${label.toLowerCase().replace(/\s+/g, '-')}-upload`}
            type="file"
            onChange={handleFileSelect}
          />
          <label htmlFor={`${label.toLowerCase().replace(/\s+/g, '-')}-upload`}>
            <Button
              variant="outlined"
              component="span"
              startIcon={<Upload size={16} />}
              sx={{ mr: 1 }}
            >
              {currentImage ? 'Change' : 'Upload'}
            </Button>
          </label>
          
          {currentImage && (
            <Button
              variant="text"
              color="error"
              onClick={onImageRemove}
              size="small"
            >
              Remove
            </Button>
          )}
        </Box>
      </Box>

      {/* File Requirements */}
      <Alert severity="info" sx={{ mt: 2 }}>
        <Typography variant="caption">
          Supported formats: JPEG, PNG, WebP • Max size: 2MB (stored as base64)
        </Typography>
      </Alert>
    </Box>
  );
};

export default SimpleImageUpload;
