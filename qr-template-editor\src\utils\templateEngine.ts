import type { BusinessData, Service, Offer } from '../types/index';

export interface TemplateData {
  // Basic Info
  PAGE_TITLE: string;
  BUSINESS_NAME: string;
  SLOGAN?: string;
  HAS_LOGO: boolean;
  HAS_NO_LOGO: boolean;
  LOGO_URL?: string;
  FAVICON_URL?: string;

  // Header Customization
  HEADER_BACKGROUND_STYLE: string;
  HEADER_TEXT_COLOR: string;
  
  // Contact
  PHONE: string;
  PHONE_CLEAN: string;
  EMAIL?: string;
  ADDRESS: string;
  ADDRESS_HTML: string;
  HOURS: string;
  HOURS_HTML: string;
  
  // Social Media
  HAS_SOCIAL: boolean;
  FACEBOOK_URL?: string;
  INSTAGRAM_URL?: string;
  YELP_URL?: string;
  WEBSITE_URL?: string;
  GOOGLE_MAPS_URL?: string;
  BOOKING_URL?: string;
  CUSTOM_SOCIAL_LINKS: Array<{ name: string; url: string; icon: string; color?: string }>;
  
  // Template & Styling
  TEMPLATE_TYPE: string;
  PRIMARY_COLOR: string;
  SECONDARY_COLOR: string;
  CUSTOM_CSS?: string;
  
  // Services & Gallery
  SERVICES: Service[];
  HAS_SERVICES: boolean;
  GALLERY_IMAGES: Array<{ url: string; index: number }>;
  HAS_GALLERY: boolean;
  OFFERS: Offer[];
  HAS_OFFERS: boolean;
}

export class TemplateEngine {
  public template: string = '';

  async loadTemplate(): Promise<void> {
    try {
      const response = await fetch('/template.html');
      if (!response.ok) {
        throw new Error(`Failed to load template: ${response.status}`);
      }
      this.template = await response.text();
    } catch (error) {
      console.error('Failed to load template:', error);
      this.template = this.getFallbackTemplate();
    }
  }

  render(data: TemplateData): string {
    let html = this.template;

    console.log('Template Engine - Input data:', {
      HAS_SERVICES: data.HAS_SERVICES,
      SERVICES: data.SERVICES,
      HAS_OFFERS: data.HAS_OFFERS,
      OFFERS: data.OFFERS
    });

    // Process in correct order
    html = this.processAllTemplateLogic(html, data);

    return html;
  }

  private processAllTemplateLogic(template: string, data: TemplateData): string {
    let html = template;
    let iterations = 0;
    const maxIterations = 20;

    // Keep processing until no more template syntax found
    while (iterations < maxIterations && this.hasTemplateLogic(html)) {
      const beforeHtml = html;

      // Process each loops (all at once)
      html = this.processAllEachLoops(html, data);

      // Process if conditionals (all at once)
      html = this.processAllIfConditionals(html, data);

      // Process variables (all at once)
      html = this.processAllVariables(html, data);

      // If no changes made, break to avoid infinite loop
      if (html === beforeHtml) {
        console.warn('Template processing stuck, breaking loop');
        break;
      }

      iterations++;
    }

    console.log(`Template processed in ${iterations} iterations`);

    // Process CSS variables last
    html = this.processCSSVariables(html, data);

    return html;
  }

  private hasTemplateLogic(html: string): boolean {
    return /\{\{[^}]+\}\}/.test(html);
  }
  
  private processAllEachLoops(template: string, data: TemplateData): string {
    const eachRegex = /\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g;

    return template.replace(eachRegex, (_fullMatch, arrayName, content) => {
      const array = (data as any)[arrayName];

      if (!Array.isArray(array)) {
        return '';
      }

      const renderedItems = array.map(item => {
        let itemHtml = content;

        // Replace {{this.property}} variables first
        Object.keys(item).forEach(key => {
          const regex = new RegExp(`\\{\\{this\\.${key}\\}\\}`, 'g');
          itemHtml = itemHtml.replace(regex, String(item[key] || ''));
        });

        // Process {{#if this.property}} conditionals
        const ifThisRegex = /\{\{#if\s+this\.(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g;
        itemHtml = itemHtml.replace(ifThisRegex, (_match: string, property: string, conditionalContent: string) => {
          const value = item[property];
          return value ? conditionalContent : '';
        });

        return itemHtml;
      }).join('');

      return renderedItems;
    });
  }

  private processAllIfConditionals(template: string, data: TemplateData): string {
    const ifRegex = /\{\{#if\s+([A-Z_][A-Z0-9_]*)\}\}([\s\S]*?)\{\{\/if\}\}/g;

    return template.replace(ifRegex, (_fullMatch, condition, content) => {
      const value = (data as any)[condition];
      return value ? content : '';
    });
  }

  private processAllVariables(template: string, data: TemplateData): string {
    const variableRegex = /\{\{([A-Z_][A-Z0-9_]*)\}\}/g;

    return template.replace(variableRegex, (_match, variable) => {
      const value = (data as any)[variable];
      return value !== undefined ? String(value) : '';
    });
  }
  

  
  private processCSSVariables(template: string, data: TemplateData): string {
    // Replace CSS custom properties
    let html = template;
    
    // Update Tailwind config colors
    const tailwindConfigRegex = /(tailwind\.config\s*=\s*\{[^}]*colors:\s*\{[^}]*primary:\s*)'[^']*'([^}]*secondary:\s*)'[^']*'/;
    html = html.replace(tailwindConfigRegex, `$1'${data.PRIMARY_COLOR}'$2'${data.SECONDARY_COLOR}'`);
    
    // Add template class to body
    html = html.replace(/<body([^>]*)class="([^"]*)"/, `<body$1class="$2 template-${data.TEMPLATE_TYPE}"`);
    
    // Add custom CSS if provided
    if (data.CUSTOM_CSS) {
      const customCSSSection = `\n        /* Custom CSS */\n        ${data.CUSTOM_CSS}\n    `;
      html = html.replace(/(\s*\/\* Custom CSS \*\/\s*)/g, customCSSSection);
    }
    
    return html;
  }
  
  private getFallbackTemplate(): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{PAGE_TITLE}}</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 400px; margin: 0 auto; }
        .error { color: red; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="error">
            <h1>Template Loading Error</h1>
            <p>Could not load the template file. Please check your connection.</p>
        </div>
    </div>
</body>
</html>`;
  }
}

export const convertBusinessDataToTemplateData = (businessData: BusinessData): TemplateData => {
  // Check if có social media nào không
  const hasSocial = !!(
    businessData.facebookUrl ||
    businessData.instagramUrl ||
    businessData.yelpUrl ||
    businessData.websiteUrl ||
    businessData.googleMapsUrl ||
    businessData.customSocialLinks.length > 0
  );

  // Helper function to generate header background style
  const getHeaderBackgroundStyle = () => {
    const value = businessData.headerBackgroundValue || '';

    switch (businessData.headerBackgroundType) {
      case 'color':
        return `background-color: ${value};`;
      case 'gradient':
        return `background: ${value};`;
      case 'image':
        // Escape quotes, backticks, and handle blob URLs safely for JavaScript
        const escapedUrl = value
          .replace(/\\/g, '\\\\')  // Escape backslashes
          .replace(/'/g, "\\'")    // Escape single quotes
          .replace(/"/g, '\\"')    // Escape double quotes
          .replace(/`/g, '\\`')    // Escape backticks
          .replace(/\n/g, '\\n')   // Escape newlines
          .replace(/\r/g, '\\r');  // Escape carriage returns
        return `background-image: url("${escapedUrl}"); background-size: cover; background-position: center; background-repeat: no-repeat;`;
      default:
        return `background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);`;
    }
  };

  return {
    // Basic Info
    PAGE_TITLE: businessData.pageTitle,
    BUSINESS_NAME: businessData.businessName,
    SLOGAN: businessData.slogan,
    HAS_LOGO: !!(businessData.logoUrl && businessData.logoUrl.trim()),
    HAS_NO_LOGO: !(businessData.logoUrl && businessData.logoUrl.trim()),
    LOGO_URL: businessData.logoUrl,
    FAVICON_URL: businessData.faviconUrl,

    // Header Customization
    HEADER_BACKGROUND_STYLE: getHeaderBackgroundStyle(),
    HEADER_TEXT_COLOR: businessData.headerTextColor || '#ffffff',
    
    // Contact
    PHONE: businessData.phone,
    PHONE_CLEAN: businessData.phone.replace(/\D/g, ''),
    EMAIL: businessData.email,
    ADDRESS: businessData.address,
    ADDRESS_HTML: businessData.address.replace(/\n/g, '<br>'),
    HOURS: businessData.hours,
    HOURS_HTML: businessData.hours.replace(/\n/g, '<br>'),
    
    // Social Media
    HAS_SOCIAL: hasSocial,
    FACEBOOK_URL: businessData.facebookUrl,
    INSTAGRAM_URL: businessData.instagramUrl,
    YELP_URL: businessData.yelpUrl,
    WEBSITE_URL: businessData.websiteUrl,
    GOOGLE_MAPS_URL: businessData.googleMapsUrl,
    BOOKING_URL: businessData.bookingUrl,
    CUSTOM_SOCIAL_LINKS: businessData.customSocialLinks.map(link => ({
      ...link,
      color: link.color || '#1976d2' // Default blue color
    })),
    
    // Template & Styling
    TEMPLATE_TYPE: businessData.template,
    PRIMARY_COLOR: businessData.primaryColor,
    SECONDARY_COLOR: businessData.secondaryColor,
    CUSTOM_CSS: businessData.customCSS,
    
    // Services & Gallery
    SERVICES: businessData.services,
    HAS_SERVICES: businessData.services.length > 0,
    GALLERY_IMAGES: businessData.galleryImages.map((url, index) => ({ url, index: index + 1 })),
    HAS_GALLERY: businessData.galleryImages.length > 0,
    OFFERS: businessData.offers,
    HAS_OFFERS: businessData.offers.length > 0
  };
};
